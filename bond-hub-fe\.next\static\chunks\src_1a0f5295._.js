(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/stores/conversationsStore.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/stores/conversationsStore.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/stores/authStore.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/stores/authStore.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/stores/chatStore.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/stores/chatStore.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/actions/auth.action.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_actions_85debbce._.js",
  "static/chunks/src_actions_auth_action_ts_960c93a2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/actions/auth.action.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/actions/group.action.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/actions/group.action.ts [app-client] (ecmascript)");
    });
});
}}),
}]);