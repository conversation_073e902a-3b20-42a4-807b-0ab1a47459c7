"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useChatStore } from "@/stores/chatStore";
import { useConversationsStore } from "@/stores/conversationsStore";
import { useAuthStore } from "@/stores/authStore";
import { useSocket } from "@/providers/SocketChatProvider";
import { toast } from "sonner";

/**
 * Component test để kiểm tra real-time messaging
 * Chỉ dùng để test, không dùng trong production
 */
export default function RealTimeMessageTest() {
  const [testMessage, setTestMessage] = useState("");
  const [groupId, setGroupId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  
  const { currentUser } = useAuthStore();
  const { messageSocket, isConnected } = useSocket();
  const { 
    messages, 
    selectedGroup, 
    currentChatType,
    sendMessage 
  } = useChatStore();
  const { conversations } = useConversationsStore();

  // Monitor socket events
  useEffect(() => {
    if (!messageSocket) return;

    const handleNewMessage = (data: any) => {
      console.log("[RealTimeMessageTest] Received newMessage event:", data);
    };

    const handleConnect = () => {
      console.log("[RealTimeMessageTest] Socket connected");
    };

    const handleDisconnect = () => {
      console.log("[RealTimeMessageTest] Socket disconnected");
    };

    messageSocket.on("newMessage", handleNewMessage);
    messageSocket.on("connect", handleConnect);
    messageSocket.on("disconnect", handleDisconnect);

    return () => {
      messageSocket.off("newMessage", handleNewMessage);
      messageSocket.off("connect", handleConnect);
      messageSocket.off("disconnect", handleDisconnect);
    };
  }, [messageSocket]);

  const handleSendTestMessage = async () => {
    if (!testMessage.trim()) {
      toast.error("Vui lòng nhập tin nhắn test");
      return;
    }

    if (!groupId.trim()) {
      toast.error("Vui lòng nhập Group ID");
      return;
    }

    if (!currentUser?.id) {
      toast.error("Bạn cần đăng nhập để gửi tin nhắn");
      return;
    }

    if (!isConnected) {
      toast.error("Socket chưa kết nối");
      return;
    }

    setIsLoading(true);
    try {
      console.log("[RealTimeMessageTest] Sending test message:", {
        content: testMessage,
        groupId,
        senderId: currentUser.id,
        socketConnected: isConnected,
      });

      // Send message using chatStore
      const result = await sendMessage({
        content: { text: testMessage },
        receiverId: null,
        groupId: groupId,
        messageType: "GROUP" as any,
      });

      if (result.success) {
        toast.success("Tin nhắn test đã được gửi!");
        setTestMessage("");
      } else {
        toast.error(result.error || "Không thể gửi tin nhắn test");
      }
    } catch (error) {
      console.error("[RealTimeMessageTest] Error:", error);
      toast.error("Đã xảy ra lỗi khi gửi tin nhắn test");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDirectSocketSend = () => {
    if (!messageSocket || !isConnected) {
      toast.error("Socket chưa kết nối");
      return;
    }

    if (!testMessage.trim() || !groupId.trim()) {
      toast.error("Vui lòng nhập tin nhắn và Group ID");
      return;
    }

    console.log("[RealTimeMessageTest] Sending direct socket message");
    
    messageSocket.emit("sendMessage", {
      content: { text: testMessage },
      groupId: groupId,
      messageType: "GROUP",
    });

    toast.success("Tin nhắn đã được gửi qua socket trực tiếp!");
  };

  return (
    <div className="p-6 max-w-2xl mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Real-Time Message Test</h2>
      
      <div className="space-y-4">
        {/* Socket Status */}
        <div className="p-3 bg-gray-50 rounded">
          <h3 className="font-medium mb-2">Socket Status:</h3>
          <p className={`text-sm ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
            {isConnected ? '✅ Connected' : '❌ Disconnected'}
          </p>
          <p className="text-xs text-gray-500">
            Socket ID: {messageSocket?.id || 'N/A'}
          </p>
        </div>

        {/* Current Chat Info */}
        <div className="p-3 bg-gray-50 rounded">
          <h3 className="font-medium mb-2">Current Chat:</h3>
          <p className="text-sm">Type: {currentChatType || 'None'}</p>
          <p className="text-sm">Selected Group: {selectedGroup?.id || 'None'}</p>
          <p className="text-sm">Messages Count: {messages.length}</p>
        </div>

        {/* Test Controls */}
        <div>
          <label className="block text-sm font-medium mb-1">
            Group ID:
          </label>
          <Input
            value={groupId}
            onChange={(e) => setGroupId(e.target.value)}
            placeholder="Nhập Group ID để test..."
            disabled={isLoading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Test Message:
          </label>
          <Input
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder="Nhập tin nhắn test..."
            disabled={isLoading}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !isLoading) {
                handleSendTestMessage();
              }
            }}
          />
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleSendTestMessage}
            disabled={isLoading || !testMessage.trim() || !groupId.trim()}
            className="flex-1"
          >
            {isLoading ? (
              <>
                <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Đang gửi...
              </>
            ) : (
              "Gửi qua ChatStore"
            )}
          </Button>

          <Button
            onClick={handleDirectSocketSend}
            disabled={!isConnected || !testMessage.trim() || !groupId.trim()}
            variant="outline"
            className="flex-1"
          >
            Gửi trực tiếp Socket
          </Button>
        </div>

        {/* Current User Info */}
        <div className="p-3 bg-gray-50 rounded">
          <h3 className="font-medium mb-2">Current User:</h3>
          <p className="text-sm">ID: {currentUser?.id || 'Not logged in'}</p>
          <p className="text-sm">Email: {currentUser?.email || 'N/A'}</p>
        </div>

        {/* Recent Messages */}
        <div className="p-3 bg-gray-50 rounded">
          <h3 className="font-medium mb-2">Recent Messages ({messages.length}):</h3>
          <div className="max-h-40 overflow-y-auto space-y-1">
            {messages.slice(-5).map((msg, index) => (
              <div key={msg.id || index} className="text-xs p-2 bg-white rounded">
                <p><strong>ID:</strong> {msg.id}</p>
                <p><strong>From:</strong> {msg.senderId}</p>
                <p><strong>Content:</strong> {msg.content.text}</p>
                <p><strong>Time:</strong> {new Date(msg.createdAt).toLocaleTimeString()}</p>
              </div>
            ))}
            {messages.length === 0 && (
              <p className="text-xs text-gray-500">Chưa có tin nhắn nào</p>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="p-3 bg-blue-50 rounded">
          <h3 className="font-medium mb-2">Hướng dẫn test:</h3>
          <ol className="text-sm text-gray-600 space-y-1">
            <li>1. Đảm bảo socket đã connected (màu xanh)</li>
            <li>2. Nhập Group ID của group đang test</li>
            <li>3. Mở chat group đó trong tab khác</li>
            <li>4. Gửi tin nhắn test và kiểm tra real-time</li>
            <li>5. Xem console logs để debug</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
