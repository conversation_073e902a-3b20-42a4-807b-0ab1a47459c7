@startuml Tham gia nhóm bằng QR code - Activity Diagram
title Tham gia nhóm bằng QR code - Activity Diagram

|User|
start
:Nhận mã QR chứa ID nhóm;
:Quét mã QR;
:<PERSON><PERSON><PERSON><PERSON> xuất ID nhóm từ mã QR (dạng group-groupId);
:<PERSON>em thông tin nhóm;
:<PERSON>ọn tham gia nhóm;
:<PERSON><PERSON><PERSON> yêu cầu tham gia nhóm;

|System|
:Lấy ID nhóm từ yêu cầu;
:Kiểm tra nhóm có tồn tại không;

if (Nhóm tồn tại?) then (Có)
  :Kiểm tra người dùng đã là thành viên chưa;

  if (<PERSON>ã là thành viên?) then (Có)
    :Tr<PERSON> về lỗi "Bạn đã là thành viên của nhóm này";
  else (Không)
    :Thêm người dùng vào nhóm với vai trò MEMBER;
    :<PERSON><PERSON><PERSON> thông tin vào cơ sở dữ liệu;
    :<PERSON><PERSON><PERSON> thông báo cho các thành viên khác qua WebSocket;
    :Trả về thông tin thành viên mới;
  endif
else (Không)
  :Trả về lỗi "Nhóm không tồn tại";
endif

|User|
if (Tham gia nhóm thành công?) then (Có)
  :Xem thông báo tham gia nhóm thành công;
  :Chuyển đến màn hình nhóm;
else (Không)
  :Hiển thị thông báo lỗi;
endif

stop
@enduml
