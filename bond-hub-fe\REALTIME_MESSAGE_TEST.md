# Real-time Message Testing Guide

## Vấn đề hiện tại
Khi người dùng A gửi tin nhắn, người dùng B có chuông thông báo nhưng tin nhắn không hiển thị real-time trong chat đang mở. Chỉ khi chuyển sang chat khác rồi quay lại thì mới thấy tin nhắn mới.

## Các thay đổi đã thực hiện

### 1. <PERSON><PERSON><PERSON> thiện ChatSocketHandler.tsx
- **Sửa logic kiểm tra `isFromCurrentChat`**: Sử dụng current state thay vì stale state từ useCallback dependencies
- **Thêm force re-render mechanism**: Đảm bảo UI được cập nhật ngay lập tức khi tin nhắn mới đến
- **Cải thiện logging**: Thêm nhiều log để debug quá trình xử lý tin nhắn

### 2. Thêm Debug Component
- **RealTimeMessageDebug.tsx**: Component hiển thị thông tin debug real-time
- **Theo dõi socket events**: Monitor các sự kiện socket và state changes
- **Hiển thị trong development mode**: Chỉ hiển thị khi NODE_ENV === "development"

### 3. Cải thiện ChatArea.tsx
- **Thêm debug logging**: Track khi messages array thay đổi
- **Cải thiện re-render detection**: Đảm bảo component re-render khi có tin nhắn mới

## Cách test

### Bước 1: Mở 2 browser/tab khác nhau
1. **Edge**: Đăng nhập bằng tài khoản Tâm Như
2. **Chrome**: Đăng nhập bằng tài khoản Hồ Thị Như Tâm

### Bước 2: Mở cùng một cuộc trò chuyện
1. Cả 2 browser đều mở cuộc trò chuyện "hài code"
2. Đảm bảo cả 2 đều đang ở trong chat đó

### Bước 3: Test gửi tin nhắn
1. **Từ Edge (Tâm Như)**: Gửi một tin nhắn
2. **Quan sát Chrome (HTNT)**: 
   - Kiểm tra xem tin nhắn có hiển thị ngay lập tức không
   - Kiểm tra debug panel (góc dưới bên phải) để xem events

### Bước 4: Kiểm tra debug information
Trong development mode, sẽ có một panel debug ở góc dưới bên phải hiển thị:
- **Current Chat**: Loại chat hiện tại (USER/GROUP)
- **Contact/Group ID**: ID của cuộc trò chuyện
- **Messages Count**: Số lượng tin nhắn hiện tại
- **Socket Connected**: Trạng thái kết nối socket
- **Recent Events**: Các sự kiện gần đây (SOCKET_NEW_MESSAGE, MESSAGES_CHANGED)

## Các log cần kiểm tra trong Console

### 1. Khi tin nhắn được nhận qua socket:
```
[ChatSocketHandler] New message received: {message data}
[ChatSocketHandler] Processing message: {message details}
[ChatSocketHandler] Message analysis: {isFromCurrentChat, etc}
[ChatSocketHandler] Adding message to current chat: {message info}
```

### 2. Khi messages array thay đổi:
```
[ChatArea] RENDER TRIGGERED - Messages array changed: {details}
[ChatArea] Last message: {message info}
[ChatArea] Messages updated for {chatType}: {count}
```

### 3. Khi chatStore xử lý tin nhắn:
```
[chatStore] Processing new message {messageId}
[chatStore] Adding message to chat messages array: {details}
[chatStore] Message added successfully, new count: {count}
```

## Troubleshooting

### Nếu tin nhắn vẫn không hiển thị real-time:

1. **Kiểm tra socket connection**:
   - Xem debug panel có hiển thị "Socket Connected: Yes" không
   - Kiểm tra console có lỗi socket không

2. **Kiểm tra message processing**:
   - Xem có log "[ChatSocketHandler] New message received" không
   - Kiểm tra `isFromCurrentChat` có đúng `true` không

3. **Kiểm tra state updates**:
   - Xem có log "[ChatArea] RENDER TRIGGERED" không
   - Kiểm tra messages count có tăng không

4. **Force refresh test**:
   - Thử refresh trang và test lại
   - Kiểm tra xem có cache issues không

## Kết quả mong đợi

Sau khi áp dụng các thay đổi này:
1. Tin nhắn sẽ hiển thị ngay lập tức khi nhận được qua socket
2. Không cần phải chuyển chat khác rồi quay lại
3. Debug panel sẽ hiển thị các events real-time
4. Console logs sẽ cho thấy quá trình xử lý tin nhắn diễn ra đúng cách

## Lưu ý

- Debug component chỉ hiển thị trong development mode
- Có thể tắt debug component bằng cách comment dòng trong ChatArea.tsx
- Nếu vấn đề vẫn tồn tại, cần kiểm tra thêm backend socket implementation
