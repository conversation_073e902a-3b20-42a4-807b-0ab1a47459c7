# Socket Fixes for Group Creation and Messaging

## Vấn đề ban đầu
**Tin nhắn chat vào group không hiện ở những thành viên khác đối với các group mới tạo**

## Root Cause Analysis
1. **Frontend chỉ connect đến `/groups` namespace** nhưng **messages được gửi qua `/messages` namespace**
2. **Frontend không join group rooms trong message gateway**
3. **Backend message gateway gửi `updateGroupList` events** nhưng **frontend không lắng nghe**
4. **Mobile app có logic đúng**: reconnect message socket sau khi tạo group

## Backend Flow (đã đúng)
```
1. Tạo group → group.service.ts
2. Gửi memberAdded qua GroupGateway → group.gateway.ts  
3. Gửi event qua EventService → event.service.ts
4. MessageGateway nhận event → message.gateway.ts
5. MessageGateway join user vào group rooms
6. MessageGateway gửi updateGroupList event
```

## Frontend Fixes Applied

### 1. Reconnect message socket sau khi tạo group
**File: `src/actions/group.action.ts`**
```typescript
// Reconnect message socket to ensure user joins group rooms for messaging
if (typeof window !== "undefined" && window.messageSocket) {
  console.log("[group.action] Reconnecting message socket after group creation");
  window.messageSocket.disconnect();
  window.messageSocket.connect();
}
```

### 2. Lắng nghe updateGroupList events từ message gateway
**File: `src/providers/SocketChatProvider.tsx`**
```typescript
// Listen for updateGroupList events from message gateway
socket.on("updateGroupList", (data) => {
  if (data.action === 'added_to_group' && currentUser?.id) {
    // Backend has already joined us to the group room
    // Trigger conversations reload to show new group
    if (typeof window !== "undefined" && window.triggerConversationsReload) {
      window.triggerConversationsReload();
    }
  }
});
```

### 3. Setup global trigger function
**File: `src/components/chat/ConverstationList.tsx`**
```typescript
// Setup global trigger function for socket events
useEffect(() => {
  if (typeof window !== "undefined") {
    window.triggerConversationsReload = refreshConversations;
  }
  return () => {
    if (typeof window !== "undefined") {
      window.triggerConversationsReload = undefined;
    }
  };
}, [currentUser?.id]);
```

### 4. Đơn giản hóa logic tạo group
**File: `src/actions/group.action.ts`**
- Loại bỏ retry logic phức tạp
- Chỉ join group room một lần
- Để backend xử lý việc gửi events

### 5. Cập nhật CreateGroupDialog
**File: `src/components/group/CreateGroupDialog.tsx`**
- Loại bỏ logic socket phức tạp
- Chỉ reload conversations sau khi tạo thành công

## Flow sau khi fix

### Khi tạo group:
1. **Frontend**: Gọi API tạo group
2. **Frontend**: Join group room trong group socket
3. **Frontend**: Reconnect message socket
4. **Backend**: Tự động join user vào group rooms trong message gateway
5. **Backend**: Gửi updateGroupList events
6. **Frontend**: Nhận updateGroupList và reload conversations
7. **Result**: Tất cả thành viên có thể nhắn tin và nhận tin nhắn

### Khi nhắn tin trong group:
1. **Frontend**: Gửi tin nhắn qua message socket
2. **Backend**: Broadcast tin nhắn đến tất cả thành viên trong group room
3. **Frontend**: Tất cả thành viên nhận tin nhắn real-time

## Files đã thay đổi
- ✅ `src/actions/group.action.ts` - Reconnect message socket
- ✅ `src/providers/SocketChatProvider.tsx` - Listen updateGroupList events
- ✅ `src/components/chat/ConverstationList.tsx` - Setup global trigger
- ✅ `src/components/group/CreateGroupDialog.tsx` - Đơn giản hóa logic
- ✅ `src/components/group/GroupSocketHandler.tsx` - Loại bỏ custom events
- ✅ `src/hooks/useGroupSocket.ts` - Đơn giản hóa logic

## Test Scenario
1. **User A tạo group với User B và User C**
2. **Kiểm tra**: Tất cả users thấy group trong conversation list
3. **User A gửi tin nhắn trong group**
4. **Kiểm tra**: User B và User C nhận được tin nhắn real-time
5. **User B reply tin nhắn**
6. **Kiểm tra**: User A và User C nhận được tin nhắn real-time

## Kết quả mong đợi
- ✅ Group creation hoạt động đúng
- ✅ Tất cả thành viên join group rooms trong message gateway
- ✅ Tin nhắn group hiển thị real-time cho tất cả thành viên
- ✅ Giảm lag và cải thiện performance
- ✅ Code đơn giản hơn, dễ maintain
