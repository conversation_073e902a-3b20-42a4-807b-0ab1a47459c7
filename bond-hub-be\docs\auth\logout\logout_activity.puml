@startuml Đăng xuất - Activity Diagram
title Đăng xuất - Activity Diagram

|User|
start
:Ch<PERSON>n đăng xuất;
:G<PERSON>i yêu cầu đăng xuất kèm refresh token;

|System|
:Ki<PERSON><PERSON> tra refresh token;

if (Refresh token hợp lệ?) then (Có)
  :Tì<PERSON> kiếm phiên đăng nhập;
  
  if (Phiên đăng nhập tồn tại?) then (Có)
    :<PERSON><PERSON><PERSON> dấu refresh token đã bị thu hồi;
    :Thô<PERSON> báo cho thiết bị về việc đăng xuất (qua WebSocket);
    :<PERSON><PERSON><PERSON> về thông báo đăng xuất thành công;
  else (Không)
    :<PERSON><PERSON><PERSON> về lỗi "Phiên đăng nhập không tồn tại";
  endif
else (Không)
  :Tr<PERSON> về lỗi "Refresh token không hợp lệ";
endif

|User|
if (<PERSON>ăng xuất thành công?) then (Có)
  :Xóa tokens khỏi thiết bị;
  :<PERSON>yển đến màn hình đăng nhập;
else (Không)
  :<PERSON><PERSON><PERSON> thị thông báo lỗi;
endif

stop
@enduml
