module.exports = {

"[project]/src/utils/dateUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Helper function to format time
__turbopack_context__.s({
    "formatLastActivity": (()=>formatLastActivity),
    "formatMessageDate": (()=>formatMessageDate),
    "formatMessageTime": (()=>formatMessageTime)
});
const formatMessageTime = (dateInput)=>{
    // If no date provided, return empty string
    if (!dateInput) return "";
    // Ensure we have a proper Date object
    let date;
    try {
        if (dateInput instanceof Date) {
            date = dateInput;
        } else if (typeof dateInput === "number") {
            date = new Date(dateInput);
        } else if (typeof dateInput === "string") {
            // Handle ISO string or timestamp string
            date = new Date(dateInput);
        } else {
            console.error("Invalid date input type:", typeof dateInput);
            return "";
        }
        // Check if date is valid
        if (isNaN(date.getTime())) {
            console.error("Invalid date:", dateInput);
            return "";
        }
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        // Less than a day
        if (diff < 86400000) {
            return date.toLocaleTimeString("vi-VN", {
                hour: "2-digit",
                minute: "2-digit"
            });
        }
        // Less than a week
        if (diff < 604800000) {
            const days = [
                "CN",
                "T2",
                "T3",
                "T4",
                "T5",
                "T6",
                "T7"
            ];
            return days[date.getDay()];
        }
        // More than a week
        return date.toLocaleDateString("vi-VN", {
            day: "2-digit",
            month: "2-digit"
        });
    } catch (error) {
        console.error("Error formatting message time:", error);
        return "";
    }
};
const formatMessageDate = (dateInput)=>{
    // If no date provided, return empty string
    if (!dateInput) return "";
    // Ensure we have a proper Date object
    let date;
    try {
        if (dateInput instanceof Date) {
            date = dateInput;
        } else if (typeof dateInput === "number") {
            date = new Date(dateInput);
        } else if (typeof dateInput === "string") {
            // Handle ISO string or timestamp string
            date = new Date(dateInput);
        } else {
            console.error("Invalid date input type:", typeof dateInput);
            return "";
        }
        // Check if date is valid
        if (isNaN(date.getTime())) {
            console.error("Invalid date:", dateInput);
            return "";
        }
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        if (messageDate.getTime() === today.getTime()) {
            return "Hôm nay";
        } else if (messageDate.getTime() === yesterday.getTime()) {
            return "Hôm qua";
        } else {
            return date.toLocaleDateString("vi-VN", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric"
            });
        }
    } catch (error) {
        console.error("Error formatting message date:", error);
        return "";
    }
};
const formatLastActivity = (dateInput)=>{
    // If no date provided, return appropriate message
    if (!dateInput) return "Không có thông tin";
    // Ensure we have a proper Date object
    let date;
    try {
        if (dateInput instanceof Date) {
            date = dateInput;
        } else if (typeof dateInput === "number") {
            date = new Date(dateInput);
        } else if (typeof dateInput === "string") {
            // Handle ISO string or timestamp string
            date = new Date(dateInput);
        } else {
            console.error("Invalid date input type:", typeof dateInput);
            return "Không có thông tin";
        }
        // Check if date is valid
        if (isNaN(date.getTime())) {
            console.error("Invalid date:", dateInput);
            return "Không có thông tin";
        }
        const now = new Date();
        const diffInMs = now.getTime() - date.getTime();
        const diffInSeconds = Math.floor(diffInMs / 1000);
        const diffInMinutes = Math.floor(diffInSeconds / 60);
        const diffInHours = Math.floor(diffInMinutes / 60);
        const diffInDays = Math.floor(diffInHours / 24);
        // Format based on how long ago the activity was
        if (diffInSeconds < 60) {
            return "Vừa mới truy cập";
        } else if (diffInMinutes < 60) {
            return `${diffInMinutes} phút trước`;
        } else if (diffInHours < 24) {
            return `${diffInHours} giờ trước`;
        } else if (diffInDays < 7) {
            return `${diffInDays} ngày trước`;
        } else {
            // For older dates, show the full date
            return date.toLocaleDateString("vi-VN", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
                hour: "2-digit",
                minute: "2-digit"
            });
        }
    } catch (error) {
        console.error("Error formatting last activity:", error);
        return "Không có thông tin";
    }
};
}}),
"[project]/src/utils/reactionUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getReactionCount": (()=>getReactionCount),
    "getReactionLabels": (()=>getReactionLabels),
    "getReactionTypeFromObject": (()=>getReactionTypeFromObject),
    "getReactionUnifiedCodes": (()=>getReactionUnifiedCodes),
    "processReactions": (()=>processReactions),
    "renderCenteredEmoji": (()=>renderCenteredEmoji)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/base.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$emoji$2d$picker$2d$react$2f$dist$2f$emoji$2d$picker$2d$react$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/emoji-picker-react/dist/emoji-picker-react.esm.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
const getReactionUnifiedCodes = ()=>({
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].LIKE]: "1f44d",
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].LOVE]: "2764-fe0f",
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].HAHA]: "1f602",
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].WOW]: "1f62e",
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].SAD]: "1f622",
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].ANGRY]: "1f621"
    });
const getReactionLabels = ()=>({
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].LIKE]: "Thích",
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].LOVE]: "Yêu thích",
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].HAHA]: "Haha",
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].WOW]: "Wow",
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].SAD]: "Buồn",
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].ANGRY]: "Phẫn nộ"
    });
const getReactionTypeFromObject = (reaction)=>{
    // Check if the reaction object has a reactionType property
    if ("reactionType" in reaction && reaction.reactionType) {
        return reaction.reactionType;
    } else if ("reaction" in reaction && typeof reaction.reaction === "string") {
        return reaction.reaction;
    }
    // Default to LIKE if neither property is found
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactionType"].LIKE;
};
const getReactionCount = (reaction)=>{
    // Check if the reaction has a count property
    if ("count" in reaction && typeof reaction.count === "number") {
        return reaction.count;
    }
    return 1; // Default count
};
const processReactions = (reactions)=>{
    const reactionCounts = {};
    let totalReactions = 0;
    // Process each reaction to handle different API response formats
    reactions.forEach((reaction)=>{
        const reactionType = getReactionTypeFromObject(reaction);
        const count = getReactionCount(reaction);
        reactionCounts[reactionType] = (reactionCounts[reactionType] || 0) + count;
        totalReactions += count;
    });
    return {
        reactionCounts,
        totalReactions
    };
};
const renderCenteredEmoji = (unified, size, emojiStyle)=>{
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "flex items-center justify-center w-full h-full"
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "flex items-center justify-center",
        style: {
            lineHeight: 0
        }
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$emoji$2d$picker$2d$react$2f$dist$2f$emoji$2d$picker$2d$react$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Emoji"], {
        unified,
        size,
        emojiStyle: emojiStyle
    })));
};
}}),
"[project]/src/utils/link-utils.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getLinkIcon": (()=>getLinkIcon),
    "getLinkTitle": (()=>getLinkTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-ssr] (ecmascript) <export default as ExternalLink>");
;
;
function getLinkIcon(domain) {
    // Kiểm tra domain và trả về icon tương ứng
    if (domain.includes("instagram.com")) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-gradient-to-tr from-yellow-500 via-pink-500 to-purple-500 w-full h-full flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                viewBox: "0 0 24 24",
                width: "24",
                height: "24",
                fill: "white",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"
                }, void 0, false, {
                    fileName: "[project]/src/utils/link-utils.tsx",
                    lineNumber: 15,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/utils/link-utils.tsx",
                lineNumber: 14,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/utils/link-utils.tsx",
            lineNumber: 13,
            columnNumber: 7
        }, this);
    } else if (domain.includes("tiktok.com")) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-black w-full h-full flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                viewBox: "0 0 24 24",
                width: "24",
                height: "24",
                fill: "white",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"
                }, void 0, false, {
                    fileName: "[project]/src/utils/link-utils.tsx",
                    lineNumber: 23,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/utils/link-utils.tsx",
                lineNumber: 22,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/utils/link-utils.tsx",
            lineNumber: 21,
            columnNumber: 7
        }, this);
    } else if (domain.includes("facebook.com")) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-blue-600 w-full h-full flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                viewBox: "0 0 24 24",
                width: "24",
                height: "24",
                fill: "white",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                }, void 0, false, {
                    fileName: "[project]/src/utils/link-utils.tsx",
                    lineNumber: 31,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/utils/link-utils.tsx",
                lineNumber: 30,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/utils/link-utils.tsx",
            lineNumber: 29,
            columnNumber: 7
        }, this);
    } else if (domain.includes("youtube.com") || domain.includes("youtu.be")) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-red-600 w-full h-full flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                viewBox: "0 0 24 24",
                width: "24",
                height: "24",
                fill: "white",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
                }, void 0, false, {
                    fileName: "[project]/src/utils/link-utils.tsx",
                    lineNumber: 39,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/utils/link-utils.tsx",
                lineNumber: 38,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/utils/link-utils.tsx",
            lineNumber: 37,
            columnNumber: 7
        }, this);
    } else if (domain.includes("lazada.vn")) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-blue-500 w-full h-full flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-white font-bold text-lg",
                children: "L"
            }, void 0, false, {
                fileName: "[project]/src/utils/link-utils.tsx",
                lineNumber: 46,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/utils/link-utils.tsx",
            lineNumber: 45,
            columnNumber: 7
        }, this);
    } else if (domain.includes("shopee.vn")) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-orange-500 w-full h-full flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-white font-bold text-lg",
                children: "S"
            }, void 0, false, {
                fileName: "[project]/src/utils/link-utils.tsx",
                lineNumber: 52,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/utils/link-utils.tsx",
            lineNumber: 51,
            columnNumber: 7
        }, this);
    } else if (domain.includes("tiki.vn")) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-blue-400 w-full h-full flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-white font-bold text-lg",
                children: "T"
            }, void 0, false, {
                fileName: "[project]/src/utils/link-utils.tsx",
                lineNumber: 58,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/utils/link-utils.tsx",
            lineNumber: 57,
            columnNumber: 7
        }, this);
    } else if (domain.includes("bataptracnghiem.com")) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-yellow-400 w-full h-full flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-white font-bold text-lg",
                children: "B"
            }, void 0, false, {
                fileName: "[project]/src/utils/link-utils.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/utils/link-utils.tsx",
            lineNumber: 63,
            columnNumber: 7
        }, this);
    } else if (domain.includes("azota.vn")) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-blue-700 w-full h-full flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-white font-bold text-lg",
                children: "A"
            }, void 0, false, {
                fileName: "[project]/src/utils/link-utils.tsx",
                lineNumber: 70,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/utils/link-utils.tsx",
            lineNumber: 69,
            columnNumber: 7
        }, this);
    } else {
        // Default icon for other domains
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLink$3e$__["ExternalLink"], {
            className: "h-5 w-5 text-gray-500"
        }, void 0, false, {
            fileName: "[project]/src/utils/link-utils.tsx",
            lineNumber: 75,
            columnNumber: 12
        }, this);
    }
}
function getLinkTitle(domain, defaultTitle) {
    // Kiểm tra domain và trả về tiêu đề tương ứng
    if (domain.includes("instagram.com")) {
        return "Instagram";
    } else if (domain.includes("tiktok.com")) {
        return "TikTok Video";
    } else if (domain.includes("facebook.com")) {
        return "Facebook Link";
    } else if (domain.includes("youtube.com") || domain.includes("youtu.be")) {
        return "YouTube Video";
    } else if (domain.includes("lazada.vn")) {
        return "Lazada Product";
    } else if (domain.includes("shopee.vn")) {
        return "Shopee Product";
    } else if (domain.includes("tiki.vn")) {
        return "Tiki Product";
    } else if (domain.includes("bataptracnghiem.com")) {
        return "Thi thử trắc nghiệm";
    } else if (domain.includes("azota.vn")) {
        return "Ôn tập TTHCM";
    } else {
        // Trả về tiêu đề mặc định cho các domain khác
        return defaultTitle;
    }
}
}}),
"[project]/src/hooks/useDebounce.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDebounce": (()=>useDebounce)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function useDebounce(value, delay) {
    const [debouncedValue, setDebouncedValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(value);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Update debounced value after delay
        const handler = setTimeout(()=>{
            setDebouncedValue(value);
        }, delay);
        // Cancel the timeout if value changes or component unmounts
        return ()=>{
            clearTimeout(handler);
        };
    }, [
        value,
        delay
    ]);
    return debouncedValue;
}
}}),
"[project]/src/actions/data:ebbc3b [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"709896cab67e73440c64e57976dd41482962bc1661":"summarizeText"},"src/actions/ai.action.ts",""] */ __turbopack_context__.s({
    "summarizeText": (()=>summarizeText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var summarizeText = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("709896cab67e73440c64e57976dd41482962bc1661", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "summarizeText"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/actions/data:8d11e6 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"60d103cf4df46d09cb2adf6a2a992510519b80a2d8":"enhanceMessage"},"src/actions/ai.action.ts",""] */ __turbopack_context__.s({
    "enhanceMessage": (()=>enhanceMessage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var enhanceMessage = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("60d103cf4df46d09cb2adf6a2a992510519b80a2d8", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "enhanceMessage"); //# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4vYWkuYWN0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHNlcnZlclwiO1xyXG5cclxuaW1wb3J0IHsgY3JlYXRlQXhpb3NJbnN0YW5jZSB9IGZyb20gXCJAL2xpYi9heGlvc1wiO1xyXG5cclxuLy8gRGVmaW5lIGludGVyZmFjZXMgZm9yIEFJIGVuaGFuY2UgZmVhdHVyZVxyXG5pbnRlcmZhY2UgUHJldmlvdXNNZXNzYWdlIHtcclxuICBjb250ZW50OiBzdHJpbmc7XHJcbiAgdHlwZTogc3RyaW5nO1xyXG4gIHNlbmRlcklkOiBzdHJpbmc7XHJcbiAgc2VuZGVyTmFtZTogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgRW5oYW5jZU1lc3NhZ2VSZXNwb25zZSB7XHJcbiAgZW5oYW5jZWRNZXNzYWdlOiBzdHJpbmc7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFbmhhbmNlIGEgbWVzc2FnZSB1c2luZyBBSVxyXG4gKiBAcGFyYW0gbWVzc2FnZSBUaGUgbWVzc2FnZSB0byBlbmhhbmNlXHJcbiAqIEBwYXJhbSBwcmV2aW91c01lc3NhZ2VzIE9wdGlvbmFsIGFycmF5IG9mIHByZXZpb3VzIG1lc3NhZ2VzIGZvciBjb250ZXh0XHJcbiAqIEByZXR1cm5zIEVuaGFuY2VkIG1lc3NhZ2VcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBlbmhhbmNlTWVzc2FnZShcclxuICBtZXNzYWdlOiBzdHJpbmcsXHJcbiAgcHJldmlvdXNNZXNzYWdlcz86IFByZXZpb3VzTWVzc2FnZVtdLFxyXG4pIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgYXhpb3NJbnN0YW5jZSA9IGNyZWF0ZUF4aW9zSW5zdGFuY2UoKTtcclxuICAgIGNvbnNvbGUubG9nKFwiYXhpb3NJbnN0YW5jZVwiLCBheGlvc0luc3RhbmNlLmRlZmF1bHRzLmJhc2VVUkwpO1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvc0luc3RhbmNlLnBvc3QoXCIvYWkvZW5oYW5jZVwiLCB7XHJcbiAgICAgIG1lc3NhZ2UsXHJcbiAgICAgIHByZXZpb3VzTWVzc2FnZXMsXHJcbiAgICB9KTtcclxuXHJcbiAgICBjb25zdCByZXN1bHQgPSByZXNwb25zZS5kYXRhIGFzIEVuaGFuY2VNZXNzYWdlUmVzcG9uc2U7XHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBlbmhhbmNlZE1lc3NhZ2U6IHJlc3VsdC5lbmhhbmNlZE1lc3NhZ2UgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkFJIG1lc3NhZ2UgZW5oYW5jZW1lbnQgZmFpbGVkOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJVbmtub3duIGVycm9yXCIsXHJcbiAgICB9O1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEdlbmVyYXRlIEFJIHJlc3BvbnNlIGJhc2VkIG9uIGEgcHJvbXB0XHJcbiAqIEBwYXJhbSBwcm9tcHQgVGhlIHByb21wdCBmb3IgQUkgZ2VuZXJhdGlvblxyXG4gKiBAcmV0dXJucyBHZW5lcmF0ZWQgQUkgcmVzcG9uc2VcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZUFJUmVzcG9uc2UocHJvbXB0OiBzdHJpbmcpIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgYXhpb3NJbnN0YW5jZSA9IGNyZWF0ZUF4aW9zSW5zdGFuY2UoKTtcclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zSW5zdGFuY2UucG9zdChcIi9haS9nZW5lcmF0ZVwiLCB7XHJcbiAgICAgIHByb21wdCxcclxuICAgIH0pO1xyXG5cclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIHJlc3BvbnNlOiByZXNwb25zZS5kYXRhLnJlc3BvbnNlIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJBSSByZXNwb25zZSBnZW5lcmF0aW9uIGZhaWxlZDpcIiwgZXJyb3IpO1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiVW5rbm93biBlcnJvclwiLFxyXG4gICAgfTtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBTdW1tYXJpemUgdGV4dCB1c2luZyBBSVxyXG4gKiBAcGFyYW0gdGV4dCBUaGUgdGV4dCB0byBzdW1tYXJpemVcclxuICogQHBhcmFtIG1heExlbmd0aCBPcHRpb25hbCBtYXhpbXVtIGxlbmd0aCBmb3IgdGhlIHN1bW1hcnlcclxuICogQHBhcmFtIHByZXZpb3VzTWVzc2FnZXMgT3B0aW9uYWwgYXJyYXkgb2YgcHJldmlvdXMgbWVzc2FnZXMgZm9yIGNvbnRleHRcclxuICogQHJldHVybnMgU3VtbWFyaXplZCB0ZXh0XHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc3VtbWFyaXplVGV4dChcclxuICB0ZXh0OiBzdHJpbmcsXHJcbiAgbWF4TGVuZ3RoPzogbnVtYmVyLFxyXG4gIHByZXZpb3VzTWVzc2FnZXM/OiBQcmV2aW91c01lc3NhZ2VbXSxcclxuKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGF4aW9zSW5zdGFuY2UgPSBjcmVhdGVBeGlvc0luc3RhbmNlKCk7XHJcblxyXG4gICAgY29uc3QgcGF5bG9hZDogYW55ID0geyB0ZXh0IH07XHJcblxyXG4gICAgaWYgKG1heExlbmd0aCkge1xyXG4gICAgICBwYXlsb2FkLm1heExlbmd0aCA9IG1heExlbmd0aC50b1N0cmluZygpO1xyXG4gICAgfVxyXG5cclxuICAgIGlmIChwcmV2aW91c01lc3NhZ2VzKSB7XHJcbiAgICAgIHBheWxvYWQucHJldmlvdXNNZXNzYWdlcyA9IHByZXZpb3VzTWVzc2FnZXM7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvc0luc3RhbmNlLnBvc3QoXCIvYWkvc3VtbWFyaXplXCIsIHBheWxvYWQpO1xyXG5cclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIHN1bW1hcnk6IHJlc3BvbnNlLmRhdGEuc3VtbWFyeSB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiQUkgdGV4dCBzdW1tYXJpemF0aW9uIGZhaWxlZDpcIiwgZXJyb3IpO1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiVW5rbm93biBlcnJvclwiLFxyXG4gICAgfTtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBTZW5kIGEgZnJlZXN0eWxlIHByb21wdCB0byBBSSB3aXRoIG9wdGlvbmFsIHN5c3RlbSBwcm9tcHRcclxuICogQHBhcmFtIHByb21wdCBUaGUgbWFpbiBwcm9tcHQgZm9yIEFJXHJcbiAqIEBwYXJhbSBzeXN0ZW1Qcm9tcHQgT3B0aW9uYWwgc3lzdGVtIHByb21wdCB0byBjb250cm9sIEFJIGJlaGF2aW9yXHJcbiAqIEByZXR1cm5zIEFJIHJlc3BvbnNlXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZnJlZXN0eWxlQUkocHJvbXB0OiBzdHJpbmcsIHN5c3RlbVByb21wdD86IHN0cmluZykge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBheGlvc0luc3RhbmNlID0gY3JlYXRlQXhpb3NJbnN0YW5jZSgpO1xyXG5cclxuICAgIGNvbnN0IHBheWxvYWQ6IGFueSA9IHsgcHJvbXB0IH07XHJcblxyXG4gICAgaWYgKHN5c3RlbVByb21wdCkge1xyXG4gICAgICBwYXlsb2FkLnN5c3RlbVByb21wdCA9IHN5c3RlbVByb21wdDtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zSW5zdGFuY2UucG9zdChcIi9haS9mcmVlc3R5bGVcIiwgcGF5bG9hZCk7XHJcblxyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgcmVzcG9uc2U6IHJlc3BvbnNlLmRhdGEucmVzcG9uc2UgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkFJIGZyZWVzdHlsZSByZXF1ZXN0IGZhaWxlZDpcIiwgZXJyb3IpO1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiVW5rbm93biBlcnJvclwiLFxyXG4gICAgfTtcclxuICB9XHJcbn1cclxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJnU0FzQnNCIn0=
}}),
"[project]/src/actions/data:ac78de [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"60a413cd394ede0a21de841e135772a42e6e722862":"batchGetRelationships"},"src/actions/friend.action.ts",""] */ __turbopack_context__.s({
    "batchGetRelationships": (()=>batchGetRelationships)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var batchGetRelationships = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("60a413cd394ede0a21de841e135772a42e6e722862", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "batchGetRelationships"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/app/(protected)/dashboard/chat/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ChatPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$ConverstationList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/chat/ConverstationList.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$ChatArea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/chat/ChatArea.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$ConverstationInfo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/chat/ConverstationInfo.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$GroupInfo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/chat/GroupInfo.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/authStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$chatStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/chatStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$conversationsStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/conversationsStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$user$2e$action$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/user.action.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
function ChatPage() {
    const [isTabContentVisible, setIsTabContentVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [showContactInfo, setShowContactInfo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isTablet, setIsTablet] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Get state from stores
    const currentUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthStore"])((state)=>state.user);
    const { selectedContact, selectedGroup, setSelectedGroup, currentChatType, setSelectedContact } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$chatStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useChatStore"])();
    const { loadConversations, markAsRead } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$conversationsStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useConversationsStore"])();
    // Get URL search params (currently unused but may be needed for future features)
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSearchParams"])();
    // const groupIdParam = searchParams.get("groupId");
    // const userIdParam = searchParams.get("userId");
    // Use a ref to track if conversations have been loaded
    const conversationsLoadedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    // Load conversations when component mounts
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (currentUser?.id && !conversationsLoadedRef.current) {
            console.log(`[ChatPage] Loading conversations for user ${currentUser.id}`);
            loadConversations(currentUser.id);
            // The API now returns both user and group conversations
            conversationsLoadedRef.current = true;
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        currentUser?.id
    ]); // Remove loadConversations from dependencies as Zustand store functions are stable
    // Handle URL parameters for opening specific chats
    // useEffect(() => {
    //   const handleUrlParams = async () => {
    //     if (!currentUser?.id) return;
    //     // Wait for conversations to load
    //     await new Promise((resolve) => setTimeout(resolve, 1000));
    //     const chatStore = useChatStore.getState();
    //     // Open group chat if groupId is provided
    //     if (groupIdParam) {
    //       console.log(`Opening group chat with ID: ${groupIdParam}`);
    //       await chatStore.openChat(groupIdParam, "GROUP");
    //     }
    //     // Open user chat if userId is provided
    //     else if (userIdParam) {
    //       console.log(`Opening user chat with ID: ${userIdParam}`);
    //       await chatStore.openChat(userIdParam, "USER");
    //     }
    //   };
    //   handleUrlParams();
    // }, [groupIdParam, userIdParam, currentUser?.id]);
    // Track if a chat is currently open
    const isChatOpen = selectedContact !== null || selectedGroup !== null;
    // Handle window resize for responsive layout
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleResize = ()=>{
            const width = window.innerWidth;
            const newIsMobile = width < 768;
            const newIsTablet = width >= 768 && width < 1024;
            const newIsTabContentVisible = !isChatOpen || width >= 768;
            const newShowContactInfo = width >= 1024 && showContactInfo;
            // Only update states if values have changed
            if (newIsMobile !== isMobile) {
                setIsMobile(newIsMobile);
            }
            if (newIsTablet !== isTablet) {
                setIsTablet(newIsTablet);
            }
            if (newIsTabContentVisible !== isTabContentVisible) {
                setIsTabContentVisible(newIsTabContentVisible);
            }
            if (newShowContactInfo !== showContactInfo) {
                setShowContactInfo(newShowContactInfo);
            }
        };
        // Initial call
        handleResize();
        // Add event listener
        window.addEventListener("resize", handleResize);
        // Cleanup
        return ()=>window.removeEventListener("resize", handleResize);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        isChatOpen,
        showContactInfo
    ]); // Only depend on these values
    // Handle selecting a contact or group
    const handleSelectContact = async (id, type)=>{
        console.log(`[ChatPage] Selecting ${type}: ${id}`);
        if (!id) {
            setSelectedContact(null);
            setSelectedGroup(null);
            return;
        }
        const chatStore = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$chatStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useChatStore"].getState();
        const conversationsStore = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$conversationsStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useConversationsStore"].getState();
        // Mark messages as read when selecting a conversation
        if (type === "USER") {
            // Clear any selected group when selecting a contact
            setSelectedGroup(null);
            // Check if this contact is already selected to prevent infinite loops
            const currentSelectedContact = chatStore.selectedContact;
            if (currentSelectedContact?.id === id) {
                console.log(`[ChatPage] Contact ${id} is already selected, skipping`);
                return;
            }
            // First, check if we already have this contact in our conversations store
            const existingConversation = conversationsStore.conversations.find((conv)=>conv.type === "USER" && conv.contact.id === id);
            if (existingConversation) {
                // Use the contact from the conversations store immediately
                setSelectedContact(existingConversation.contact);
                // Mark all messages as read using the store
                conversationsStore.markAsRead(id);
                // Check if we have cached messages before forcing a reload
                const cacheKey = `USER_${id}`;
                const cachedData = chatStore.messageCache[cacheKey];
                const currentTime = new Date();
                const isCacheValid = cachedData && currentTime.getTime() - cachedData.lastFetched.getTime() < 5 * 60 * 1000;
                // Only reload if we don't have valid cache
                if (!isCacheValid || !cachedData || cachedData.messages.length === 0) {
                    console.log(`[ChatPage] No valid cache for user ${id}, reloading messages`);
                    chatStore.setShouldFetchMessages(true);
                    chatStore.reloadConversationMessages(id, "USER");
                }
            } else {
                try {
                    // Only fetch user data if we don't have it in the conversation store
                    const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$user$2e$action$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getUserDataById"])(id);
                    if (result.success && result.user) {
                        // Ensure userInfo exists
                        const user = result.user;
                        if (!user.userInfo) {
                            user.userInfo = {
                                id: user.id,
                                fullName: user.email || user.phoneNumber || "Unknown",
                                profilePictureUrl: null,
                                statusMessage: "No status",
                                blockStrangers: false,
                                createdAt: new Date(),
                                updatedAt: new Date(),
                                userAuth: user
                            };
                        }
                        // Only update the contact if it's still the selected one
                        const currentSelectedContact = chatStore.selectedContact;
                        if (!currentSelectedContact || currentSelectedContact.id !== id) {
                            setSelectedContact(user);
                            chatStore.setShouldFetchMessages(true);
                            chatStore.reloadConversationMessages(id, "USER");
                            // Mark all messages as read using the store
                            conversationsStore.markAsRead(id);
                        }
                    }
                } catch (error) {
                    console.error("Error fetching user data:", error);
                    setSelectedContact(null);
                }
            }
        } else {
            // Handle group conversation
            try {
                console.log(`[ChatPage] Opening group chat with ID: ${id}`);
                // Check if socket is connected before opening group chat
                const { messageSocket, isConnected } = window.messageSocket ? {
                    messageSocket: window.messageSocket,
                    isConnected: window.messageSocket.connected
                } : {
                    messageSocket: null,
                    isConnected: false
                };
                if (messageSocket && !isConnected) {
                    console.log(`[ChatPage] Socket not connected, attempting to reconnect before opening group chat`);
                    messageSocket.connect();
                    await new Promise((resolve)=>setTimeout(resolve, 1000));
                }
                // Check if this group is already selected
                const currentSelectedGroup = chatStore.selectedGroup;
                if (currentSelectedGroup?.id === id) {
                    console.log(`[ChatPage] Group ${id} is already selected, skipping`);
                    return;
                }
                // Proceed with opening the chat
                const success = await chatStore.openChat(id, "GROUP");
                // Mark all messages as read using the store
                conversationsStore.markAsRead(id);
                // Only reload if the initial load failed or if there are no messages
                // Reduced retry frequency to improve performance
                if (!success) {
                    console.log(`[ChatPage] Initial group chat load failed, will retry after delay`);
                    setTimeout(()=>{
                        const currentSelectedGroup = chatStore.selectedGroup;
                        const currentMessages = chatStore.messages;
                        if (currentSelectedGroup?.id === id && (!currentMessages || currentMessages.length === 0)) {
                            console.log(`[ChatPage] Reloading group conversation messages after delay (no messages loaded)`);
                            chatStore.setShouldFetchMessages(true);
                            chatStore.reloadConversationMessages(id, "GROUP");
                        }
                    }, 2000); // Increased delay to reduce server load
                }
            } catch (error) {
                console.error("Error opening group chat:", error);
                setSelectedGroup(null);
            }
        }
    };
    // Note: Group selection is handled by handleSelectContact with type="GROUP"
    // Toggle contact info sidebar
    const toggleContactInfo = ()=>{
        setShowContactInfo((prev)=>!prev);
    };
    // Function to go back to conversation list on mobile
    const handleBackToList = ()=>{
        setIsTabContentVisible(true);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex h-full w-full bg-gray-100 overflow-hidden",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `w-full md:w-[340px] bg-white border-r flex flex-col overflow-hidden ${isTabContentVisible ? "flex" : "hidden"}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$ConverstationList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    onSelectContact: (contactId)=>{
                        handleSelectContact(contactId, "USER");
                        // Hide conversation list on mobile when selecting a chat
                        if (isMobile) {
                            setIsTabContentVisible(false);
                        }
                    },
                    onSelectGroup: (groupId)=>{
                        handleSelectContact(groupId, "GROUP");
                        // Hide conversation list on mobile when selecting a chat
                        if (isMobile) {
                            setIsTabContentVisible(false);
                        }
                    }
                }, void 0, false, {
                    fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
                    lineNumber: 290,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
                lineNumber: 287,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 flex overflow-hidden relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `
            ${isTabContentVisible && isMobile ? "hidden" : "flex"}
            flex-col overflow-hidden transition-all duration-300
            ${!isMobile && !isTablet && showContactInfo ? "w-[calc(100%-340px)]" : "w-full"}
          `,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$ChatArea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            currentUser: currentUser,
                            onToggleInfo: toggleContactInfo,
                            onBackToList: handleBackToList
                        }, void 0, false, {
                            fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
                            lineNumber: 318,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
                        lineNumber: 311,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `
            ${isMobile || isTablet ? "absolute right-0 top-0 bottom-0 z-30" : "absolute right-0 top-0 bottom-0"}
            w-[340px] border-l bg-white flex flex-col overflow-hidden
            transition-all duration-300 transform
            ${showContactInfo ? "translate-x-0 opacity-100" : "translate-x-full opacity-0 pointer-events-none"}
          `,
                        children: [
                            (isMobile || isTablet) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `fixed inset-0 bg-black/20 z-20 transition-opacity duration-300 ${showContactInfo ? "opacity-100" : "opacity-0 pointer-events-none"}`,
                                onClick: ()=>setShowContactInfo(false)
                            }, void 0, false, {
                                fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
                                lineNumber: 337,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-full relative z-30",
                                children: currentChatType === "USER" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$ConverstationInfo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    contact: selectedContact,
                                    onClose: ()=>setShowContactInfo(false),
                                    isOverlay: isMobile || isTablet
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
                                    lineNumber: 345,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chat$2f$GroupInfo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    group: selectedGroup,
                                    onClose: ()=>setShowContactInfo(false),
                                    isOverlay: isMobile || isTablet
                                }, void 0, false, {
                                    fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
                                    lineNumber: 353,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
                                lineNumber: 343,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
                        lineNumber: 327,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
                lineNumber: 309,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/(protected)/dashboard/chat/page.tsx",
        lineNumber: 285,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_a3808391._.js.map