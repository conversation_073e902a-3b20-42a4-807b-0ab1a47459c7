{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/SearchHeader.tsx"], "sourcesContent": ["\"use client\";\r\nimport {\r\n  Search,\r\n  UserPlus,\r\n  Users,\r\n  X,\r\n  MoreH<PERSON>zon<PERSON>,\r\n  Clock,\r\n  Bell,\r\n  SmilePlus,\r\n} from \"lucide-react\";\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useDebounce } from \"@/hooks/useDebounce\";\r\nimport { searchUser, getUserDataById } from \"@/actions/user.action\";\r\nimport { searchMessagesGlobal } from \"@/actions/message.action\";\r\nimport { getFriendsList } from \"@/actions/friend.action\";\r\nimport { isEmail, isPhoneNumber } from \"@/utils/helpers\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { User, Message } from \"@/types/base\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport ProfileDialog from \"./profile/ProfileDialog\";\r\nimport QRCodeDialog from \"./QRCodeDialog\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport CreateGroupDialog from \"./group/CreateGroupDialog\";\r\n\r\n// Extended Message type with search context\r\ninterface MessageWithContext extends Message {\r\n  _searchContext?: {\r\n    userId: string;\r\n  };\r\n}\r\n\r\n// Sử dụng type đơn giản hóa cho Friend\r\ntype Friend = {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  phoneNumber?: string;\r\n  email?: string;\r\n};\r\n\r\n// Type cho kết quả tìm kiếm tin nhắn\r\ninterface SearchResultMessage {\r\n  id: string;\r\n  sender: {\r\n    id: string;\r\n    fullName: string;\r\n    profilePictureUrl: string;\r\n  };\r\n  content: string;\r\n  conversationName?: string;\r\n  date: string;\r\n  highlighted?: boolean;\r\n  _searchContext?: {\r\n    userId: string;\r\n  };\r\n}\r\n\r\n// Type đơn giản hóa cho kết quả tìm kiếm người dùng\r\ntype UserSearchResult = {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  phoneNumber: string;\r\n  email?: string;\r\n};\r\n\r\nexport default function SearchHeader({ className }: { className?: string }) {\r\n  const [searchQuery, setSearchQuery] = useState<string>(\"\");\r\n  const [showResults, setShowResults] = useState<boolean>(false);\r\n  const [showSuggestions, setShowSuggestions] = useState<boolean>(false);\r\n  const [isSearchActive, setIsSearchActive] = useState<boolean>(false);\r\n  const [filteredFriends, setFilteredFriends] = useState<Friend[]>([]);\r\n  const [allFriends, setAllFriends] = useState<Friend[]>([]);\r\n  const [filteredMessages, setFilteredMessages] = useState<\r\n    SearchResultMessage[]\r\n  >([]);\r\n  // State lưu thông tin người gửi đã được lấy từ API\r\n  const [senderDetails, setSenderDetails] = useState<{ [key: string]: User }>(\r\n    {},\r\n  );\r\n\r\n  const [recentSearches, setRecentSearches] = useState<string[]>([]);\r\n  const [phoneSearchResult, setPhoneSearchResult] =\r\n    useState<UserSearchResult | null>(null);\r\n  const [isSearchingUser, setIsSearchingUser] = useState<boolean>(false);\r\n  const [isLoadingFriends, setIsLoadingFriends] = useState<boolean>(false);\r\n  const [showProfileDialog, setShowProfileDialog] = useState<boolean>(false);\r\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\r\n  const [isAddFriendMode, setIsAddFriendMode] = useState<boolean>(false);\r\n  const [showQRCodeDialog, setShowQRCodeDialog] = useState<boolean>(false);\r\n  const [showCreateGroupDialog, setShowCreateGroupDialog] =\r\n    useState<boolean>(false);\r\n  const searchRef = useRef<HTMLDivElement>(null);\r\n  const debouncedSearchQuery = useDebounce(searchQuery, 300);\r\n  const { accessToken, user: currentUser } = useAuthStore();\r\n  const { openChat } = useChatStore();\r\n  const router = useRouter();\r\n\r\n  // Lấy danh sách bạn bè khi component mount và người dùng đã đăng nhập\r\n  useEffect(() => {\r\n    const fetchFriends = async () => {\r\n      // Kiểm tra xem người dùng đã đăng nhập hay chưa\r\n      if (!accessToken || !currentUser) {\r\n        setAllFriends([]);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setIsLoadingFriends(true);\r\n        const result = await getFriendsList(accessToken);\r\n        if (result.success && result.friends) {\r\n          setAllFriends(result.friends);\r\n        } else {\r\n          console.error(\"Failed to fetch friends:\", result.error);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching friends:\", error);\r\n      } finally {\r\n        setIsLoadingFriends(false);\r\n      }\r\n    };\r\n\r\n    fetchFriends();\r\n  }, [accessToken, currentUser]);\r\n\r\n  // Effect để lấy thông tin người gửi từ API khi có kết quả tìm kiếm\r\n  useEffect(() => {\r\n    // Nếu không có tin nhắn hoặc không có token, không làm gì\r\n    if (filteredMessages.length === 0 || !accessToken || !currentUser) {\r\n      return;\r\n    }\r\n\r\n    // Lấy danh sách ID người gửi để lấy thông tin\r\n    // Loại bỏ các ID không hợp lệ ngay từ đầu\r\n    const senderIds = filteredMessages\r\n      .filter((msg) => {\r\n        // Kiểm tra ID hợp lệ và không phải ID hệ thống\r\n        return (\r\n          msg.sender &&\r\n          msg.sender.id &&\r\n          msg.sender.id.trim() !== \"\" &&\r\n          msg.sender.id !== \"system\" &&\r\n          msg.sender.id !== \"unknown\" &&\r\n          msg.sender.id !== \"loading\"\r\n        );\r\n      })\r\n      .map((msg) => msg.sender.id)\r\n      // Lọc các ID trùng lặp\r\n      .filter((id, index, self) => self.indexOf(id) === index);\r\n\r\n    // Nếu không có ID hợp lệ nào, không làm gì\r\n    if (senderIds.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // Lấy thông tin người gửi từ API\r\n    const fetchSenderDetails = async () => {\r\n      for (const senderId of senderIds) {\r\n        // Kiểm tra xem đã có thông tin người gửi trong state chưa\r\n        if (!senderDetails[senderId]) {\r\n          try {\r\n            // Gọi API với ID đã được kiểm tra\r\n            const result = await getUserDataById(senderId);\r\n\r\n            if (result.success && result.user) {\r\n              // Cập nhật thông tin người gửi vào state\r\n              setSenderDetails((prev) => ({\r\n                ...prev,\r\n                [senderId]: result.user,\r\n              }));\r\n            }\r\n          } catch (error) {\r\n            console.error(`Error fetching user data for ${senderId}:`, error);\r\n            // Không làm gì khi có lỗi, để tránh gọi lại API\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchSenderDetails();\r\n  }, [filteredMessages, accessToken, currentUser, senderDetails]);\r\n\r\n  // Handle click outside to close search results and suggestions\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (\r\n        searchRef.current &&\r\n        !searchRef.current.contains(event.target as Node)\r\n      ) {\r\n        setShowResults(false);\r\n        setShowSuggestions(false);\r\n        setIsSearchActive(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  // Search function\r\n  useEffect(() => {\r\n    if (!debouncedSearchQuery) {\r\n      setFilteredFriends([]);\r\n      setFilteredMessages([]);\r\n      setPhoneSearchResult(null);\r\n      setIsSearchingUser(false);\r\n      return;\r\n    }\r\n\r\n    // Kiểm tra xem có phải số điện thoại hoặc email không\r\n    const isPhone = isPhoneNumber(debouncedSearchQuery);\r\n    const isEmailValue = isEmail(debouncedSearchQuery);\r\n    const isSearchingUser = isPhone || isEmailValue;\r\n    setIsSearchingUser(isSearchingUser);\r\n\r\n    // Tìm kiếm tin nhắn dựa trên từ khóa\r\n    const searchForMessages = async () => {\r\n      // Kiểm tra xem người dùng đã đăng nhập hay chưa\r\n      if (!accessToken || !currentUser) {\r\n        setFilteredMessages([]);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        console.log(\"Searching messages with query:\", debouncedSearchQuery);\r\n        // Hiển thị trạng thái đang tìm kiếm\r\n        setFilteredMessages([\r\n          {\r\n            id: \"loading\",\r\n            content: \"Đang tìm kiếm tin nhắn...\",\r\n            sender: {\r\n              id: \"system\",\r\n              fullName: \"Hệ thống\",\r\n              profilePictureUrl: \"/images/default-avatar.png\",\r\n            },\r\n            date: new Date().toLocaleDateString(),\r\n            highlighted: false,\r\n          },\r\n        ]);\r\n\r\n        // Lấy danh sách ID của tất cả bạn bè để tìm kiếm tin nhắn\r\n        const friendIds = allFriends.map((friend) => friend.id);\r\n\r\n        // Nếu không có bạn bè nào, không cần tìm kiếm\r\n        if (friendIds.length === 0) {\r\n          setFilteredMessages([]);\r\n          return;\r\n        }\r\n\r\n        // Truyền token và danh sách ID bạn bè vào hàm searchMessagesGlobal\r\n        const result = await searchMessagesGlobal(\r\n          debouncedSearchQuery,\r\n          friendIds,\r\n        );\r\n\r\n        if (result.success && result.messages && result.messages.length > 0) {\r\n          console.log(\"Found messages:\", result.messages.length);\r\n          // Chuyển đổi từ Message từ API sang SearchResultMessage trong component\r\n          const messages: SearchResultMessage[] = result.messages.map(\r\n            (message: MessageWithContext) => {\r\n              try {\r\n                // Xử lý nội dung tin nhắn\r\n                let messageContent = \"\";\r\n                if (typeof message.content === \"string\") {\r\n                  messageContent = message.content;\r\n                } else if (\r\n                  message.content &&\r\n                  typeof message.content === \"object\"\r\n                ) {\r\n                  // Kiểm tra nếu content là object và có thuộc tính text\r\n                  messageContent = message.content.text || \"\";\r\n                }\r\n\r\n                // Sử dụng messageContent để hiển thị nội dung tin nhắn\r\n\r\n                // Xử lý thông tin người gửi dựa trên dữ liệu trả về từ API\r\n                // Dữ liệu trả về có dạng: sender: { id, email, phoneNumber, ... }\r\n\r\n                // Lấy thông tin về cuộc trò chuyện\r\n                let conversationInfo = \"\";\r\n                if (message._searchContext && message._searchContext.userId) {\r\n                  // Tìm tên người dùng từ danh sách bạn bè\r\n                  const friend = allFriends.find(\r\n                    (f) => f.id === message._searchContext?.userId,\r\n                  );\r\n                  if (friend) {\r\n                    conversationInfo = friend.fullName;\r\n                  }\r\n                }\r\n\r\n                // Lưu thông tin người gửi từ API response\r\n                return {\r\n                  id: message.id,\r\n                  content: messageContent,\r\n                  conversationName: conversationInfo,\r\n                  sender: {\r\n                    id: message.sender.id,\r\n                    fullName: message.sender.email\r\n                      ? message.sender.email.split(\"@\")[0]\r\n                      : \"Người dùng\",\r\n                    profilePictureUrl: \"/images/default-avatar.png\",\r\n                  },\r\n                  date:\r\n                    typeof message.createdAt === \"string\"\r\n                      ? message.createdAt\r\n                      : new Date().toISOString(),\r\n                  highlighted: true,\r\n                  _searchContext: message._searchContext,\r\n                };\r\n              } catch (mapError) {\r\n                console.error(\"Error mapping message:\", mapError, message);\r\n                // Trả về một tin nhắn mặc định nếu có lỗi khi chuyển đổi\r\n                return {\r\n                  id: message.id || \"unknown-id\",\r\n                  content: \"Không thể hiển thị nội dung tin nhắn\",\r\n                  sender: {\r\n                    id: \"unknown\",\r\n                    fullName: \"Người dùng\",\r\n                    profilePictureUrl: \"/images/default-avatar.png\",\r\n                  },\r\n                  date: new Date().toLocaleDateString(),\r\n                  highlighted: true,\r\n                };\r\n              }\r\n            },\r\n          );\r\n\r\n          // Lọc bỏ các tin nhắn không hợp lệ\r\n          const validMessages = messages.filter(\r\n            (msg) => msg.id !== \"unknown-id\",\r\n          );\r\n          setFilteredMessages(validMessages);\r\n        } else {\r\n          console.log(\"No messages found or API returned error\");\r\n          // Hiển thị thông báo không tìm thấy kết quả\r\n          setFilteredMessages([]);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error searching messages:\", error);\r\n        setFilteredMessages([]);\r\n      }\r\n    };\r\n\r\n    // Gọi hàm tìm kiếm tin nhắn\r\n    searchForMessages();\r\n\r\n    if (isSearchingUser) {\r\n      // Nếu là số điện thoại hoặc email hợp lệ, gọi API tìm kiếm\r\n      const searchUserByValue = async () => {\r\n        // Kiểm tra xem người dùng đã đăng nhập hay chưa\r\n        if (!accessToken || !currentUser) {\r\n          setPhoneSearchResult(null);\r\n          return;\r\n        }\r\n\r\n        try {\r\n          // Gọi API tìm kiếm người dùng bằng số điện thoại hoặc email\r\n          const result = await searchUser(debouncedSearchQuery);\r\n\r\n          if (result.success && result.user) {\r\n            // API trả về dữ liệu người dùng trực tiếp, không phải trong trường user\r\n            const userData = result.user;\r\n            setPhoneSearchResult({\r\n              id: userData.id,\r\n              fullName: userData.userInfo?.fullName || \"Người dùng\",\r\n              profilePictureUrl:\r\n                userData.userInfo?.profilePictureUrl ||\r\n                \"/images/default-avatar.png\",\r\n              phoneNumber: userData.phoneNumber || debouncedSearchQuery, // Nếu tìm bằng email, có thể không có phoneNumber\r\n            });\r\n          } else {\r\n            setPhoneSearchResult(null);\r\n          }\r\n        } catch (error) {\r\n          // Xử lý lỗi 404 (không tìm thấy) và các lỗi khác\r\n          console.log(\"Error searching user:\", error);\r\n          // Không hiển thị lỗi, chỉ đặt kết quả tìm kiếm là null\r\n          setPhoneSearchResult(null);\r\n          // Không hiển thị toast lỗi, để UI hiển thị \"Không tìm thấy người dùng\"\r\n        }\r\n      };\r\n\r\n      // Gọi hàm tìm kiếm\r\n      searchUserByValue();\r\n    } else {\r\n      // Tìm kiếm bạn bè dựa trên tên\r\n      // Lọc danh sách bạn bè từ API\r\n      const filtered = allFriends.filter((friend) =>\r\n        friend.fullName\r\n          .toLowerCase()\r\n          .includes(debouncedSearchQuery.toLowerCase()),\r\n      );\r\n      setFilteredFriends(filtered);\r\n      setPhoneSearchResult(null);\r\n    }\r\n  }, [\r\n    debouncedSearchQuery,\r\n    allFriends,\r\n    isAddFriendMode,\r\n    accessToken,\r\n    currentUser,\r\n  ]);\r\n\r\n  // Handle search input change\r\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setSearchQuery(e.target.value);\r\n    if (e.target.value) {\r\n      setShowResults(true);\r\n      setShowSuggestions(false);\r\n    } else {\r\n      setShowResults(false);\r\n      setShowSuggestions(true);\r\n    }\r\n  };\r\n\r\n  // Handle search activation\r\n  const activateSearch = () => {\r\n    setIsSearchActive(true);\r\n    setShowSuggestions(true);\r\n    // Dispatch a custom event to notify other components\r\n    const event = new CustomEvent(\"searchActivated\", {\r\n      detail: { active: true },\r\n    });\r\n    document.dispatchEvent(event);\r\n  };\r\n\r\n  // Handle search deactivation\r\n  const deactivateSearch = () => {\r\n    console.log(\"Deactivating search\");\r\n    setIsSearchActive(false);\r\n    setShowResults(false);\r\n    setShowSuggestions(false);\r\n    setSearchQuery(\"\");\r\n    setIsAddFriendMode(false);\r\n    // Dispatch a custom event to notify other components\r\n    const event = new CustomEvent(\"searchActivated\", {\r\n      detail: { active: false },\r\n    });\r\n    document.dispatchEvent(event);\r\n  };\r\n\r\n  // Handle add friend mode\r\n  const activateAddFriendMode = () => {\r\n    // Thay vì focus vào input, hiển thị dialog QR code\r\n    setShowQRCodeDialog(true);\r\n  };\r\n\r\n  // Handle user profile click\r\n  const handleUserClick = async (user: UserSearchResult) => {\r\n    try {\r\n      // Fetch complete user data using getUserDataById\r\n      const result = await getUserDataById(user.id);\r\n\r\n      if (result.success && result.user) {\r\n        // Use the complete user data from the API\r\n        setSelectedUser(result.user);\r\n      } else {\r\n        // Fallback to simplified user object if API call fails\r\n        console.error(\"Failed to fetch complete user data:\", result.error);\r\n        // Chuyển đổi từ UserSearchResult sang User\r\n        // Sử dụng type assertion để tránh lỗi TypeScript\r\n        const userForProfile = {\r\n          id: user.id,\r\n          userInfo: {\r\n            fullName: user.fullName,\r\n            profilePictureUrl: user.profilePictureUrl,\r\n          },\r\n          email: user.email,\r\n          phoneNumber: user.phoneNumber,\r\n        } as unknown as User;\r\n\r\n        setSelectedUser(userForProfile);\r\n      }\r\n\r\n      // Show the profile dialog\r\n      setShowProfileDialog(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching user data:\", error);\r\n      toast.error(\"Không thể tải thông tin người dùng\");\r\n    }\r\n  };\r\n\r\n  // Handle search submission\r\n  const handleSearchSubmit = () => {\r\n    if (searchQuery.trim() && !recentSearches.includes(searchQuery)) {\r\n      setRecentSearches((prev) => [searchQuery, ...prev.slice(0, 4)]);\r\n    }\r\n  };\r\n\r\n  // Handle selecting a recent search\r\n  const handleSelectRecentSearch = (search: string) => {\r\n    setSearchQuery(search);\r\n    setShowSuggestions(false);\r\n    setShowResults(true);\r\n  };\r\n\r\n  // Clear search\r\n  const clearSearch = () => {\r\n    setSearchQuery(\"\");\r\n  };\r\n\r\n  // Group friends by first letter for alphabetical display\r\n  const groupFriendsByLetter = () => {\r\n    const groups: { [key: string]: Friend[] } = {};\r\n\r\n    const friendsToGroup = searchQuery ? filteredFriends : allFriends;\r\n\r\n    friendsToGroup.forEach((friend) => {\r\n      const firstLetter = friend.fullName.charAt(0).toUpperCase();\r\n      if (!groups[firstLetter]) {\r\n        groups[firstLetter] = [];\r\n      }\r\n      groups[firstLetter].push(friend);\r\n    });\r\n\r\n    return groups;\r\n  };\r\n\r\n  const friendsByLetter = groupFriendsByLetter();\r\n\r\n  return (\r\n    <div\r\n      className={cn(`w-[300px] p-4 relative border-r bg-white`, className)}\r\n      ref={searchRef}\r\n    >\r\n      {/* Header with search input and buttons */}\r\n      <div className=\"flex items-center justify-between w-full\">\r\n        {!isSearchActive ? (\r\n          // Normal state - Search input with buttons\r\n          <>\r\n            <div className=\"relative w-[200px]\">\r\n              <div\r\n                className=\"flex items-center border border-gray-200 rounded-md px-2 h-8 w-full cursor-pointer\"\r\n                onClick={activateSearch}\r\n              >\r\n                <Search className=\"h-4 w-4 text-gray-500\" />\r\n                <div className=\"border-0 h-8 bg-transparent outline-none w-full text-xs ml-2 py-0 text-gray-400 flex items-center\">\r\n                  Tìm kiếm\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-2\">\r\n              <button\r\n                className=\"h-8 w-8 rounded-full hover:bg-gray-100 flex items-center justify-center\"\r\n                title=\"Tìm kiếm và kết bạn\"\r\n                onClick={activateAddFriendMode}\r\n              >\r\n                <UserPlus className=\"h-5 w-5\" />\r\n              </button>\r\n              <button\r\n                className=\"h-8 w-8 rounded-full hover:bg-gray-100 flex items-center justify-center\"\r\n                title=\"Tạo nhóm\"\r\n                onClick={() => setShowCreateGroupDialog(true)}\r\n              >\r\n                <Users className=\"h-5 w-5\" />\r\n              </button>\r\n            </div>\r\n          </>\r\n        ) : (\r\n          // Active search state - Full width input with close button\r\n          <>\r\n            <div className=\"relative flex-1 mr-2\">\r\n              <div className=\"flex items-center border border-gray-200 rounded-md px-2 h-8 w-full bg-gray-50\">\r\n                <Search className=\"h-4 w-4 text-gray-500\" />\r\n                <input\r\n                  placeholder=\"Tìm kiếm\"\r\n                  className=\"border-0 h-8 bg-transparent outline-none w-full text-xs placeholder:text-[0.8125rem] ml-2 py-0\"\r\n                  value={searchQuery}\r\n                  onChange={handleSearchChange}\r\n                  autoFocus\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === \"Enter\") {\r\n                      handleSearchSubmit();\r\n                    }\r\n                  }}\r\n                />\r\n                {searchQuery && (\r\n                  <button onClick={clearSearch} className=\"flex-shrink-0\">\r\n                    <X className=\"h-4 w-4 text-gray-500\" />\r\n                  </button>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <button\r\n              onClick={deactivateSearch}\r\n              className=\"h-8 px-3 rounded-md bg-gray-100 hover:bg-gray-200 flex items-center justify-center text-sm font-medium\"\r\n              title=\"Đóng\"\r\n            >\r\n              Đóng\r\n            </button>\r\n          </>\r\n        )}\r\n      </div>\r\n\r\n      {/* Search Suggestions Dropdown - Positioned absolutely relative to the parent */}\r\n      {isSearchActive && showSuggestions && (\r\n        <div className=\"absolute left-0 top-[60px] w-full bg-white border-t border-gray-200 shadow-lg z-50\">\r\n          <div className=\"p-3 border-b border-gray-100\">\r\n            <div className=\"text-sm font-medium mb-2\">Tìm gần đây</div>\r\n            {recentSearches.length > 0 ? (\r\n              <div className=\"space-y-2\">\r\n                {recentSearches.map((search, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className=\"flex items-center justify-between hover:bg-gray-50 p-2 rounded-md cursor-pointer\"\r\n                    onClick={() => handleSelectRecentSearch(search)}\r\n                  >\r\n                    <div className=\"flex items-center\">\r\n                      <Clock className=\"h-4 w-4 text-gray-400 mr-2\" />\r\n                      <span className=\"text-sm\">{search}</span>\r\n                    </div>\r\n                    <button\r\n                      className=\"h-6 w-6 rounded-full hover:bg-gray-200 flex items-center justify-center\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        setRecentSearches((prev) =>\r\n                          prev.filter((_, i) => i !== index),\r\n                        );\r\n                      }}\r\n                    >\r\n                      <X className=\"h-3 w-3 text-gray-400\" />\r\n                    </button>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-sm text-gray-500 py-2\">\r\n                Không có tìm kiếm nào gần đây\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"p-3\">\r\n            <div className=\"text-sm font-medium mb-2\">Lọc tin nhắn</div>\r\n            <div className=\"flex space-x-2\">\r\n              <div className=\"bg-gray-100 rounded-full px-3 py-1 flex items-center cursor-pointer hover:bg-gray-200\">\r\n                <Bell className=\"h-4 w-4 text-gray-600 mr-1\" />\r\n                <span className=\"text-sm\">Nhắc bạn</span>\r\n              </div>\r\n              <div className=\"bg-gray-100 rounded-full px-3 py-1 flex items-center cursor-pointer hover:bg-gray-200\">\r\n                <SmilePlus className=\"h-4 w-4 text-gray-600 mr-1\" />\r\n                <span className=\"text-sm\">Biểu cảm</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Profile Dialog */}\r\n      {showProfileDialog && selectedUser && (\r\n        <ProfileDialog\r\n          isOpen={showProfileDialog}\r\n          onOpenChange={(open) => setShowProfileDialog(open)}\r\n          user={selectedUser}\r\n          isOwnProfile={selectedUser?.id === currentUser?.id}\r\n          onChat={() => {\r\n            // Xử lý khi nhấn nút nhắn tin\r\n            setShowProfileDialog(false);\r\n          }}\r\n          onCall={() => {\r\n            // Xử lý khi nhấn nút gọi điện\r\n            setShowProfileDialog(false);\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* QR Code Dialog */}\r\n      {currentUser && (\r\n        <QRCodeDialog\r\n          isOpen={showQRCodeDialog}\r\n          onClose={() => setShowQRCodeDialog(false)}\r\n          userId={currentUser.id}\r\n        />\r\n      )}\r\n\r\n      {/* Create Group Dialog */}\r\n      <CreateGroupDialog\r\n        isOpen={showCreateGroupDialog}\r\n        onOpenChange={setShowCreateGroupDialog}\r\n      />\r\n\r\n      {/* Search Results Dropdown */}\r\n      {isSearchActive && showResults && searchQuery && (\r\n        <div className=\"absolute left-0 top-[60px] w-full bg-white border-t border-gray-200 shadow-lg z-50 overflow-hidden\">\r\n          {/* Tab navigation */}\r\n          <div className=\"flex border-b border-gray-200 bg-white\">\r\n            <button className=\"px-4 py-2 text-[13px] font-semibold text-blue-600 border-b-2 border-blue-600\">\r\n              Tất cả\r\n            </button>\r\n            <button className=\"px-4 py-2 text-[13px] font-semibold text-gray-500 hover:text-gray-700\">\r\n              Liên hệ\r\n            </button>\r\n            <button className=\"px-4 py-2 text-[13px] font-semibold text-gray-500 hover:text-gray-700\">\r\n              Tin nhắn\r\n            </button>\r\n            <button className=\"px-4 py-2 text-[13px] font-semibold text-gray-500 hover:text-gray-700\">\r\n              File\r\n            </button>\r\n          </div>\r\n\r\n          {/* User search section */}\r\n          {isSearchingUser && (\r\n            <div className=\"border-b border-gray-100\">\r\n              <div className=\"p-3\">\r\n                <div className=\"text-sm font-medium mb-2\">\r\n                  Tìm bạn qua{\" \"}\r\n                  {isEmail(debouncedSearchQuery) ? \"email\" : \"số điện thoại\"}:\r\n                </div>\r\n\r\n                {phoneSearchResult ? (\r\n                  <div\r\n                    className=\"flex items-center py-2 px-1 hover:bg-gray-50 cursor-pointer rounded-md\"\r\n                    onClick={() => handleUserClick(phoneSearchResult)}\r\n                  >\r\n                    <div className=\"flex items-center w-full\">\r\n                      <div className=\"h-10 w-10 rounded-full overflow-hidden mr-3\">\r\n                        <Image\r\n                          src={phoneSearchResult.profilePictureUrl}\r\n                          alt={phoneSearchResult.fullName}\r\n                          width={40}\r\n                          height={40}\r\n                          className=\"object-cover\"\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <div className=\"text-sm font-medium\">\r\n                          {phoneSearchResult.fullName}\r\n                        </div>\r\n                        <div className=\"text-xs text-gray-500\">\r\n                          {isEmail(debouncedSearchQuery)\r\n                            ? \"Email\"\r\n                            : \"Số điện thoại\"}\r\n                          :{\" \"}\r\n                          <span className=\"text-blue-500\">\r\n                            {isEmail(debouncedSearchQuery)\r\n                              ? phoneSearchResult.email || debouncedSearchQuery\r\n                              : phoneSearchResult.phoneNumber}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-sm text-gray-500 py-2\">\r\n                    Không tìm thấy người dùng nào với{\" \"}\r\n                    {isEmail(debouncedSearchQuery) ? \"email\" : \"số điện thoại\"}{\" \"}\r\n                    này\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {filteredMessages.length > 0 && (\r\n            <div className=\"border-b border-gray-100\">\r\n              <div className=\"text-sm font-medium p-3\">\r\n                Tin nhắn{\" \"}\r\n                {filteredMessages.length > 20\r\n                  ? \"(20+)\"\r\n                  : `(${filteredMessages.length})`}\r\n              </div>\r\n\r\n              {filteredMessages.map((message) => {\r\n                // Nếu là tin nhắn đang tải, hiển thị trạng thái đang tải\r\n                if (message.id === \"loading\") {\r\n                  return (\r\n                    <div\r\n                      key=\"loading\"\r\n                      className=\"flex items-center py-2 px-1 rounded-md\"\r\n                    >\r\n                      <div className=\"flex-1 text-center\">\r\n                        <div className=\"animate-pulse flex space-x-4 items-center\">\r\n                          <div className=\"rounded-full bg-gray-200 h-10 w-10\"></div>\r\n                          <div className=\"flex-1 space-y-2 py-1\">\r\n                            <div className=\"h-2 bg-gray-200 rounded w-3/4\"></div>\r\n                            <div className=\"h-2 bg-gray-200 rounded w-1/2\"></div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                }\r\n\r\n                // Xử lý hiển thị tin nhắn bình thường\r\n                const content = message.content;\r\n                const lowerContent = content.toLowerCase();\r\n                const lowerQuery = debouncedSearchQuery.toLowerCase();\r\n                const startIndex = lowerContent.indexOf(lowerQuery);\r\n\r\n                // Tách nội dung để đánh dấu từ khóa\r\n                const beforeText =\r\n                  startIndex >= 0 ? content.substring(0, startIndex) : \"\";\r\n                const highlightedText =\r\n                  startIndex >= 0\r\n                    ? content.substring(\r\n                        startIndex,\r\n                        startIndex + debouncedSearchQuery.length,\r\n                      )\r\n                    : \"\";\r\n                const afterText =\r\n                  startIndex >= 0\r\n                    ? content.substring(\r\n                        startIndex + debouncedSearchQuery.length,\r\n                      )\r\n                    : content;\r\n\r\n                // Tính thời gian hiển thị\r\n                const messageDate = new Date(message.date);\r\n                const now = new Date();\r\n                const diffInMs = now.getTime() - messageDate.getTime();\r\n                const diffInMinutes = Math.floor(diffInMs / (1000 * 60));\r\n                const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));\r\n\r\n                let timeDisplay = message.date;\r\n                if (diffInMinutes < 60) {\r\n                  timeDisplay = `${diffInMinutes} phút`;\r\n                } else if (diffInHours < 24) {\r\n                  timeDisplay = `${diffInHours} giờ`;\r\n                } else if (diffInHours < 48) {\r\n                  timeDisplay = \"1 ngày\";\r\n                } else {\r\n                  const diffInDays = Math.floor(diffInHours / 24);\r\n                  timeDisplay = `${diffInDays} ngày`;\r\n                }\r\n\r\n                return (\r\n                  <div\r\n                    key={message.id}\r\n                    className=\"flex py-3 hover:bg-gray-50 cursor-pointer px-3 border-b border-gray-100\"\r\n                    onClick={async (e) => {\r\n                      e.preventDefault();\r\n                      e.stopPropagation();\r\n\r\n                      // Lưu ID người dùng trước khi đóng giao diện tìm kiếm\r\n                      let userIdToOpen: string | null = null;\r\n\r\n                      // Get the correct user ID to open the chat\r\n                      // If the search context has a userId, use that (this is the conversation partner ID)\r\n                      const contextUserId = message._searchContext?.userId;\r\n\r\n                      if (contextUserId) {\r\n                        userIdToOpen = contextUserId;\r\n                      } else {\r\n                        // Fallback: If no context userId, check if sender is not the current user\r\n                        const senderId = message.sender.id;\r\n                        if (senderId && senderId !== currentUser?.id) {\r\n                          userIdToOpen = senderId;\r\n                        }\r\n                      }\r\n\r\n                      if (userIdToOpen) {\r\n                        try {\r\n                          // Lưu ID vào biến tạm thời\r\n                          const idToOpen = userIdToOpen;\r\n\r\n                          // Gọi openChat trước khi đóng giao diện tìm kiếm\r\n                          // Điều này đảm bảo rằng chúng ta đã bắt đầu quá trình mở chat\r\n                          // trước khi component có thể bị unmount\r\n                          const success = await openChat(idToOpen, \"USER\");\r\n\r\n                          // Sau đó mới đóng giao diện tìm kiếm\r\n                          deactivateSearch();\r\n\r\n                          if (success) {\r\n                            // Chuyển hướng đến trang chat\r\n                            toast.success(\"Mở cuộc trò chuyện thành công\");\r\n                            router.push(\"/dashboard/chat\");\r\n                          } else {\r\n                            toast.error(\"Không thể mở cuộc trò chuyện này\");\r\n                          }\r\n                        } catch (error) {\r\n                          console.error(\"Error opening chat:\", error);\r\n                          toast.error(\"Có lỗi xảy ra khi mở cuộc trò chuyện\");\r\n                          // Vẫn đóng giao diện tìm kiếm nếu có lỗi\r\n                          deactivateSearch();\r\n                        }\r\n                      } else {\r\n                        toast.error(\"Không thể mở cuộc trò chuyện này\");\r\n                      }\r\n                    }}\r\n                  >\r\n                    <div className=\"h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0\">\r\n                      {senderDetails[message.sender.id]?.userInfo\r\n                        ?.profilePictureUrl &&\r\n                      typeof senderDetails[message.sender.id]?.userInfo\r\n                        ?.profilePictureUrl === \"string\" &&\r\n                      senderDetails[\r\n                        message.sender.id\r\n                      ]?.userInfo?.profilePictureUrl?.trim() !== \"\" ? (\r\n                        <Image\r\n                          src={\r\n                            senderDetails[message.sender.id]?.userInfo\r\n                              ?.profilePictureUrl ||\r\n                            \"/images/default-avatar.png\"\r\n                          }\r\n                          alt={\r\n                            senderDetails[message.sender.id]?.userInfo\r\n                              ?.fullName || \"Người dùng\"\r\n                          }\r\n                          width={40}\r\n                          height={40}\r\n                          className=\"object-cover\"\r\n                        />\r\n                      ) : (\r\n                        <div className=\"h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white\">\r\n                          <span>\r\n                            {(\r\n                              senderDetails[message.sender.id]?.userInfo\r\n                                ?.fullName || \"Người dùng\"\r\n                            ).charAt(0)}\r\n                          </span>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex-1 overflow-hidden\">\r\n                      <div className=\"flex flex-col\">\r\n                        <div className=\"flex justify-between items-start\">\r\n                          <div className=\"text-sm font-medium\">\r\n                            {senderDetails[message.sender.id]?.userInfo\r\n                              ?.fullName || \"Người dùng\"}\r\n                          </div>\r\n                          <div className=\"text-xs text-gray-500 ml-2 flex-shrink-0\">\r\n                            {timeDisplay}\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-700 truncate\">\r\n                          {startIndex >= 0 ? (\r\n                            <span>\r\n                              <span className=\"font-medium\">\r\n                                {senderDetails[message.sender.id]?.userInfo\r\n                                  ?.fullName || \"Người dùng\"}\r\n                                :{\" \"}\r\n                              </span>\r\n                              {beforeText}\r\n                              <span className=\"text-blue-500 font-medium\">\r\n                                {highlightedText}\r\n                              </span>\r\n                              {afterText}\r\n                            </span>\r\n                          ) : (\r\n                            <span>\r\n                              <span className=\"font-medium\">\r\n                                {senderDetails[message.sender.id]?.userInfo\r\n                                  ?.fullName || \"Người dùng\"}\r\n                                :{\" \"}\r\n                              </span>\r\n                              {content}\r\n                            </span>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          )}\r\n\r\n          {isLoadingFriends ? (\r\n            <div className=\"p-4 text-center\">\r\n              <div className=\"animate-spin h-6 w-6 border-2 border-blue-500 rounded-full border-t-transparent mx-auto mb-2\"></div>\r\n              <p className=\"text-sm text-gray-500\">\r\n                Đang tải danh sách bạn bè...\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            filteredFriends.length > 0 && (\r\n              <div className=\"p-2 border-b border-gray-100\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <div className=\"text-sm font-medium\">\r\n                    Bạn bè ({filteredFriends.length})\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <button className=\"text-xs text-blue-500 hover:underline mr-1\">\r\n                      Tất cả\r\n                    </button>\r\n                    <MoreHorizontal className=\"h-4 w-4 text-gray-400\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )\r\n          )}\r\n\r\n          {filteredFriends.length > 0 && (\r\n            <div className=\"max-h-[400px] overflow-y-auto no-scrollbar\">\r\n              <div className=\"px-3 py-2 border-b border-gray-100\">\r\n                <div className=\"text-xs text-gray-500\">Tên (A-Z)</div>\r\n              </div>\r\n\r\n              {/* Alphabetical sections */}\r\n              {Object.keys(friendsByLetter)\r\n                .sort()\r\n                .map((letter) => (\r\n                  <div key={letter}>\r\n                    {/* Letter header */}\r\n                    <div className=\"px-3 py-1 bg-gray-50 text-xs font-medium text-gray-500\">\r\n                      {letter}\r\n                    </div>\r\n\r\n                    {/* Friends list */}\r\n                    {friendsByLetter[letter].map((friend) => (\r\n                      <div\r\n                        key={friend.id}\r\n                        className=\"flex items-center justify-between py-2 hover:bg-gray-50 cursor-pointer px-3\"\r\n                        onClick={() =>\r\n                          handleUserClick({\r\n                            id: friend.id,\r\n                            fullName: friend.fullName,\r\n                            profilePictureUrl: friend.profilePictureUrl,\r\n                            phoneNumber: friend.phoneNumber || \"\", // Sử dụng số điện thoại từ friend nếu có\r\n                            email: friend.email || \"\", // Sử dụng email từ friend nếu có\r\n                          })\r\n                        }\r\n                      >\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"h-8 w-8 rounded-full overflow-hidden mr-2\">\r\n                            <Image\r\n                              src={\r\n                                friend.profilePictureUrl &&\r\n                                typeof friend.profilePictureUrl === \"string\" &&\r\n                                friend.profilePictureUrl?.trim() !== \"\"\r\n                                  ? friend.profilePictureUrl\r\n                                  : \"/images/default-avatar.png\"\r\n                              }\r\n                              alt={friend.fullName}\r\n                              width={32}\r\n                              height={32}\r\n                              className=\"object-cover\"\r\n                            />\r\n                          </div>\r\n                          <span className=\"text-sm\">{friend.fullName}</span>\r\n                        </div>\r\n                        <button\r\n                          className=\"h-6 w-6 rounded-full hover:bg-gray-200 flex items-center justify-center\"\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            // Thêm xử lý menu nếu cần\r\n                          }}\r\n                        >\r\n                          <MoreHorizontal className=\"h-4 w-4 text-gray-400\" />\r\n                        </button>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                ))}\r\n            </div>\r\n          )}\r\n\r\n          {/* No results message - only show when no messages and no friends match */}\r\n          {searchQuery &&\r\n            filteredMessages.length === 0 &&\r\n            filteredFriends.length === 0 && (\r\n              <div className=\"p-4 text-center\">\r\n                <div className=\"flex justify-center mb-4\">\r\n                  <div className=\"relative w-24 h-24\">\r\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                      <div className=\"w-16 h-16 rounded-full border-2 border-blue-200 flex items-center justify-center\">\r\n                        <Search className=\"h-8 w-8 text-blue-300\" />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <p className=\"text-gray-500 text-sm\">Không tìm thấy kết quả</p>\r\n                <p className=\"text-gray-500 text-xs mt-1\">\r\n                  Vui lòng thử lại với từ khóa khác.\r\n                </p>\r\n              </div>\r\n            )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;;;AA3BA;;;;;;;;;;;;;;;;;AAuEe,SAAS,aAAa,EAAE,SAAS,EAA0B;;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAErD,EAAE;IACJ,mDAAmD;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAGH,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAC7C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IACpC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,uBAAuB,yBAAyB,GACrD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,uBAAuB,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,aAAa;IACtD,MAAM,EAAE,WAAW,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IACtD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;uDAAe;oBACnB,gDAAgD;oBAChD,IAAI,CAAC,eAAe,CAAC,aAAa;wBAChC,cAAc,EAAE;wBAChB;oBACF;oBAEA,IAAI;wBACF,oBAAoB;wBACpB,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE;wBACpC,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,EAAE;4BACpC,cAAc,OAAO,OAAO;wBAC9B,OAAO;4BACL,QAAQ,KAAK,CAAC,4BAA4B,OAAO,KAAK;wBACxD;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C,SAAU;wBACR,oBAAoB;oBACtB;gBACF;;YAEA;QACF;iCAAG;QAAC;QAAa;KAAY;IAE7B,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,0DAA0D;YAC1D,IAAI,iBAAiB,MAAM,KAAK,KAAK,CAAC,eAAe,CAAC,aAAa;gBACjE;YACF;YAEA,8CAA8C;YAC9C,0CAA0C;YAC1C,MAAM,YAAY,iBACf,MAAM;oDAAC,CAAC;oBACP,+CAA+C;oBAC/C,OACE,IAAI,MAAM,IACV,IAAI,MAAM,CAAC,EAAE,IACb,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,OAAO,MACzB,IAAI,MAAM,CAAC,EAAE,KAAK,YAClB,IAAI,MAAM,CAAC,EAAE,KAAK,aAClB,IAAI,MAAM,CAAC,EAAE,KAAK;gBAEtB;mDACC,GAAG;oDAAC,CAAC,MAAQ,IAAI,MAAM,CAAC,EAAE;kDAC3B,uBAAuB;aACtB,MAAM;oDAAC,CAAC,IAAI,OAAO,OAAS,KAAK,OAAO,CAAC,QAAQ;;YAEpD,2CAA2C;YAC3C,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B;YACF;YAEA,iCAAiC;YACjC,MAAM;6DAAqB;oBACzB,KAAK,MAAM,YAAY,UAAW;wBAChC,0DAA0D;wBAC1D,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;4BAC5B,IAAI;gCACF,kCAAkC;gCAClC,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE;gCAErC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oCACjC,yCAAyC;oCACzC;qFAAiB,CAAC,OAAS,CAAC;gDAC1B,GAAG,IAAI;gDACP,CAAC,SAAS,EAAE,OAAO,IAAI;4CACzB,CAAC;;gCACH;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAAC,EAAE;4BAC3D,gDAAgD;4BAClD;wBACF;oBACF;gBACF;;YAEA;QACF;iCAAG;QAAC;QAAkB;QAAa;QAAa;KAAc;IAE9D,+DAA+D;IAC/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GACxC;oBACA,eAAe;oBACf,mBAAmB;oBACnB,kBAAkB;gBACpB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;0CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;iCAAG,EAAE;IAEL,kBAAkB;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,sBAAsB;gBACzB,mBAAmB,EAAE;gBACrB,oBAAoB,EAAE;gBACtB,qBAAqB;gBACrB,mBAAmB;gBACnB;YACF;YAEA,sDAAsD;YACtD,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE;YAC9B,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD,EAAE;YAC7B,MAAM,kBAAkB,WAAW;YACnC,mBAAmB;YAEnB,qCAAqC;YACrC,MAAM;4DAAoB;oBACxB,gDAAgD;oBAChD,IAAI,CAAC,eAAe,CAAC,aAAa;wBAChC,oBAAoB,EAAE;wBACtB;oBACF;oBAEA,IAAI;wBACF,QAAQ,GAAG,CAAC,kCAAkC;wBAC9C,oCAAoC;wBACpC,oBAAoB;4BAClB;gCACE,IAAI;gCACJ,SAAS;gCACT,QAAQ;oCACN,IAAI;oCACJ,UAAU;oCACV,mBAAmB;gCACrB;gCACA,MAAM,IAAI,OAAO,kBAAkB;gCACnC,aAAa;4BACf;yBACD;wBAED,0DAA0D;wBAC1D,MAAM,YAAY,WAAW,GAAG;kFAAC,CAAC,SAAW,OAAO,EAAE;;wBAEtD,8CAA8C;wBAC9C,IAAI,UAAU,MAAM,KAAK,GAAG;4BAC1B,oBAAoB,EAAE;4BACtB;wBACF;wBAEA,mEAAmE;wBACnE,MAAM,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD,EACtC,sBACA;wBAGF,IAAI,OAAO,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;4BACnE,QAAQ,GAAG,CAAC,mBAAmB,OAAO,QAAQ,CAAC,MAAM;4BACrD,wEAAwE;4BACxE,MAAM,WAAkC,OAAO,QAAQ,CAAC,GAAG;qFACzD,CAAC;oCACC,IAAI;wCACF,0BAA0B;wCAC1B,IAAI,iBAAiB;wCACrB,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;4CACvC,iBAAiB,QAAQ,OAAO;wCAClC,OAAO,IACL,QAAQ,OAAO,IACf,OAAO,QAAQ,OAAO,KAAK,UAC3B;4CACA,uDAAuD;4CACvD,iBAAiB,QAAQ,OAAO,CAAC,IAAI,IAAI;wCAC3C;wCAEA,uDAAuD;wCAEvD,2DAA2D;wCAC3D,kEAAkE;wCAElE,mCAAmC;wCACnC,IAAI,mBAAmB;wCACvB,IAAI,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,MAAM,EAAE;4CAC3D,yCAAyC;4CACzC,MAAM,SAAS,WAAW,IAAI;4GAC5B,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,cAAc,EAAE;;4CAE1C,IAAI,QAAQ;gDACV,mBAAmB,OAAO,QAAQ;4CACpC;wCACF;wCAEA,0CAA0C;wCAC1C,OAAO;4CACL,IAAI,QAAQ,EAAE;4CACd,SAAS;4CACT,kBAAkB;4CAClB,QAAQ;gDACN,IAAI,QAAQ,MAAM,CAAC,EAAE;gDACrB,UAAU,QAAQ,MAAM,CAAC,KAAK,GAC1B,QAAQ,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAClC;gDACJ,mBAAmB;4CACrB;4CACA,MACE,OAAO,QAAQ,SAAS,KAAK,WACzB,QAAQ,SAAS,GACjB,IAAI,OAAO,WAAW;4CAC5B,aAAa;4CACb,gBAAgB,QAAQ,cAAc;wCACxC;oCACF,EAAE,OAAO,UAAU;wCACjB,QAAQ,KAAK,CAAC,0BAA0B,UAAU;wCAClD,yDAAyD;wCACzD,OAAO;4CACL,IAAI,QAAQ,EAAE,IAAI;4CAClB,SAAS;4CACT,QAAQ;gDACN,IAAI;gDACJ,UAAU;gDACV,mBAAmB;4CACrB;4CACA,MAAM,IAAI,OAAO,kBAAkB;4CACnC,aAAa;wCACf;oCACF;gCACF;;4BAGF,mCAAmC;4BACnC,MAAM,gBAAgB,SAAS,MAAM;0FACnC,CAAC,MAAQ,IAAI,EAAE,KAAK;;4BAEtB,oBAAoB;wBACtB,OAAO;4BACL,QAAQ,GAAG,CAAC;4BACZ,4CAA4C;4BAC5C,oBAAoB,EAAE;wBACxB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,oBAAoB,EAAE;oBACxB;gBACF;;YAEA,4BAA4B;YAC5B;YAEA,IAAI,iBAAiB;gBACnB,2DAA2D;gBAC3D,MAAM;gEAAoB;wBACxB,gDAAgD;wBAChD,IAAI,CAAC,eAAe,CAAC,aAAa;4BAChC,qBAAqB;4BACrB;wBACF;wBAEA,IAAI;4BACF,4DAA4D;4BAC5D,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;4BAEhC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gCACjC,wEAAwE;gCACxE,MAAM,WAAW,OAAO,IAAI;gCAC5B,qBAAqB;oCACnB,IAAI,SAAS,EAAE;oCACf,UAAU,SAAS,QAAQ,EAAE,YAAY;oCACzC,mBACE,SAAS,QAAQ,EAAE,qBACnB;oCACF,aAAa,SAAS,WAAW,IAAI;gCACvC;4BACF,OAAO;gCACL,qBAAqB;4BACvB;wBACF,EAAE,OAAO,OAAO;4BACd,iDAAiD;4BACjD,QAAQ,GAAG,CAAC,yBAAyB;4BACrC,uDAAuD;4BACvD,qBAAqB;wBACrB,uEAAuE;wBACzE;oBACF;;gBAEA,mBAAmB;gBACnB;YACF,OAAO;gBACL,+BAA+B;gBAC/B,8BAA8B;gBAC9B,MAAM,WAAW,WAAW,MAAM;uDAAC,CAAC,SAClC,OAAO,QAAQ,CACZ,WAAW,GACX,QAAQ,CAAC,qBAAqB,WAAW;;gBAE9C,mBAAmB;gBACnB,qBAAqB;YACvB;QACF;iCAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6BAA6B;IAC7B,MAAM,qBAAqB,CAAC;QAC1B,eAAe,EAAE,MAAM,CAAC,KAAK;QAC7B,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,eAAe;YACf,mBAAmB;QACrB,OAAO;YACL,eAAe;YACf,mBAAmB;QACrB;IACF;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB;QACrB,kBAAkB;QAClB,mBAAmB;QACnB,qDAAqD;QACrD,MAAM,QAAQ,IAAI,YAAY,mBAAmB;YAC/C,QAAQ;gBAAE,QAAQ;YAAK;QACzB;QACA,SAAS,aAAa,CAAC;IACzB;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC;QACZ,kBAAkB;QAClB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,qDAAqD;QACrD,MAAM,QAAQ,IAAI,YAAY,mBAAmB;YAC/C,QAAQ;gBAAE,QAAQ;YAAM;QAC1B;QACA,SAAS,aAAa,CAAC;IACzB;IAEA,yBAAyB;IACzB,MAAM,wBAAwB;QAC5B,mDAAmD;QACnD,oBAAoB;IACtB;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,iDAAiD;YACjD,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;YAE5C,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,0CAA0C;gBAC1C,gBAAgB,OAAO,IAAI;YAC7B,OAAO;gBACL,uDAAuD;gBACvD,QAAQ,KAAK,CAAC,uCAAuC,OAAO,KAAK;gBACjE,2CAA2C;gBAC3C,iDAAiD;gBACjD,MAAM,iBAAiB;oBACrB,IAAI,KAAK,EAAE;oBACX,UAAU;wBACR,UAAU,KAAK,QAAQ;wBACvB,mBAAmB,KAAK,iBAAiB;oBAC3C;oBACA,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;gBAC/B;gBAEA,gBAAgB;YAClB;YAEA,0BAA0B;YAC1B,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB;QACzB,IAAI,YAAY,IAAI,MAAM,CAAC,eAAe,QAAQ,CAAC,cAAc;YAC/D,kBAAkB,CAAC,OAAS;oBAAC;uBAAgB,KAAK,KAAK,CAAC,GAAG;iBAAG;QAChE;IACF;IAEA,mCAAmC;IACnC,MAAM,2BAA2B,CAAC;QAChC,eAAe;QACf,mBAAmB;QACnB,eAAe;IACjB;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,eAAe;IACjB;IAEA,yDAAyD;IACzD,MAAM,uBAAuB;QAC3B,MAAM,SAAsC,CAAC;QAE7C,MAAM,iBAAiB,cAAc,kBAAkB;QAEvD,eAAe,OAAO,CAAC,CAAC;YACtB,MAAM,cAAc,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;YACzD,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACxB,MAAM,CAAC,YAAY,GAAG,EAAE;YAC1B;YACA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB;IAExB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAC,wCAAwC,CAAC,EAAE;QAC1D,KAAK;;0BAGL,6LAAC;gBAAI,WAAU;0BACZ,CAAC,iBACA,2CAA2C;8BAC3C;;sCACE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,SAAS;;kDAET,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAI,WAAU;kDAAoG;;;;;;;;;;;;;;;;;sCAMvH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,OAAM;oCACN,SAAS;8CAET,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCACC,WAAU;oCACV,OAAM;oCACN,SAAS,IAAM,yBAAyB;8CAExC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;mCAKvB,2DAA2D;8BAC3D;;sCACE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,UAAU;wCACV,SAAS;wCACT,WAAW,CAAC;4CACV,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB;4CACF;wCACF;;;;;;oCAED,6BACC,6LAAC;wCAAO,SAAS;wCAAa,WAAU;kDACtC,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMrB,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCACP;;;;;;;;;;;;;YAQN,kBAAkB,iCACjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA2B;;;;;;4BACzC,eAAe,MAAM,GAAG,kBACvB,6LAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC;wCAEC,WAAU;wCACV,SAAS,IAAM,yBAAyB;;0DAExC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;;0DAE7B,6LAAC;gDACC,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,kBAAkB,CAAC,OACjB,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gDAEhC;0DAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCAjBV;;;;;;;;;qDAuBX,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;;;;;;;kCAMhD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA2B;;;;;;0CAC1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQnC,qBAAqB,8BACpB,6LAAC,iJAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,cAAc,CAAC,OAAS,qBAAqB;gBAC7C,MAAM;gBACN,cAAc,cAAc,OAAO,aAAa;gBAChD,QAAQ;oBACN,8BAA8B;oBAC9B,qBAAqB;gBACvB;gBACA,QAAQ;oBACN,8BAA8B;oBAC9B,qBAAqB;gBACvB;;;;;;YAKH,6BACC,6LAAC,qIAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,QAAQ,YAAY,EAAE;;;;;;0BAK1B,6LAAC,mJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,cAAc;;;;;;YAIf,kBAAkB,eAAe,6BAChC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAA+E;;;;;;0CAGjG,6LAAC;gCAAO,WAAU;0CAAwE;;;;;;0CAG1F,6LAAC;gCAAO,WAAU;0CAAwE;;;;;;0CAG1F,6LAAC;gCAAO,WAAU;0CAAwE;;;;;;;;;;;;oBAM3F,iCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAA2B;wCAC5B;wCACX,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD,EAAE,wBAAwB,UAAU;wCAAgB;;;;;;;gCAG5D,kCACC,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,gBAAgB;8CAE/B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,kBAAkB,iBAAiB;oDACxC,KAAK,kBAAkB,QAAQ;oDAC/B,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEACZ,kBAAkB,QAAQ;;;;;;kEAE7B,6LAAC;wDAAI,WAAU;;4DACZ,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD,EAAE,wBACL,UACA;4DAAgB;4DAClB;0EACF,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD,EAAE,wBACL,kBAAkB,KAAK,IAAI,uBAC3B,kBAAkB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAO3C,6LAAC;oCAAI,WAAU;;wCAA6B;wCACR;wCACjC,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD,EAAE,wBAAwB,UAAU;wCAAiB;wCAAI;;;;;;;;;;;;;;;;;;oBAQzE,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAA0B;oCAC9B;oCACR,iBAAiB,MAAM,GAAG,KACvB,UACA,CAAC,CAAC,EAAE,iBAAiB,MAAM,CAAC,CAAC,CAAC;;;;;;;4BAGnC,iBAAiB,GAAG,CAAC,CAAC;gCACrB,yDAAyD;gCACzD,IAAI,QAAQ,EAAE,KAAK,WAAW;oCAC5B,qBACE,6LAAC;wCAEC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCARjB;;;;;gCAcV;gCAEA,sCAAsC;gCACtC,MAAM,UAAU,QAAQ,OAAO;gCAC/B,MAAM,eAAe,QAAQ,WAAW;gCACxC,MAAM,aAAa,qBAAqB,WAAW;gCACnD,MAAM,aAAa,aAAa,OAAO,CAAC;gCAExC,oCAAoC;gCACpC,MAAM,aACJ,cAAc,IAAI,QAAQ,SAAS,CAAC,GAAG,cAAc;gCACvD,MAAM,kBACJ,cAAc,IACV,QAAQ,SAAS,CACf,YACA,aAAa,qBAAqB,MAAM,IAE1C;gCACN,MAAM,YACJ,cAAc,IACV,QAAQ,SAAS,CACf,aAAa,qBAAqB,MAAM,IAE1C;gCAEN,0BAA0B;gCAC1B,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;gCACzC,MAAM,MAAM,IAAI;gCAChB,MAAM,WAAW,IAAI,OAAO,KAAK,YAAY,OAAO;gCACpD,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE;gCACtD,MAAM,cAAc,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,EAAE;gCAEzD,IAAI,cAAc,QAAQ,IAAI;gCAC9B,IAAI,gBAAgB,IAAI;oCACtB,cAAc,GAAG,cAAc,KAAK,CAAC;gCACvC,OAAO,IAAI,cAAc,IAAI;oCAC3B,cAAc,GAAG,YAAY,IAAI,CAAC;gCACpC,OAAO,IAAI,cAAc,IAAI;oCAC3B,cAAc;gCAChB,OAAO;oCACL,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;oCAC5C,cAAc,GAAG,WAAW,KAAK,CAAC;gCACpC;gCAEA,qBACE,6LAAC;oCAEC,WAAU;oCACV,SAAS,OAAO;wCACd,EAAE,cAAc;wCAChB,EAAE,eAAe;wCAEjB,sDAAsD;wCACtD,IAAI,eAA8B;wCAElC,2CAA2C;wCAC3C,qFAAqF;wCACrF,MAAM,gBAAgB,QAAQ,cAAc,EAAE;wCAE9C,IAAI,eAAe;4CACjB,eAAe;wCACjB,OAAO;4CACL,0EAA0E;4CAC1E,MAAM,WAAW,QAAQ,MAAM,CAAC,EAAE;4CAClC,IAAI,YAAY,aAAa,aAAa,IAAI;gDAC5C,eAAe;4CACjB;wCACF;wCAEA,IAAI,cAAc;4CAChB,IAAI;gDACF,2BAA2B;gDAC3B,MAAM,WAAW;gDAEjB,iDAAiD;gDACjD,8DAA8D;gDAC9D,wCAAwC;gDACxC,MAAM,UAAU,MAAM,SAAS,UAAU;gDAEzC,qCAAqC;gDACrC;gDAEA,IAAI,SAAS;oDACX,8BAA8B;oDAC9B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oDACd,OAAO,IAAI,CAAC;gDACd,OAAO;oDACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gDACd;4CACF,EAAE,OAAO,OAAO;gDACd,QAAQ,KAAK,CAAC,uBAAuB;gDACrC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gDACZ,yCAAyC;gDACzC;4CACF;wCACF,OAAO;4CACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wCACd;oCACF;;sDAEA,6LAAC;4CAAI,WAAU;sDACZ,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC/B,qBACJ,OAAO,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UACrC,sBAAsB,YAC1B,aAAa,CACX,QAAQ,MAAM,CAAC,EAAE,CAClB,EAAE,UAAU,mBAAmB,WAAW,mBACzC,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KACE,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC9B,qBACJ;gDAEF,KACE,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC9B,YAAY;gDAElB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;qEAGZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;8DACE,CACC,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC9B,YAAY,YAClB,EAAE,MAAM,CAAC;;;;;;;;;;;;;;;;sDAKjB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC/B,YAAY;;;;;;0EAElB,6LAAC;gEAAI,WAAU;0EACZ;;;;;;;;;;;;kEAGL,6LAAC;wDAAI,WAAU;kEACZ,cAAc,kBACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;;wEACb,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC/B,YAAY;wEAAa;wEAC3B;;;;;;;gEAEH;8EACD,6LAAC;oEAAK,WAAU;8EACb;;;;;;gEAEF;;;;;;iFAGH,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;;wEACb,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE,UAC/B,YAAY;wEAAa;wEAC3B;;;;;;;gEAEH;;;;;;;;;;;;;;;;;;;;;;;;mCAvHN,QAAQ,EAAE;;;;;4BA+HrB;;;;;;;oBAIH,iCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;+BAKvC,gBAAgB,MAAM,GAAG,mBACvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAsB;wCAC1B,gBAAgB,MAAM;wCAAC;;;;;;;8CAElC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAA6C;;;;;;sDAG/D,6LAAC,mNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAOnC,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;4BAIxC,OAAO,IAAI,CAAC,iBACV,IAAI,GACJ,GAAG,CAAC,CAAC,uBACJ,6LAAC;;sDAEC,6LAAC;4CAAI,WAAU;sDACZ;;;;;;wCAIF,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,uBAC5B,6LAAC;gDAEC,WAAU;gDACV,SAAS,IACP,gBAAgB;wDACd,IAAI,OAAO,EAAE;wDACb,UAAU,OAAO,QAAQ;wDACzB,mBAAmB,OAAO,iBAAiB;wDAC3C,aAAa,OAAO,WAAW,IAAI;wDACnC,OAAO,OAAO,KAAK,IAAI;oDACzB;;kEAGF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oEACJ,KACE,OAAO,iBAAiB,IACxB,OAAO,OAAO,iBAAiB,KAAK,YACpC,OAAO,iBAAiB,EAAE,WAAW,KACjC,OAAO,iBAAiB,GACxB;oEAEN,KAAK,OAAO,QAAQ;oEACpB,OAAO;oEACP,QAAQ;oEACR,WAAU;;;;;;;;;;;0EAGd,6LAAC;gEAAK,WAAU;0EAAW,OAAO,QAAQ;;;;;;;;;;;;kEAE5C,6LAAC;wDACC,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,eAAe;wDACjB,0BAA0B;wDAC5B;kEAEA,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;+CArCvB,OAAO,EAAE;;;;;;mCATV;;;;;;;;;;;oBAwDjB,eACC,iBAAiB,MAAM,KAAK,KAC5B,gBAAgB,MAAM,KAAK,mBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;0CAK1B,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CACrC,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;AAS1D;GAl/BwB;;QA2BO,8HAAA,CAAA,cAAW;QACG,6HAAA,CAAA,eAAY;QAClC,6HAAA,CAAA,eAAY;QAClB,qIAAA,CAAA,YAAS;;;KA9BF", "debugId": null}}]}