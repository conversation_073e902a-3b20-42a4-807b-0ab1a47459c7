@startuml Giải tán nhóm - Sequence Diagram
title G<PERSON><PERSON><PERSON> tán nhóm - Sequence Diagram

skinparam sequenceParticipant {
    BorderColor grey
    BackgroundColor white
    FontColor black
}

skinparam sequenceLifeLine {
    BorderColor grey
    BackgroundColor white
}

skinparam sequenceArrow {
    Color black
}

actor User
boundary "Frontend" as Frontend
control "GroupController" as Controller
entity "GroupService" as Service
entity "PrismaService" as Prisma
entity "GroupGateway" as Gateway
entity "EventService" as Event
database "Database" as DB
queue "WebSocket" as WS

User -> Frontend: Chọn giải tán nhóm
Frontend -> Controller: POST /groups/:groupId/dissolve

Controller -> Service: dissolveGroup(groupId, requestUserId)

Service -> Prisma: group.findUnique()
Prisma -> DB: SELECT * FROM groups\nWHERE id = ?
DB --> Prisma: Group data with members
Prisma --> Service: Group object with members

Service -> Service: Kiểm tra người dùng có phải trưởng nhóm

alt Không phải trưởng nhóm
    Service --> Controller: throw ForbiddenException
    Controller --> Frontend: 403 Forbidden
    Frontend --> User: <PERSON><PERSON><PERSON> thị lỗi "Chỉ trưởng nhóm mới có thể giải tán nhóm"
else Là trưởng nhóm
    Service -> Service: Lưu danh sách thành viên
    
    Service -> Prisma: groupMember.deleteMany()
    Prisma -> DB: DELETE FROM group_members\nWHERE groupId = ?
    DB --> Prisma: Delete result
    Prisma --> Service: Delete result
    
    Service -> Prisma: group.delete()
    Prisma -> DB: DELETE FROM groups\nWHERE id = ?
    DB --> Prisma: Delete result
    Prisma --> Service: Delete result
    
    loop Cho mỗi thành viên (trừ người giải tán)
        Service -> Gateway: notifyGroupDissolved()
        Gateway -> WS: emit('group_dissolved', data)
    end
    
    Service -> Event: emitGroupDissolved()
    Event -> WS: emit('group_dissolved', data)
    
    Service --> Controller: void
    Controller --> Frontend: 204 No Content
    Frontend --> User: Hiển thị thông báo giải tán nhóm thành công
end

@enduml
