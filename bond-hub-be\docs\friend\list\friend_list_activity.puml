@startuml Xem danh sách bạn bè - Activity Diagram
title Xem danh sách bạn bè - Activity Diagram

|User|
start
:Chọn xem danh sách bạn bè;

|System|
:Lấy ID người dùng từ token xác thực;
:<PERSON><PERSON><PERSON> tra định dạng ID;

if (<PERSON> hợp lệ?) then (Có)
  :<PERSON><PERSON><PERSON> vấn danh sách bạn bè từ cơ sở dữ liệu;
  :<PERSON><PERSON><PERSON> cá<PERSON> mối quan hệ có trạng thái ACCEPTED;
  :L<PERSON>y thông tin chi tiết của từng người bạn;
  :Sắ<PERSON> xếp danh sách theo thời gian cập nhật;
  :<PERSON><PERSON><PERSON> về danh sách bạn bè;
else (Không)
  :Tr<PERSON> về lỗi "ID người dùng không hợp lệ";
endif

|User|
if (Lấy danh sách thành công?) then (Có)
  :Xem danh sách bạn bè;
else (Không)
  :<PERSON><PERSON><PERSON> thị thông báo lỗi;
endif

stop
@enduml
