{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root;\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n));\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName;\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName;\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n));\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n));\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName;\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n));\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sLACA,4YACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  },\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\r\n);\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n));\r\nLabel.displayName = LabelPrimitive.Root.displayName;\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className,\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\",\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n));\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\";\r\nimport { Circle } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  );\r\n});\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName;\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-3.5 w-3.5 fill-primary\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  );\r\n});\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;\r\n\r\nexport { RadioGroup, RadioGroupItem };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;AACA,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;AACA,eAAe,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className,\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\",\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n));\r\nSwitch.displayName = SwitchPrimitives.Root.displayName;\r\n\r\nexport { Switch };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+XACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className,\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  },\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgRsB,iBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/password/ChangePasswordForm.tsx"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { changePassword } from \"@/actions/auth.action\";\r\nimport { toast } from \"sonner\";\r\nimport { Eye, EyeOff } from \"lucide-react\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\n\r\ninterface ChangePasswordFormProps {\r\n  onSuccess?: () => void;\r\n}\r\n\r\nexport default function ChangePasswordForm({\r\n  onSuccess,\r\n}: ChangePasswordFormProps) {\r\n  const [currentPassword, setCurrentPassword] = useState(\"\");\r\n  const [newPassword, setNewPassword] = useState(\"\");\r\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [showCurrentPassword, setShowCurrentPassword] = useState(false);\r\n  const [showNewPassword, setShowNewPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n\r\n  // Lấy accessToken từ store\r\n  const accessToken = useAuthStore((state) => state.accessToken);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Validate passwords\r\n    if (newPassword !== confirmPassword) {\r\n      toast.error(\"Mật khẩu mới không khớp\");\r\n      return;\r\n    }\r\n\r\n    if (newPassword.length < 8) {\r\n      toast.error(\"Mật khẩu phải có ít nhất 8 ký tự\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      if (!accessToken) {\r\n        toast.error(\"Bạn cần đăng nhập lại để thực hiện thao tác này\");\r\n        return;\r\n      }\r\n\r\n      const result = await changePassword(\r\n        currentPassword,\r\n        newPassword,\r\n        accessToken,\r\n      );\r\n\r\n      if (result.success) {\r\n        toast.success(result.message || \"Đổi mật khẩu thành công\");\r\n        // Reset form\r\n        setCurrentPassword(\"\");\r\n        setNewPassword(\"\");\r\n        setConfirmPassword(\"\");\r\n\r\n        // Gọi callback nếu có\r\n        if (onSuccess) {\r\n          onSuccess();\r\n        }\r\n      } else {\r\n        toast.error(result.error || \"Đổi mật khẩu thất bại\");\r\n      }\r\n    } catch (error) {\r\n      console.log(error);\r\n\r\n      toast.error(\"Đã xảy ra lỗi khi đổi mật khẩu\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"currentPassword\" className=\"text-sm text-gray-500\">\r\n          Mật khẩu hiện tại\r\n        </Label>\r\n        <div className=\"relative\">\r\n          <Input\r\n            id=\"currentPassword\"\r\n            type={showCurrentPassword ? \"text\" : \"password\"}\r\n            value={currentPassword}\r\n            onChange={(e) => setCurrentPassword(e.target.value)}\r\n            className=\"pr-10\"\r\n            required\r\n          />\r\n          <button\r\n            type=\"button\"\r\n            className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500\"\r\n            onClick={() => setShowCurrentPassword(!showCurrentPassword)}\r\n          >\r\n            {showCurrentPassword ? <EyeOff size={16} /> : <Eye size={16} />}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"newPassword\" className=\"text-sm text-gray-500\">\r\n          Mật khẩu mới\r\n        </Label>\r\n        <div className=\"relative\">\r\n          <Input\r\n            id=\"newPassword\"\r\n            type={showNewPassword ? \"text\" : \"password\"}\r\n            value={newPassword}\r\n            onChange={(e) => setNewPassword(e.target.value)}\r\n            className=\"pr-10\"\r\n            required\r\n          />\r\n          <button\r\n            type=\"button\"\r\n            className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500\"\r\n            onClick={() => setShowNewPassword(!showNewPassword)}\r\n          >\r\n            {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"confirmPassword\" className=\"text-sm text-gray-500\">\r\n          Xác nhận mật khẩu mới\r\n        </Label>\r\n        <div className=\"relative\">\r\n          <Input\r\n            id=\"confirmPassword\"\r\n            type={showConfirmPassword ? \"text\" : \"password\"}\r\n            value={confirmPassword}\r\n            onChange={(e) => setConfirmPassword(e.target.value)}\r\n            className=\"pr-10\"\r\n            required\r\n          />\r\n          <button\r\n            type=\"button\"\r\n            className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500\"\r\n            onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n          >\r\n            {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <Button\r\n        type=\"submit\"\r\n        className=\"w-full bg-[#0841a3] hover:bg-[#0033a0] text-white\"\r\n        disabled={isLoading}\r\n      >\r\n        {isLoading ? \"Đang đổi mật khẩu...\" : \"Đổi mật khẩu\"}\r\n      </Button>\r\n    </form>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;AAMe,SAAS,mBAAmB,EACzC,SAAS,EACe;IACxB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,2BAA2B;IAC3B,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,WAAW;IAE7D,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,qBAAqB;QACrB,IAAI,gBAAgB,iBAAiB;YACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,IAAI,CAAC,aAAa;gBAChB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD,EAChC,iBACA,aACA;YAGF,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,IAAI;gBAChC,aAAa;gBACb,mBAAmB;gBACnB,eAAe;gBACf,mBAAmB;gBAEnB,sBAAsB;gBACtB,IAAI,WAAW;oBACb;gBACF;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YAEZ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAkB,WAAU;kCAAwB;;;;;;kCAGnE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAM,sBAAsB,SAAS;gCACrC,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;gCACV,QAAQ;;;;;;0CAEV,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,uBAAuB,CAAC;0CAEtC,oCAAsB,8OAAC,0MAAA,CAAA,SAAM;oCAAC,MAAM;;;;;yDAAS,8OAAC,gMAAA,CAAA,MAAG;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAK/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAc,WAAU;kCAAwB;;;;;;kCAG/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAM,kBAAkB,SAAS;gCACjC,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;gCACV,QAAQ;;;;;;0CAEV,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,mBAAmB,CAAC;0CAElC,gCAAkB,8OAAC,0MAAA,CAAA,SAAM;oCAAC,MAAM;;;;;yDAAS,8OAAC,gMAAA,CAAA,MAAG;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAK3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAkB,WAAU;kCAAwB;;;;;;kCAGnE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAM,sBAAsB,SAAS;gCACrC,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;gCACV,QAAQ;;;;;;0CAEV,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,uBAAuB,CAAC;0CAEtC,oCAAsB,8OAAC,0MAAA,CAAA,SAAM;oCAAC,MAAM;;;;;yDAAS,8OAAC,gMAAA,CAAA,MAAG;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAK/D,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,WAAU;gBACV,UAAU;0BAET,YAAY,yBAAyB;;;;;;;;;;;;AAI9C", "debugId": null}}, {"offset": {"line": 1106, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/user-update.action.ts"], "sourcesContent": ["\"use client\";\r\nimport axiosInstance from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\n\r\n// Initiate email update process\r\nexport async function initiateUpdateEmail(newEmail: string) {\r\n  try {\r\n    // Lấy accessToken từ store\r\n    const accessToken = useAuthStore.getState().accessToken;\r\n\r\n    if (!accessToken) {\r\n      console.error(\"Cannot update email: No access token available\");\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const response = await axiosInstance.post(\r\n      \"/auth/update-email/initiate\",\r\n      {\r\n        newEmail,\r\n      },\r\n      {\r\n        headers: {\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      },\r\n    );\r\n\r\n    return {\r\n      success: true,\r\n      updateId: response.data.updateId,\r\n      message:\r\n        response.data.message ||\r\n        \"Mã xác nhận đã được gửi đến email mới của bạn\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Initiate email update failed:\", error);\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"<PERSON><PERSON> xảy ra lỗi khi cập nhật email\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP for email update\r\nexport async function verifyUpdateEmailOtp(updateId: string, otp: string) {\r\n  try {\r\n    // Lấy accessToken từ store\r\n    const accessToken = useAuthStore.getState().accessToken;\r\n\r\n    if (!accessToken) {\r\n      console.error(\"Cannot verify email update: No access token available\");\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const response = await axiosInstance.post(\r\n      \"/auth/update-email/verify\",\r\n      {\r\n        updateId,\r\n        otp,\r\n      },\r\n      {\r\n        headers: {\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      },\r\n    );\r\n\r\n    // After successful verification, update the user in the store\r\n    const currentUser = useAuthStore.getState().user;\r\n    if (currentUser && response.data.user) {\r\n      // Đảm bảo cập nhật đầy đủ thông tin người dùng, bao gồm email\r\n      const updatedUser = {\r\n        ...currentUser,\r\n        ...response.data.user,\r\n        email: response.data.user.email || currentUser.email,\r\n      };\r\n      console.log(\"Cập nhật thông tin người dùng sau khi xác minh email:\", {\r\n        oldEmail: currentUser.email,\r\n        newEmail: response.data.user.email || \"không có trong response\",\r\n      });\r\n      useAuthStore.getState().updateUser(updatedUser);\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Email đã được cập nhật thành công\",\r\n      user: response.data.user,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Verify email update OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error ? error.message : \"Mã xác nhận không hợp lệ\",\r\n    };\r\n  }\r\n}\r\n\r\n// Initiate phone number update process\r\nexport async function initiateUpdatePhone(newPhoneNumber: string) {\r\n  try {\r\n    // Lấy accessToken từ store\r\n    const accessToken = useAuthStore.getState().accessToken;\r\n\r\n    if (!accessToken) {\r\n      console.error(\"Cannot update phone: No access token available\");\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const response = await axiosInstance.post(\r\n      \"/auth/update-phone/initiate\",\r\n      {\r\n        newPhoneNumber,\r\n      },\r\n      {\r\n        headers: {\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      },\r\n    );\r\n\r\n    return {\r\n      success: true,\r\n      updateId: response.data.updateId,\r\n      message:\r\n        response.data.message ||\r\n        \"Mã xác nhận đã được gửi đến số điện thoại mới của bạn\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Initiate phone update failed:\", error);\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi cập nhật số điện thoại\",\r\n    };\r\n  }\r\n}\r\n\r\n// Verify OTP for phone number update\r\nexport async function verifyUpdatePhoneOtp(updateId: string, otp: string) {\r\n  try {\r\n    // Lấy accessToken từ store\r\n    const accessToken = useAuthStore.getState().accessToken;\r\n\r\n    if (!accessToken) {\r\n      console.error(\"Cannot verify phone update: No access token available\");\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const response = await axiosInstance.post(\r\n      \"/auth/update-phone/verify\",\r\n      {\r\n        updateId,\r\n        otp,\r\n      },\r\n      {\r\n        headers: {\r\n          Authorization: `Bearer ${accessToken}`,\r\n        },\r\n      },\r\n    );\r\n\r\n    // After successful verification, update the user in the store\r\n    const currentUser = useAuthStore.getState().user;\r\n    if (currentUser && response.data.user) {\r\n      // Đảm bảo cập nhật đầy đủ thông tin người dùng, bao gồm số điện thoại\r\n      const updatedUser = {\r\n        ...currentUser,\r\n        ...response.data.user,\r\n        phoneNumber: response.data.user.phoneNumber || currentUser.phoneNumber,\r\n      };\r\n      console.log(\r\n        \"Cập nhật thông tin người dùng sau khi xác minh số điện thoại:\",\r\n        {\r\n          oldPhone: currentUser.phoneNumber,\r\n          newPhone: response.data.user.phoneNumber || \"không có trong response\",\r\n        },\r\n      );\r\n      useAuthStore.getState().updateUser(updatedUser);\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message:\r\n        response.data.message || \"Số điện thoại đã được cập nhật thành công\",\r\n      user: response.data.user,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Verify phone update OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error ? error.message : \"Mã xác nhận không hợp lệ\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AACA;AACA;AAFA;;;AAKO,eAAe,oBAAoB,QAAgB;IACxD,IAAI;QACF,2BAA2B;QAC3B,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW;QAEvD,IAAI,CAAC,aAAa;YAChB,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,IAAI,CACvC,+BACA;YACE;QACF,GACA;YACE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,aAAa;YACxC;QACF;QAGF,OAAO;YACL,SAAS;YACT,UAAU,SAAS,IAAI,CAAC,QAAQ;YAChC,SACE,SAAS,IAAI,CAAC,OAAO,IACrB;QACJ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,OACE,iBAAiB,QACb,MAAM,OAAO,GACb;QACR;IACF;AACF;AAGO,eAAe,qBAAqB,QAAgB,EAAE,GAAW;IACtE,IAAI;QACF,2BAA2B;QAC3B,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW;QAEvD,IAAI,CAAC,aAAa;YAChB,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,IAAI,CACvC,6BACA;YACE;YACA;QACF,GACA;YACE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,aAAa;YACxC;QACF;QAGF,8DAA8D;QAC9D,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;QAChD,IAAI,eAAe,SAAS,IAAI,CAAC,IAAI,EAAE;YACrC,8DAA8D;YAC9D,MAAM,cAAc;gBAClB,GAAG,WAAW;gBACd,GAAG,SAAS,IAAI,CAAC,IAAI;gBACrB,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,YAAY,KAAK;YACtD;YACA,QAAQ,GAAG,CAAC,yDAAyD;gBACnE,UAAU,YAAY,KAAK;gBAC3B,UAAU,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;YACxC;YACA,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;QACrC;QAEA,OAAO;YACL,SAAS;YACT,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;YAClC,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YACL,SAAS;YACT,OACE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC7C;IACF;AACF;AAGO,eAAe,oBAAoB,cAAsB;IAC9D,IAAI;QACF,2BAA2B;QAC3B,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW;QAEvD,IAAI,CAAC,aAAa;YAChB,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,IAAI,CACvC,+BACA;YACE;QACF,GACA;YACE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,aAAa;YACxC;QACF;QAGF,OAAO;YACL,SAAS;YACT,UAAU,SAAS,IAAI,CAAC,QAAQ;YAChC,SACE,SAAS,IAAI,CAAC,OAAO,IACrB;QACJ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,OACE,iBAAiB,QACb,MAAM,OAAO,GACb;QACR;IACF;AACF;AAGO,eAAe,qBAAqB,QAAgB,EAAE,GAAW;IACtE,IAAI;QACF,2BAA2B;QAC3B,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW;QAEvD,IAAI,CAAC,aAAa;YAChB,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,IAAI,CACvC,6BACA;YACE;YACA;QACF,GACA;YACE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,aAAa;YACxC;QACF;QAGF,8DAA8D;QAC9D,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;QAChD,IAAI,eAAe,SAAS,IAAI,CAAC,IAAI,EAAE;YACrC,sEAAsE;YACtE,MAAM,cAAc;gBAClB,GAAG,WAAW;gBACd,GAAG,SAAS,IAAI,CAAC,IAAI;gBACrB,aAAa,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,YAAY,WAAW;YACxE;YACA,QAAQ,GAAG,CACT,iEACA;gBACE,UAAU,YAAY,WAAW;gBACjC,UAAU,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI;YAC9C;YAEF,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;QACrC;QAEA,OAAO;YACL,SAAS;YACT,SACE,SAAS,IAAI,CAAC,OAAO,IAAI;YAC3B,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YACL,SAAS;YACT,OACE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC7C;IACF;AACF", "debugId": null}}, {"offset": {"line": 1279, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/utils/helpers.ts"], "sourcesContent": ["import { DeviceType } from \"@/types/base\";\r\nimport * as U<PERSON>ars<PERSON> from \"ua-parser-js\";\r\n\r\n/**\r\n * <PERSON><PERSON><PERSON> định thông tin thiết bị dựa trên userAgent\r\n * @returns Thông tin về loại thiết bị và tên thiết bị\r\n */\r\nexport const getDeviceInfo = () => {\r\n  if (typeof window === \"undefined\") {\r\n    return { deviceType: DeviceType.OTHER, deviceName: \"Dell Latitude 5290\" };\r\n  }\r\n\r\n  const parser = new UAParser.UAParser();\r\n  const result = parser.getResult();\r\n\r\n  // Xác định deviceType\r\n  let deviceType: DeviceType;\r\n  const device = result.device.type?.toLowerCase();\r\n  const os = result.os.name?.toLowerCase();\r\n\r\n  if (device === \"mobile\" || /iphone|android/.test(result.ua.toLowerCase())) {\r\n    deviceType = DeviceType.MOBILE;\r\n  } else if (device === \"tablet\" || /ipad/.test(result.ua.toLowerCase())) {\r\n    deviceType = DeviceType.TABLET;\r\n  } else if (os && /mac|win|linux/.test(os)) {\r\n    deviceType = DeviceType.DESKTOP;\r\n  } else {\r\n    deviceType = DeviceType.OTHER;\r\n  }\r\n\r\n  // Lấy deviceName\r\n  const deviceName =\r\n    result.device.model || result.os.name || \"Dell Latitude 5290\";\r\n\r\n  return { deviceType, deviceName };\r\n};\r\n\r\n/**\r\n * Kiểm tra xem một chuỗi có phải là email hợp lệ hay không\r\n * @param input Chuỗi cần kiểm tra\r\n * @returns true nếu là email hợp lệ, false nếu không phải\r\n */\r\nexport const isEmail = (input: string): boolean => {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(input);\r\n};\r\n\r\n/**\r\n * Kiểm tra xem một chuỗi có phải là số điện thoại hợp lệ hay không\r\n * @param input Chuỗi cần kiểm tra\r\n * @returns true nếu là số điện thoại hợp lệ, false nếu không phải\r\n */\r\nexport const isPhoneNumber = (input: string): boolean => {\r\n  const phoneRegex = /^\\d{10,11}$/; // Giả sử số điện thoại Việt Nam có 10-11 chữ số\r\n  return phoneRegex.test(input);\r\n};\r\n\r\n/**\r\n * Định dạng số điện thoại theo định dạng Việt Nam\r\n * @param phone Số điện thoại cần định dạng\r\n * @returns Số điện thoại đã được định dạng\r\n */\r\nexport const formatPhoneNumber = (phone: string): string => {\r\n  if (!phone) return \"\";\r\n\r\n  // Loại bỏ tất cả các ký tự không phải số\r\n  const cleaned = phone.replace(/\\D/g, \"\");\r\n\r\n  // Kiểm tra độ dài và định dạng theo quy tắc Việt Nam\r\n  if (cleaned.length === 10) {\r\n    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;\r\n  } else if (cleaned.length === 11) {\r\n    return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;\r\n  }\r\n\r\n  return cleaned;\r\n};\r\n\r\n/**\r\n * Định dạng ngày tháng theo định dạng dd/mm/yyyy\r\n * @param date Đối tượng Date cần định dạng\r\n * @returns Chuỗi ngày tháng đã được định dạng\r\n */\r\nexport const formatDate = (date: Date): string => {\r\n  const day = date.getDate().toString().padStart(2, \"0\");\r\n  const month = (date.getMonth() + 1).toString().padStart(2, \"0\");\r\n  const year = date.getFullYear();\r\n\r\n  return `${day}/${month}/${year}`;\r\n};\r\n\r\n/**\r\n * Chuyển đổi giới tính từ tiếng Anh sang tiếng Việt\r\n * @param gender Giới tính bằng tiếng Anh (\"male\" hoặc \"female\")\r\n * @returns Giới tính bằng tiếng Việt\r\n */\r\nexport const translateGender = (gender: string): string => {\r\n  if (gender.toLowerCase() === \"male\") return \"Nam\";\r\n  if (gender.toLowerCase() === \"female\") return \"Nữ\";\r\n  return gender;\r\n};\r\n\r\n/**\r\n * Kiểm tra xem một chuỗi có phải là họ tên tiếng Việt hợp lệ hay không\r\n * @param input Chuỗi cần kiểm tra\r\n * @returns true nếu là họ tên tiếng Việt hợp lệ, false nếu không phải\r\n */\r\nexport const isVietnameseName = (input: string): boolean => {\r\n  // Regex cho tên tiếng Việt có dấu hoặc không dấu\r\n  // Cho phép chữ cái, dấu cách và dấu tiếng Việt\r\n  // Yêu cầu ít nhất 2 từ (họ và tên)\r\n  const vietnameseNameRegex =\r\n    /^[A-Za-zÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ]+(\\s[A-Za-zÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ]+)+$/;\r\n  return vietnameseNameRegex.test(input);\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAMO,MAAM,gBAAgB;IAC3B,wCAAmC;QACjC,OAAO;YAAE,YAAY,oHAAA,CAAA,aAAU,CAAC,KAAK;YAAE,YAAY;QAAqB;IAC1E;;IAEA,MAAM;IACN,MAAM;IAEN,sBAAsB;IACtB,IAAI;IACJ,MAAM;IACN,MAAM;IAYN,iBAAiB;IACjB,MAAM;AAIR;AAOO,MAAM,UAAU,CAAC;IACtB,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAOO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,aAAa,eAAe,gDAAgD;IAClF,OAAO,WAAW,IAAI,CAAC;AACzB;AAOO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,OAAO,OAAO;IAEnB,yCAAyC;IACzC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,qDAAqD;IACrD,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;IAC5E,OAAO,IAAI,QAAQ,MAAM,KAAK,IAAI;QAChC,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;IAC5E;IAEA,OAAO;AACT;AAOO,MAAM,aAAa,CAAC;IACzB,MAAM,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAClD,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAC3D,MAAM,OAAO,KAAK,WAAW;IAE7B,OAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM;AAClC;AAOO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,OAAO,WAAW,OAAO,QAAQ,OAAO;IAC5C,IAAI,OAAO,WAAW,OAAO,UAAU,OAAO;IAC9C,OAAO;AACT;AAOO,MAAM,mBAAmB,CAAC;IAC/B,iDAAiD;IACjD,+CAA+C;IAC/C,mCAAmC;IACnC,MAAM,sBACJ;IACF,OAAO,oBAAoB,IAAI,CAAC;AAClC", "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/hooks/useUserDataSync.ts"], "sourcesContent": ["import { useEffect, useRef, useCallback } from \"react\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { getUserDataById } from \"@/actions/user.action\";\r\n\r\n/**\r\n * Hook để đồng bộ dữ liệu người dùng từ database\r\n * @param interval Thời gian giữa các lần đồng bộ (mặc định: 30 giây)\r\n * @param enabled Bật/tắt đồng bộ tự động\r\n */\r\nexport const useUserDataSync = (\r\n  interval: number = 30000, // 30 giây\r\n  enabled: boolean = true,\r\n) => {\r\n  const { user, isAuthenticated, updateUser } = useAuthStore();\r\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\r\n  const lastSyncRef = useRef<number>(Date.now());\r\n  const isSyncingRef = useRef<boolean>(false); // Tránh đồng bộ đồng thời\r\n  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Timeout để tránh gọi API quá nhiều\r\n\r\n  // Hàm đồng bộ dữ liệu người dùng từ database với các kiểm tra để tránh gọi API quá nhiều\r\n  const syncUserData = useCallback(async () => {\r\n    // Kiểm tra điều kiện trước khi đồng bộ\r\n    if (!isAuthenticated || !user?.id || isSyncingRef.current) return;\r\n\r\n    // Kiểm tra thời gian từ lần đồng bộ cuối cùng, nếu chưa đủ 5 giây thì bỏ qua\r\n    const now = Date.now();\r\n    if (now - lastSyncRef.current < 5000) return;\r\n\r\n    // Đánh dấu đang đồng bộ để tránh gọi nhiều lần\r\n    isSyncingRef.current = true;\r\n\r\n    try {\r\n      const response = await getUserDataById(user.id);\r\n      if (response.success && response.user) {\r\n        updateUser(response.user);\r\n        lastSyncRef.current = now;\r\n      }\r\n    } catch {\r\n      // Xử lý lỗi một cách yên lặng để tránh crash ứng dụng\r\n    } finally {\r\n      // Đặt timeout để tránh đồng bộ liên tục\r\n      syncTimeoutRef.current = setTimeout(() => {\r\n        isSyncingRef.current = false;\r\n      }, 2000);\r\n    }\r\n  }, [isAuthenticated, user?.id, updateUser]);\r\n\r\n  // Thiết lập interval để đồng bộ dữ liệu định kỳ\r\n  useEffect(() => {\r\n    if (!enabled || !isAuthenticated) return;\r\n\r\n    // Đồng bộ dữ liệu ngay khi component mount (với một độ trễ nhỏ để tránh block rendering)\r\n    const initialSyncTimeout = setTimeout(() => {\r\n      syncUserData();\r\n    }, 1000);\r\n\r\n    // Thiết lập interval để đồng bộ dữ liệu định kỳ\r\n    // Sử dụng interval lớn hơn để giảm tải server\r\n    intervalRef.current = setInterval(syncUserData, Math.max(interval, 30000));\r\n\r\n    return () => {\r\n      // Xóa tất cả các timer để tránh memory leak\r\n      if (initialSyncTimeout) clearTimeout(initialSyncTimeout);\r\n      if (intervalRef.current) clearInterval(intervalRef.current);\r\n      if (syncTimeoutRef.current) clearTimeout(syncTimeoutRef.current);\r\n\r\n      intervalRef.current = null;\r\n      syncTimeoutRef.current = null;\r\n    };\r\n  }, [isAuthenticated, interval, enabled, syncUserData]);\r\n\r\n  // Không cần useEffect thứ hai vì đã có dependency trong syncUserData\r\n\r\n  return {\r\n    syncUserData,\r\n    lastSync: lastSyncRef.current,\r\n  };\r\n};\r\n\r\n// Cache kết quả để tránh gọi API quá nhiều\r\nlet lastRefreshTime = 0;\r\nlet refreshPromise: Promise<boolean> | null = null;\r\n\r\n// Hàm tiện ích để đồng bộ dữ liệu người dùng từ bất kỳ đâu trong ứng dụng\r\n// Sử dụng kỹ thuật debounce và cache để giảm số lần gọi API\r\nexport const refreshUserData = async (): Promise<boolean> => {\r\n  const now = Date.now();\r\n\r\n  // Nếu đã gọi trong vòng 2 giây, trả về promise đang chờ hoặc true\r\n  if (now - lastRefreshTime < 2000) {\r\n    return refreshPromise || Promise.resolve(true);\r\n  }\r\n\r\n  const { user, updateUser } = useAuthStore.getState();\r\n  if (!user?.id) return false;\r\n\r\n  lastRefreshTime = now;\r\n\r\n  // Tạo và cache promise\r\n  refreshPromise = getUserDataById(user.id)\r\n    .then((response) => {\r\n      if (response.success && response.user) {\r\n        updateUser(response.user);\r\n        return true;\r\n      }\r\n      return false;\r\n    })\r\n    .catch(() => false)\r\n    .finally(() => {\r\n      // Xóa cache sau 2 giây\r\n      setTimeout(() => {\r\n        refreshPromise = null;\r\n      }, 2000);\r\n    });\r\n\r\n  return refreshPromise;\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAOO,MAAM,kBAAkB,CAC7B,WAAmB,KAAK,EACxB,UAAmB,IAAI;IAEvB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IACzD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAClD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU,KAAK,GAAG;IAC3C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAW,QAAQ,0BAA0B;IACvE,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB,OAAO,qCAAqC;IAEjG,yFAAyF;IACzF,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,uCAAuC;QACvC,IAAI,CAAC,mBAAmB,CAAC,MAAM,MAAM,aAAa,OAAO,EAAE;QAE3D,6EAA6E;QAC7E,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,YAAY,OAAO,GAAG,MAAM;QAEtC,+CAA+C;QAC/C,aAAa,OAAO,GAAG;QAEvB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;YAC9C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,WAAW,SAAS,IAAI;gBACxB,YAAY,OAAO,GAAG;YACxB;QACF,EAAE,OAAM;QACN,sDAAsD;QACxD,SAAU;YACR,wCAAwC;YACxC,eAAe,OAAO,GAAG,WAAW;gBAClC,aAAa,OAAO,GAAG;YACzB,GAAG;QACL;IACF,GAAG;QAAC;QAAiB,MAAM;QAAI;KAAW;IAE1C,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,iBAAiB;QAElC,yFAAyF;QACzF,MAAM,qBAAqB,WAAW;YACpC;QACF,GAAG;QAEH,gDAAgD;QAChD,8CAA8C;QAC9C,YAAY,OAAO,GAAG,YAAY,cAAc,KAAK,GAAG,CAAC,UAAU;QAEnE,OAAO;YACL,4CAA4C;YAC5C,IAAI,oBAAoB,aAAa;YACrC,IAAI,YAAY,OAAO,EAAE,cAAc,YAAY,OAAO;YAC1D,IAAI,eAAe,OAAO,EAAE,aAAa,eAAe,OAAO;YAE/D,YAAY,OAAO,GAAG;YACtB,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG;QAAC;QAAiB;QAAU;QAAS;KAAa;IAErD,qEAAqE;IAErE,OAAO;QACL;QACA,UAAU,YAAY,OAAO;IAC/B;AACF;AAEA,2CAA2C;AAC3C,IAAI,kBAAkB;AACtB,IAAI,iBAA0C;AAIvC,MAAM,kBAAkB;IAC7B,MAAM,MAAM,KAAK,GAAG;IAEpB,kEAAkE;IAClE,IAAI,MAAM,kBAAkB,MAAM;QAChC,OAAO,kBAAkB,QAAQ,OAAO,CAAC;IAC3C;IAEA,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,0HAAA,CAAA,eAAY,CAAC,QAAQ;IAClD,IAAI,CAAC,MAAM,IAAI,OAAO;IAEtB,kBAAkB;IAElB,uBAAuB;IACvB,iBAAiB,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE,EACrC,IAAI,CAAC,CAAC;QACL,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,WAAW,SAAS,IAAI;YACxB,OAAO;QACT;QACA,OAAO;IACT,GACC,KAAK,CAAC,IAAM,OACZ,OAAO,CAAC;QACP,uBAAuB;QACvB,WAAW;YACT,iBAAiB;QACnB,GAAG;IACL;IAEF,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1460, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/profile/UpdateEmailForm.tsx"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  initiateUpdateEmail,\r\n  verifyUpdateEmailOtp,\r\n} from \"@/actions/user-update.action\";\r\nimport { toast } from \"sonner\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\nimport { refreshUserData } from \"@/hooks/useUserDataSync\";\r\n\r\nenum UpdateEmailStep {\r\n  ENTER_EMAIL,\r\n  ENTER_OTP,\r\n  COMPLETE,\r\n}\r\n\r\ninterface UpdateEmailFormProps {\r\n  currentEmail?: string | null;\r\n  onSuccess?: () => void;\r\n}\r\n\r\nexport default function UpdateEmailForm({\r\n  currentEmail,\r\n  onSuccess,\r\n}: UpdateEmailFormProps) {\r\n  const [step, setStep] = useState<UpdateEmailStep>(\r\n    UpdateEmailStep.ENTER_EMAIL,\r\n  );\r\n  const [newEmail, setNewEmail] = useState(\"\");\r\n  const [updateId, setUpdateId] = useState(\"\");\r\n  const [otp, setOtp] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const handleSendOtp = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Validate email\r\n    if (!isEmail(newEmail)) {\r\n      toast.error(\"Vui lòng nhập địa chỉ email hợp lệ\");\r\n      return;\r\n    }\r\n\r\n    // Check if new email is the same as current email\r\n    if (newEmail === currentEmail) {\r\n      toast.error(\"Email mới không được trùng với email hiện tại\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const result = await initiateUpdateEmail(newEmail);\r\n\r\n      if (result.success) {\r\n        setUpdateId(result.updateId);\r\n        setStep(UpdateEmailStep.ENTER_OTP);\r\n        toast.success(result.message);\r\n      } else {\r\n        toast.error(result.error || \"Không thể gửi mã xác nhận\");\r\n      }\r\n    } catch (error) {\r\n      console.error(error);\r\n      toast.error(\"Đã xảy ra lỗi khi gửi mã xác nhận\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleVerifyOtp = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!otp || otp.length < 4) {\r\n      toast.error(\"Vui lòng nhập mã xác nhận hợp lệ\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const result = await verifyUpdateEmailOtp(updateId, otp);\r\n\r\n      if (result.success) {\r\n        setStep(UpdateEmailStep.COMPLETE);\r\n        toast.success(result.message);\r\n\r\n        // Làm mới dữ liệu người dùng từ server để đảm bảo UI hiển thị email mới\r\n        await refreshUserData();\r\n        console.log(\"Đã làm mới dữ liệu người dùng sau khi cập nhật email\");\r\n\r\n        // Sau 1.5 giây, gọi callback nếu có\r\n        if (onSuccess) {\r\n          setTimeout(() => {\r\n            onSuccess();\r\n          }, 1500);\r\n        }\r\n      } else {\r\n        toast.error(result.error || \"Mã xác nhận không hợp lệ\");\r\n      }\r\n    } catch (error) {\r\n      console.error(error);\r\n      toast.error(\"Đã xảy ra lỗi khi xác nhận mã OTP\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleReset = () => {\r\n    setStep(UpdateEmailStep.ENTER_EMAIL);\r\n    setNewEmail(\"\");\r\n    setOtp(\"\");\r\n    setUpdateId(\"\");\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {step === UpdateEmailStep.ENTER_EMAIL && (\r\n        <form onSubmit={handleSendOtp} className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"current-email\" className=\"text-sm text-gray-500\">\r\n              Email hiện tại\r\n            </Label>\r\n            <div className=\"text-sm font-medium\">\r\n              {currentEmail || \"Chưa cập nhật\"}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"new-email\" className=\"text-sm text-gray-500\">\r\n              Email mới\r\n            </Label>\r\n            <Input\r\n              id=\"new-email\"\r\n              type=\"email\"\r\n              value={newEmail}\r\n              onChange={(e) => setNewEmail(e.target.value)}\r\n              placeholder=\"Nhập email mới\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full bg-[#0841a3] hover:bg-[#0033a0] text-white\"\r\n            disabled={isLoading}\r\n          >\r\n            {isLoading ? \"Đang gửi mã...\" : \"Gửi mã xác nhận\"}\r\n          </Button>\r\n        </form>\r\n      )}\r\n\r\n      {step === UpdateEmailStep.ENTER_OTP && (\r\n        <form onSubmit={handleVerifyOtp} className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"otp\" className=\"text-sm text-gray-500\">\r\n              Mã xác nhận\r\n            </Label>\r\n            <Input\r\n              id=\"otp\"\r\n              type=\"text\"\r\n              value={otp}\r\n              onChange={(e) => setOtp(e.target.value)}\r\n              placeholder=\"Nhập mã xác nhận\"\r\n              required\r\n            />\r\n            <p className=\"text-xs text-gray-500\">\r\n              Mã xác nhận đã được gửi đến email {newEmail}\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex space-x-2\">\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              className=\"flex-1\"\r\n              onClick={handleReset}\r\n              disabled={isLoading}\r\n            >\r\n              Quay lại\r\n            </Button>\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"flex-1 bg-[#0841a3] hover:bg-[#0033a0] text-white\"\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? \"Đang xác nhận...\" : \"Xác nhận\"}\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      )}\r\n\r\n      {step === UpdateEmailStep.COMPLETE && (\r\n        <div className=\"space-y-4\">\r\n          <div className=\"p-4 bg-green-50 rounded-md\">\r\n            <p className=\"text-green-600 font-medium\">\r\n              Email đã được cập nhật thành công!\r\n            </p>\r\n          </div>\r\n\r\n          <Button\r\n            type=\"button\"\r\n            className=\"w-full bg-[#0841a3] hover:bg-[#0033a0] text-white\"\r\n            onClick={() => {\r\n              if (onSuccess) {\r\n                onSuccess();\r\n              } else {\r\n                handleReset();\r\n              }\r\n            }}\r\n          >\r\n            {onSuccess ? \"Hoàn tất\" : \"Cập nhật email khác\"}\r\n          </Button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;;;;;;;;;;AAEA,IAAA,AAAK,yCAAA;;;;WAAA;EAAA;AAWU,SAAS,gBAAgB,EACtC,YAAY,EACZ,SAAS,EACY;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAG/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,gBAAgB,OAAO;QAC3B,EAAE,cAAc;QAEhB,iBAAiB;QACjB,IAAI,CAAC,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE,WAAW;YACtB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,kDAAkD;QAClD,IAAI,aAAa,cAAc;YAC7B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE;YAEzC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,OAAO,QAAQ;gBAC3B;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;YAC9B,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,EAAE,cAAc;QAEhB,IAAI,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG;YAC1B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU;YAEpD,IAAI,OAAO,OAAO,EAAE;gBAClB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAE5B,wEAAwE;gBACxE,MAAM,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD;gBACpB,QAAQ,GAAG,CAAC;gBAEZ,oCAAoC;gBACpC,IAAI,WAAW;oBACb,WAAW;wBACT;oBACF,GAAG;gBACL;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB;QACA,YAAY;QACZ,OAAO;QACP,YAAY;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,4BACC,8OAAC;gBAAK,UAAU;gBAAe,WAAU;;kCACvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAgB,WAAU;0CAAwB;;;;;;0CAGjE,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAwB;;;;;;0CAG7D,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,aAAY;gCACZ,QAAQ;;;;;;;;;;;;kCAIZ,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;kCAET,YAAY,mBAAmB;;;;;;;;;;;;YAKrC,4BACC,8OAAC;gBAAK,UAAU;gBAAiB,WAAU;;kCACzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAM,WAAU;0CAAwB;;;;;;0CAGvD,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;gCACtC,aAAY;gCACZ,QAAQ;;;;;;0CAEV,8OAAC;gCAAE,WAAU;;oCAAwB;oCACA;;;;;;;;;;;;;kCAIvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;gCACT,UAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU;0CAET,YAAY,qBAAqB;;;;;;;;;;;;;;;;;;YAMzC,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;kCAK5C,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,WAAU;wBACV,SAAS;4BACP,IAAI,WAAW;gCACb;4BACF,OAAO;gCACL;4BACF;wBACF;kCAEC,YAAY,aAAa;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 1773, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/profile/UpdatePhoneForm.tsx"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  initiateUpdatePhone,\r\n  verifyUpdatePhoneOtp,\r\n} from \"@/actions/user-update.action\";\r\nimport { toast } from \"sonner\";\r\nimport { isPhoneNumber } from \"@/utils/helpers\";\r\nimport { refreshUserData } from \"@/hooks/useUserDataSync\";\r\n\r\nenum UpdatePhoneStep {\r\n  ENTER_PHONE,\r\n  ENTER_OTP,\r\n  COMPLETE,\r\n}\r\n\r\ninterface UpdatePhoneFormProps {\r\n  currentPhone?: string | null;\r\n  onSuccess?: () => void;\r\n}\r\n\r\nexport default function UpdatePhoneForm({\r\n  currentPhone,\r\n  onSuccess,\r\n}: UpdatePhoneFormProps) {\r\n  const [step, setStep] = useState<UpdatePhoneStep>(\r\n    UpdatePhoneStep.ENTER_PHONE,\r\n  );\r\n  const [newPhone, setNewPhone] = useState(\"\");\r\n  const [updateId, setUpdateId] = useState(\"\");\r\n  const [otp, setOtp] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const handleSendOtp = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Validate phone number\r\n    if (!isPhoneNumber(newPhone)) {\r\n      toast.error(\"Vui lòng nhập số điện thoại hợp lệ\");\r\n      return;\r\n    }\r\n\r\n    // Check if new phone is the same as current phone\r\n    if (newPhone === currentPhone) {\r\n      toast.error(\"Số điện thoại mới không được trùng với số hiện tại\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const result = await initiateUpdatePhone(newPhone);\r\n\r\n      if (result.success) {\r\n        setUpdateId(result.updateId);\r\n        setStep(UpdatePhoneStep.ENTER_OTP);\r\n        toast.success(result.message);\r\n      } else {\r\n        toast.error(result.error || \"Không thể gửi mã xác nhận\");\r\n      }\r\n    } catch (error) {\r\n      console.error(error);\r\n      toast.error(\"Đã xảy ra lỗi khi gửi mã xác nhận\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleVerifyOtp = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!otp || otp.length < 4) {\r\n      toast.error(\"Vui lòng nhập mã xác nhận hợp lệ\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const result = await verifyUpdatePhoneOtp(updateId, otp);\r\n\r\n      if (result.success) {\r\n        setStep(UpdatePhoneStep.COMPLETE);\r\n        toast.success(result.message);\r\n\r\n        // Làm mới dữ liệu người dùng từ server để đảm bảo UI hiển thị số điện thoại mới\r\n        await refreshUserData();\r\n        console.log(\r\n          \"Đã làm mới dữ liệu người dùng sau khi cập nhật số điện thoại\",\r\n        );\r\n\r\n        // Sau 1.5 giây, gọi callback nếu có\r\n        if (onSuccess) {\r\n          setTimeout(() => {\r\n            onSuccess();\r\n          }, 1500);\r\n        }\r\n      } else {\r\n        toast.error(result.error || \"Mã xác nhận không hợp lệ\");\r\n      }\r\n    } catch (error) {\r\n      console.error(error);\r\n      toast.error(\"Đã xảy ra lỗi khi xác nhận mã OTP\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleReset = () => {\r\n    setStep(UpdatePhoneStep.ENTER_PHONE);\r\n    setNewPhone(\"\");\r\n    setOtp(\"\");\r\n    setUpdateId(\"\");\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {step === UpdatePhoneStep.ENTER_PHONE && (\r\n        <form onSubmit={handleSendOtp} className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"current-phone\" className=\"text-sm text-gray-500\">\r\n              Số điện thoại hiện tại\r\n            </Label>\r\n            <div className=\"text-sm font-medium\">\r\n              {currentPhone || \"Chưa cập nhật\"}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"new-phone\" className=\"text-sm text-gray-500\">\r\n              Số điện thoại mới\r\n            </Label>\r\n            <Input\r\n              id=\"new-phone\"\r\n              type=\"tel\"\r\n              value={newPhone}\r\n              onChange={(e) => setNewPhone(e.target.value)}\r\n              placeholder=\"Nhập số điện thoại mới\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full bg-[#0841a3] hover:bg-[#0033a0] text-white\"\r\n            disabled={isLoading}\r\n          >\r\n            {isLoading ? \"Đang gửi mã...\" : \"Gửi mã xác nhận\"}\r\n          </Button>\r\n        </form>\r\n      )}\r\n\r\n      {step === UpdatePhoneStep.ENTER_OTP && (\r\n        <form onSubmit={handleVerifyOtp} className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"otp\" className=\"text-sm text-gray-500\">\r\n              Mã xác nhận\r\n            </Label>\r\n            <Input\r\n              id=\"otp\"\r\n              type=\"text\"\r\n              value={otp}\r\n              onChange={(e) => setOtp(e.target.value)}\r\n              placeholder=\"Nhập mã xác nhận\"\r\n              required\r\n            />\r\n            <p className=\"text-xs text-gray-500\">\r\n              Mã xác nhận đã được gửi đến số điện thoại {newPhone}\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex space-x-2\">\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              className=\"flex-1\"\r\n              onClick={handleReset}\r\n              disabled={isLoading}\r\n            >\r\n              Quay lại\r\n            </Button>\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"flex-1 bg-[#0841a3] hover:bg-[#0033a0] text-white\"\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? \"Đang xác nhận...\" : \"Xác nhận\"}\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      )}\r\n\r\n      {step === UpdatePhoneStep.COMPLETE && (\r\n        <div className=\"space-y-4\">\r\n          <div className=\"p-4 bg-green-50 rounded-md\">\r\n            <p className=\"text-green-600 font-medium\">\r\n              Số điện thoại đã được cập nhật thành công!\r\n            </p>\r\n          </div>\r\n\r\n          <Button\r\n            type=\"button\"\r\n            className=\"w-full bg-[#0841a3] hover:bg-[#0033a0] text-white\"\r\n            onClick={() => {\r\n              if (onSuccess) {\r\n                onSuccess();\r\n              } else {\r\n                handleReset();\r\n              }\r\n            }}\r\n          >\r\n            {onSuccess ? \"Hoàn tất\" : \"Cập nhật số điện thoại khác\"}\r\n          </Button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;;;;;;;;;;AAEA,IAAA,AAAK,yCAAA;;;;WAAA;EAAA;AAWU,SAAS,gBAAgB,EACtC,YAAY,EACZ,SAAS,EACY;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAG/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,gBAAgB,OAAO;QAC3B,EAAE,cAAc;QAEhB,wBAAwB;QACxB,IAAI,CAAC,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;YAC5B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,kDAAkD;QAClD,IAAI,aAAa,cAAc;YAC7B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE;YAEzC,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY,OAAO,QAAQ;gBAC3B;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;YAC9B,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,EAAE,cAAc;QAEhB,IAAI,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG;YAC1B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU;YAEpD,IAAI,OAAO,OAAO,EAAE;gBAClB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAE5B,gFAAgF;gBAChF,MAAM,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD;gBACpB,QAAQ,GAAG,CACT;gBAGF,oCAAoC;gBACpC,IAAI,WAAW;oBACb,WAAW;wBACT;oBACF,GAAG;gBACL;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB;QACA,YAAY;QACZ,OAAO;QACP,YAAY;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,4BACC,8OAAC;gBAAK,UAAU;gBAAe,WAAU;;kCACvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAgB,WAAU;0CAAwB;;;;;;0CAGjE,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAwB;;;;;;0CAG7D,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,aAAY;gCACZ,QAAQ;;;;;;;;;;;;kCAIZ,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;kCAET,YAAY,mBAAmB;;;;;;;;;;;;YAKrC,4BACC,8OAAC;gBAAK,UAAU;gBAAiB,WAAU;;kCACzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAM,WAAU;0CAAwB;;;;;;0CAGvD,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;gCACtC,aAAY;gCACZ,QAAQ;;;;;;0CAEV,8OAAC;gCAAE,WAAU;;oCAAwB;oCACQ;;;;;;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;gCACT,UAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU;0CAET,YAAY,qBAAqB;;;;;;;;;;;;;;;;;;YAMzC,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;kCAK5C,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,WAAU;wBACV,SAAS;4BACP,IAAI,WAAW;gCACb;4BACF,OAAO;gCACL;4BACF;wBACF;kCAEC,YAAY,aAAa;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 2086, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/SettingDialog.tsx"], "sourcesContent": ["import {\r\n  <PERSON>alog,\r\n  DialogContent,\r\n  DialogTitle,\r\n  DialogClose,\r\n} from \"@/components/ui/dialog\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport {\r\n  ChevronDown,\r\n  Settings,\r\n  Lock,\r\n  Palette,\r\n  Bell,\r\n  MessageSquare,\r\n  Wrench,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  Mail,\r\n  Phone,\r\n  KeyRound,\r\n} from \"lucide-react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport ChangePasswordForm from \"./password/ChangePasswordForm\";\r\nimport UpdateEmailForm from \"./profile/UpdateEmailForm\";\r\nimport UpdatePhoneForm from \"./profile/UpdatePhoneForm\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface SettingsDialogProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\ntype SettingTab =\r\n  | \"general\"\r\n  | \"privacy\"\r\n  | \"interface\"\r\n  | \"notifications\"\r\n  | \"messages\"\r\n  | \"utilities\";\r\n\r\ntype UserInfoUpdateType = \"phone\" | \"email\" | \"password\" | null;\r\ntype PrivacyContentType = \"main\" | \"userInfo\";\r\n\r\nexport default function SettingsDialog({\r\n  isOpen,\r\n  onOpenChange,\r\n}: SettingsDialogProps) {\r\n  const [activeTab, setActiveTab] = useState<SettingTab | null>(\"general\");\r\n  const [contactTab, setContactTab] = useState(\"all\");\r\n  const [language, setLanguage] = useState(\"Vietnamese\");\r\n  const [isSmallScreen, setIsSmallScreen] = useState(false);\r\n  const [showContent, setShowContent] = useState(false);\r\n  const [showOnlineStatus, setShowOnlineStatus] = useState(true);\r\n  const [updateUserInfoType, setUpdateUserInfoType] =\r\n    useState<UserInfoUpdateType>(null);\r\n  const [privacyContentType, setPrivacyContentType] =\r\n    useState<PrivacyContentType>(\"main\");\r\n\r\n  const user = useAuthStore((state) => state.user);\r\n\r\n  const tabs = [\r\n    { id: \"general\", label: \"Cài đặt chung\", icon: Settings },\r\n    { id: \"privacy\", label: \"Quyền riêng tư\", icon: Lock },\r\n    { id: \"interface\", label: \"Giao diện\", icon: Palette },\r\n    { id: \"notifications\", label: \"Thông báo\", icon: Bell },\r\n    { id: \"messages\", label: \"Tin nhắn\", icon: MessageSquare },\r\n    { id: \"utilities\", label: \"Tiện ích\", icon: Wrench },\r\n  ];\r\n\r\n  // Handle responsive behavior\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const minWidth = 768;\r\n      const isSmall = window.innerWidth < minWidth;\r\n      setIsSmallScreen(isSmall);\r\n      setShowContent(!isSmall);\r\n\r\n      if (isSmall) {\r\n        setActiveTab(null);\r\n      } else {\r\n        setActiveTab(\"general\");\r\n      }\r\n    };\r\n\r\n    handleResize();\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Reset state when dialog opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      const minWidth = 768;\r\n      const isSmall = window.innerWidth < minWidth;\r\n      setIsSmallScreen(isSmall);\r\n      setShowContent(!isSmall);\r\n\r\n      if (isSmall) {\r\n        setActiveTab(null);\r\n      } else {\r\n        setActiveTab(\"general\");\r\n      }\r\n    }\r\n  }, [isOpen]);\r\n\r\n  const handleTabClick = (tabId: SettingTab) => {\r\n    setActiveTab(tabId);\r\n    if (isSmallScreen) {\r\n      setShowContent(true);\r\n    }\r\n  };\r\n\r\n  const handleBackClick = () => {\r\n    setShowContent(false);\r\n    setActiveTab(null);\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"p-0 overflow-hidden rounded-md mx-auto max-w-[55vw] max-h-[95vh] h-[90vh]\">\r\n        <div className=\"flex h-full\">\r\n          {/* Sidebar */}\r\n          <div\r\n            className={cn(\r\n              \"border-r border-gray-200 bg-white\",\r\n              isSmallScreen && showContent ? \"hidden\" : \"block\",\r\n              isSmallScreen ? \"w-full\" : \"w-[245px]\",\r\n            )}\r\n          >\r\n            <div className=\"flex items-center justify-between px-4 py-3\">\r\n              <DialogTitle className=\"text-lg font-semibold text-gray-800\">\r\n                Cài đặt\r\n              </DialogTitle>\r\n              <DialogClose className=\"h-3.5 w-3.5 rounded-sm opacity-70 hover:opacity-100 focus:outline-none\"></DialogClose>\r\n            </div>\r\n            <ul className=\"py-0.5\">\r\n              {tabs.map((tab) => {\r\n                const Icon = tab.icon;\r\n                return (\r\n                  <li key={tab.id}>\r\n                    <button\r\n                      onClick={() => handleTabClick(tab.id as SettingTab)}\r\n                      className={cn(\r\n                        \"w-full flex items-center px-4 py-2 text-sm font-semibold\",\r\n                        activeTab === tab.id\r\n                          ? \"bg-[#e6f0ff] text-[#0841a3]\"\r\n                          : \"text-gray-600 hover:bg-gray-100\",\r\n                      )}\r\n                    >\r\n                      <Icon className=\"h-4 w-4 mr-2.5\" />\r\n                      {tab.label}\r\n                    </button>\r\n                  </li>\r\n                );\r\n              })}\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Content */}\r\n          {showContent && activeTab && (\r\n            <div className=\"flex-1 overflow-y-auto pt-7 bg-[#ebecf0]\">\r\n              {isSmallScreen && (\r\n                <div className=\"flex items-center px-4 py-1 border-b border-gray-200\">\r\n                  <button\r\n                    onClick={handleBackClick}\r\n                    className=\"mr-2 hover:bg-gray-100 flex items-center\"\r\n                  >\r\n                    <ChevronLeft className=\"h-4 w-4 text-[#0841a3]\" />\r\n                  </button>\r\n                  <DialogTitle className=\"text-base font-semibold text-gray-800\">\r\n                    {tabs.find((tab) => tab.id === activeTab)?.label}\r\n                  </DialogTitle>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"pt-4 px-4 pb-4 space-y-4\">\r\n                {/* General Settings */}\r\n                {activeTab === \"general\" && (\r\n                  <div className=\"space-y-4\">\r\n                    {/* Danh bạ (Contact List) */}\r\n                    <div>\r\n                      <h4 className=\"text-base font-semibold mb-1.5 text-gray-800\">\r\n                        Danh bạ\r\n                      </h4>\r\n                      <div>\r\n                        <p className=\"text-sm text-gray-500 mb-1.5\">\r\n                          Danh sách bạn bè được hiển thị trong danh bạ\r\n                        </p>\r\n\r\n                        <div className=\"bg-white rounded-md border border-gray-100 p-2.5\">\r\n                          <RadioGroup\r\n                            value={contactTab}\r\n                            onValueChange={setContactTab}\r\n                            className=\"space-y-2\"\r\n                          >\r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <RadioGroupItem\r\n                                value=\"all\"\r\n                                id=\"all\"\r\n                                className=\"border-[#0841a3] text-[#0841a3] h-4 w-4\"\r\n                              />\r\n                              <Label\r\n                                htmlFor=\"all\"\r\n                                className=\"text-sm font-normal text-gray-700\"\r\n                              >\r\n                                Hiện thị tất cả bạn bè\r\n                              </Label>\r\n                            </div>\r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <RadioGroupItem\r\n                                value=\"active\"\r\n                                id=\"active\"\r\n                                className=\"border-[#0841a3] text-[#0841a3] h-4 w-4\"\r\n                              />\r\n                              <Label\r\n                                htmlFor=\"active\"\r\n                                className=\"text-sm font-normal text-gray-700\"\r\n                              >\r\n                                Chỉ hiện thị bạn bè đang sử dụng Zalo\r\n                              </Label>\r\n                            </div>\r\n                          </RadioGroup>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Ngôn ngữ (Language) */}\r\n                    <div>\r\n                      <h4 className=\"text-base font-semibold mb-1.5 text-gray-800\">\r\n                        Ngôn ngữ\r\n                      </h4>\r\n                      <div>\r\n                        <div className=\"bg-white rounded-md border border-gray-100 p-2.5\">\r\n                          <div className=\"flex justify-between items-center\">\r\n                            <span className=\"text-sm text-gray-600\">\r\n                              Thay đổi ngôn ngữ\r\n                            </span>\r\n                            <Select\r\n                              value={language}\r\n                              onValueChange={setLanguage}\r\n                            >\r\n                              <SelectTrigger className=\"w-[120px] border border-gray-100 rounded-md h-7 px-2 text-base bg-white\">\r\n                                <div className=\"flex justify-between items-center w-full\">\r\n                                  <SelectValue />\r\n                                  <ChevronDown className=\"h-3 w-3 opacity-60\" />\r\n                                </div>\r\n                              </SelectTrigger>\r\n                              <SelectContent>\r\n                                <SelectItem value=\"English\">English</SelectItem>\r\n                                <SelectItem value=\"Vietnamese\">\r\n                                  Tiếng Việt\r\n                                </SelectItem>\r\n                              </SelectContent>\r\n                            </Select>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Privacy Settings */}\r\n                {activeTab === \"privacy\" && privacyContentType === \"main\" && (\r\n                  <div className=\"space-y-4\">\r\n                    {/* Cá nhân section */}\r\n                    <div>\r\n                      <h4 className=\"text-base font-semibold mb-1.5 text-gray-800\">\r\n                        Cá nhân\r\n                      </h4>\r\n                      <div className=\"space-y-3\">\r\n                        <div className=\"bg-white rounded-md border border-gray-100 overflow-hidden\">\r\n                          <div\r\n                            className=\"p-2.5 flex justify-between items-center cursor-pointer hover:bg-gray-50\"\r\n                            onClick={() => {\r\n                              setUpdateUserInfoType(\"phone\");\r\n                              setPrivacyContentType(\"userInfo\");\r\n                            }}\r\n                          >\r\n                            <div className=\"flex items-center\">\r\n                              <Phone className=\"h-4 w-4 mr-2.5 text-gray-500\" />\r\n                              <span className=\"text-sm text-gray-700\">\r\n                                Số điện thoại\r\n                              </span>\r\n                            </div>\r\n                            <ChevronRight className=\"h-4 w-4 text-gray-400\" />\r\n                          </div>\r\n\r\n                          <div\r\n                            className=\"p-2.5 flex justify-between items-center border-t border-gray-100 cursor-pointer hover:bg-gray-50\"\r\n                            onClick={() => {\r\n                              setUpdateUserInfoType(\"email\");\r\n                              setPrivacyContentType(\"userInfo\");\r\n                            }}\r\n                          >\r\n                            <div className=\"flex items-center\">\r\n                              <Mail className=\"h-4 w-4 mr-2.5 text-gray-500\" />\r\n                              <span className=\"text-sm text-gray-700\">\r\n                                Email\r\n                              </span>\r\n                            </div>\r\n                            <ChevronRight className=\"h-4 w-4 text-gray-400\" />\r\n                          </div>\r\n\r\n                          <div\r\n                            className=\"p-2.5 flex justify-between items-center border-t border-gray-100 cursor-pointer hover:bg-gray-50\"\r\n                            onClick={() => {\r\n                              setUpdateUserInfoType(\"password\");\r\n                              setPrivacyContentType(\"userInfo\");\r\n                            }}\r\n                          >\r\n                            <div className=\"flex items-center\">\r\n                              <KeyRound className=\"h-4 w-4 mr-2.5 text-gray-500\" />\r\n                              <span className=\"text-sm text-gray-700\">\r\n                                Mật khẩu\r\n                              </span>\r\n                            </div>\r\n                            <ChevronRight className=\"h-4 w-4 text-gray-400\" />\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Tin nhắn và cuộc gọi section */}\r\n                    <div>\r\n                      <h4 className=\"text-base font-semibold mb-1.5 text-gray-800\">\r\n                        Tin nhắn và cuộc gọi\r\n                      </h4>\r\n                      <div className=\"bg-white rounded-md border border-gray-100 p-2.5\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"flex items-center\">\r\n                            <span className=\"text-sm text-gray-700\">\r\n                              Hiện trạng thái &quot;Đã xem&quot;\r\n                            </span>\r\n                          </div>\r\n                          <Switch\r\n                            checked={showOnlineStatus}\r\n                            onCheckedChange={setShowOnlineStatus}\r\n                            className=\"data-[state=checked]:bg-[#0841a3]\"\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Chặn tin nhắn section */}\r\n                    <div>\r\n                      <h4 className=\"text-base font-semibold mb-1.5 text-gray-800\">\r\n                        Chặn tin nhắn\r\n                      </h4>\r\n                      <div className=\"bg-white rounded-md border border-gray-100 p-2.5\">\r\n                        <div\r\n                          className=\"flex justify-between items-center cursor-pointer hover:bg-gray-50\"\r\n                          onClick={() =>\r\n                            toast.info(\"Tính năng đang được phát triển\")\r\n                          }\r\n                        >\r\n                          <span className=\"text-sm text-gray-700\">\r\n                            Danh sách chặn\r\n                          </span>\r\n                          <ChevronRight className=\"h-4 w-4 text-gray-400\" />\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Nguồn tìm kiếm section */}\r\n                    <div>\r\n                      <h4 className=\"text-base font-semibold mb-1.5 text-gray-800\">\r\n                        Nguồn tìm kiếm\r\n                      </h4>\r\n                      <div className=\"bg-white rounded-md border border-gray-100 p-2.5\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"flex items-center\">\r\n                            <span className=\"text-sm text-gray-700\">\r\n                              Cho phép tìm kiếm bằng số điện thoại\r\n                            </span>\r\n                          </div>\r\n                          <Switch\r\n                            checked={true}\r\n                            className=\"data-[state=checked]:bg-[#0841a3]\"\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* User Info Update Forms */}\r\n                {activeTab === \"privacy\" &&\r\n                  privacyContentType === \"userInfo\" && (\r\n                    <div className=\"space-y-4\">\r\n                      {updateUserInfoType === \"phone\" && (\r\n                        <div className=\"bg-white rounded-md border border-gray-100 p-4\">\r\n                          <div className=\"flex items-center mb-4\">\r\n                            <button\r\n                              onClick={() => {\r\n                                setUpdateUserInfoType(null);\r\n                                setPrivacyContentType(\"main\");\r\n                              }}\r\n                              className=\"mr-2 hover:bg-gray-100 p-1 rounded-full\"\r\n                            >\r\n                              <ChevronLeft className=\"h-4 w-4 text-[#0841a3]\" />\r\n                            </button>\r\n                            <h4 className=\"text-base font-semibold text-gray-800\">\r\n                              Số điện thoại\r\n                            </h4>\r\n                          </div>\r\n\r\n                          <UpdatePhoneForm\r\n                            currentPhone={user?.phoneNumber}\r\n                            onSuccess={() => {\r\n                              setUpdateUserInfoType(null);\r\n                              setPrivacyContentType(\"main\");\r\n                            }}\r\n                          />\r\n                        </div>\r\n                      )}\r\n\r\n                      {updateUserInfoType === \"email\" && (\r\n                        <div className=\"bg-white rounded-md border border-gray-100 p-4\">\r\n                          <div className=\"flex items-center mb-4\">\r\n                            <button\r\n                              onClick={() => {\r\n                                setUpdateUserInfoType(null);\r\n                                setPrivacyContentType(\"main\");\r\n                              }}\r\n                              className=\"mr-2 hover:bg-gray-100 p-1 rounded-full\"\r\n                            >\r\n                              <ChevronLeft className=\"h-4 w-4 text-[#0841a3]\" />\r\n                            </button>\r\n                            <h4 className=\"text-base font-semibold text-gray-800\">\r\n                              Email\r\n                            </h4>\r\n                          </div>\r\n\r\n                          <UpdateEmailForm\r\n                            currentEmail={user?.email}\r\n                            onSuccess={() => {\r\n                              setUpdateUserInfoType(null);\r\n                              setPrivacyContentType(\"main\");\r\n                            }}\r\n                          />\r\n                        </div>\r\n                      )}\r\n\r\n                      {updateUserInfoType === \"password\" && (\r\n                        <div className=\"bg-white rounded-md border border-gray-100 p-4\">\r\n                          <div className=\"flex items-center mb-4\">\r\n                            <button\r\n                              onClick={() => {\r\n                                setUpdateUserInfoType(null);\r\n                                setPrivacyContentType(\"main\");\r\n                              }}\r\n                              className=\"mr-2 hover:bg-gray-100 p-1 rounded-full\"\r\n                            >\r\n                              <ChevronLeft className=\"h-4 w-4 text-[#0841a3]\" />\r\n                            </button>\r\n                            <h4 className=\"text-base font-semibold text-gray-800\">\r\n                              Mật khẩu\r\n                            </h4>\r\n                          </div>\r\n\r\n                          <ChangePasswordForm\r\n                            onSuccess={() => {\r\n                              setUpdateUserInfoType(null);\r\n                              setPrivacyContentType(\"main\");\r\n                            }}\r\n                          />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n\r\n                {/* Interface Settings */}\r\n                {activeTab === \"interface\" && (\r\n                  <div className=\"space-y-4\">\r\n                    <h4 className=\"text-base font-semibold mb-2 text-gray-800\">\r\n                      Giao diện\r\n                    </h4>\r\n                    <p className=\"text-sm text-gray-500\">\r\n                      Tùy chỉnh giao diện ứng dụng\r\n                    </p>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Notifications Settings */}\r\n                {activeTab === \"notifications\" && (\r\n                  <div className=\"space-y-4\">\r\n                    <h4 className=\"text-base font-semibold mb-2 text-gray-800\">\r\n                      Thông báo\r\n                    </h4>\r\n                    <p className=\"text-sm text-gray-500\">\r\n                      Quản lý cài đặt thông báo\r\n                    </p>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Messages Settings */}\r\n                {activeTab === \"messages\" && (\r\n                  <div className=\"space-y-4\">\r\n                    <h4 className=\"text-base font-semibold mb-2 text-gray-800\">\r\n                      Tin nhắn\r\n                    </h4>\r\n                    <p className=\"text-sm text-gray-500\">\r\n                      Cài đặt cho tin nhắn và cuộc trò chuyện\r\n                    </p>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Utilities Settings */}\r\n                {activeTab === \"utilities\" && (\r\n                  <div className=\"space-y-4\">\r\n                    <h4 className=\"text-base font-semibold mb-2 text-gray-800\">\r\n                      Tiện ích\r\n                    </h4>\r\n                    <p className=\"text-sm text-gray-500\">\r\n                      Quản lý các tiện ích bổ sung\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAMA;AACA;AAOA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAkBe,SAAS,eAAe,EACrC,MAAM,EACN,YAAY,EACQ;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAC/C,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/B,MAAM,CAAC,oBAAoB,sBAAsB,GAC/C,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAE/B,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IAE/C,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,OAAO;YAAiB,MAAM,0MAAA,CAAA,WAAQ;QAAC;QACxD;YAAE,IAAI;YAAW,OAAO;YAAkB,MAAM,kMAAA,CAAA,OAAI;QAAC;QACrD;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,wMAAA,CAAA,UAAO;QAAC;QACrD;YAAE,IAAI;YAAiB,OAAO;YAAa,MAAM,kMAAA,CAAA,OAAI;QAAC;QACtD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,wNAAA,CAAA,gBAAa;QAAC;QACzD;YAAE,IAAI;YAAa,OAAO;YAAY,MAAM,sMAAA,CAAA,SAAM;QAAC;KACpD;IAED,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,WAAW;YACjB,MAAM,UAAU,OAAO,UAAU,GAAG;YACpC,iBAAiB;YACjB,eAAe,CAAC;YAEhB,IAAI,SAAS;gBACX,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,MAAM,WAAW;YACjB,MAAM,UAAU,OAAO,UAAU,GAAG;YACpC,iBAAiB;YACjB,eAAe,CAAC;YAEhB,IAAI,SAAS;gBACX,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,iBAAiB,CAAC;QACtB,aAAa;QACb,IAAI,eAAe;YACjB,eAAe;QACjB;IACF;IAEA,MAAM,kBAAkB;QACtB,eAAe;QACf,aAAa;IACf;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;sBACvB,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,iBAAiB,cAAc,WAAW,SAC1C,gBAAgB,WAAW;;0CAG7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,cAAW;wCAAC,WAAU;kDAAsC;;;;;;kDAG7D,8OAAC,kIAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAEzB,8OAAC;gCAAG,WAAU;0CACX,KAAK,GAAG,CAAC,CAAC;oCACT,MAAM,OAAO,IAAI,IAAI;oCACrB,qBACE,8OAAC;kDACC,cAAA,8OAAC;4CACC,SAAS,IAAM,eAAe,IAAI,EAAE;4CACpC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA,cAAc,IAAI,EAAE,GAChB,gCACA;;8DAGN,8OAAC;oDAAK,WAAU;;;;;;gDACf,IAAI,KAAK;;;;;;;uCAXL,IAAI,EAAE;;;;;gCAenB;;;;;;;;;;;;oBAKH,eAAe,2BACd,8OAAC;wBAAI,WAAU;;4BACZ,+BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC,kIAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB,KAAK,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,YAAY;;;;;;;;;;;;0CAKjD,8OAAC;gCAAI,WAAU;;oCAEZ,cAAc,2BACb,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAA+B;;;;;;0EAI5C,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,0IAAA,CAAA,aAAU;oEACT,OAAO;oEACP,eAAe;oEACf,WAAU;;sFAEV,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFACb,OAAM;oFACN,IAAG;oFACH,WAAU;;;;;;8FAEZ,8OAAC,iIAAA,CAAA,QAAK;oFACJ,SAAQ;oFACR,WAAU;8FACX;;;;;;;;;;;;sFAIH,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0IAAA,CAAA,iBAAc;oFACb,OAAM;oFACN,IAAG;oFACH,WAAU;;;;;;8FAEZ,8OAAC,iIAAA,CAAA,QAAK;oFACJ,SAAQ;oFACR,WAAU;8FACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAUX,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,8OAAC;kEACC,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFAGxC,8OAAC,kIAAA,CAAA,SAAM;wEACL,OAAO;wEACP,eAAe;;0FAEf,8OAAC,kIAAA,CAAA,gBAAa;gFAAC,WAAU;0FACvB,cAAA,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,kIAAA,CAAA,cAAW;;;;;sGACZ,8OAAC,oNAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;0FAG3B,8OAAC,kIAAA,CAAA,gBAAa;;kGACZ,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,8OAAC,kIAAA,CAAA,aAAU;wFAAC,OAAM;kGAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAa9C,cAAc,aAAa,uBAAuB,wBACjD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,WAAU;oEACV,SAAS;wEACP,sBAAsB;wEACtB,sBAAsB;oEACxB;;sFAEA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;8FACjB,8OAAC;oFAAK,WAAU;8FAAwB;;;;;;;;;;;;sFAI1C,8OAAC,sNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;8EAG1B,8OAAC;oEACC,WAAU;oEACV,SAAS;wEACP,sBAAsB;wEACtB,sBAAsB;oEACxB;;sFAEA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;oFAAK,WAAU;8FAAwB;;;;;;;;;;;;sFAI1C,8OAAC,sNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;8EAG1B,8OAAC;oEACC,WAAU;oEACV,SAAS;wEACP,sBAAsB;wEACtB,sBAAsB;oEACxB;;sFAEA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,8MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,8OAAC;oFAAK,WAAU;8FAAwB;;;;;;;;;;;;sFAI1C,8OAAC,sNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOhC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;8EAI1C,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAS;oEACT,iBAAiB;oEACjB,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAOlB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,SAAS,IACP,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;;8EAGb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EAGxC,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAM9B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;8EAI1C,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAS;oEACT,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCASrB,cAAc,aACb,uBAAuB,4BACrB,8OAAC;wCAAI,WAAU;;4CACZ,uBAAuB,yBACtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS;oEACP,sBAAsB;oEACtB,sBAAsB;gEACxB;gEACA,WAAU;0EAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;0EAEzB,8OAAC;gEAAG,WAAU;0EAAwC;;;;;;;;;;;;kEAKxD,8OAAC,gJAAA,CAAA,UAAe;wDACd,cAAc,MAAM;wDACpB,WAAW;4DACT,sBAAsB;4DACtB,sBAAsB;wDACxB;;;;;;;;;;;;4CAKL,uBAAuB,yBACtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS;oEACP,sBAAsB;oEACtB,sBAAsB;gEACxB;gEACA,WAAU;0EAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;0EAEzB,8OAAC;gEAAG,WAAU;0EAAwC;;;;;;;;;;;;kEAKxD,8OAAC,gJAAA,CAAA,UAAe;wDACd,cAAc,MAAM;wDACpB,WAAW;4DACT,sBAAsB;4DACtB,sBAAsB;wDACxB;;;;;;;;;;;;4CAKL,uBAAuB,4BACtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS;oEACP,sBAAsB;oEACtB,sBAAsB;gEACxB;gEACA,WAAU;0EAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;0EAEzB,8OAAC;gEAAG,WAAU;0EAAwC;;;;;;;;;;;;kEAKxD,8OAAC,oJAAA,CAAA,UAAkB;wDACjB,WAAW;4DACT,sBAAsB;4DACtB,sBAAsB;wDACxB;;;;;;;;;;;;;;;;;;oCAQX,cAAc,6BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAG3D,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;oCAOxC,cAAc,iCACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAG3D,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;oCAOxC,cAAc,4BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAG3D,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;oCAOxC,cAAc,6BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAG3D,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzD", "debugId": null}}, {"offset": {"line": 3187, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { buttonVariants } from \"@/components/ui/button\";\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root;\r\n\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger;\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal;\r\n\r\nconst AlertDialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className,\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n));\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName;\r\n\r\nconst AlertDialogContent = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  </AlertDialogPortal>\r\n));\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName;\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nAlertDialogHeader.displayName = \"AlertDialogHeader\";\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nAlertDialogFooter.displayName = \"AlertDialogFooter\";\r\n\r\nconst AlertDialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName;\r\n\r\nconst AlertDialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogDescription.displayName =\r\n  AlertDialogPrimitive.Description.displayName;\r\n\r\nconst AlertDialogAction = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action\r\n    ref={ref}\r\n    className={cn(buttonVariants(), className)}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName;\r\n\r\nconst AlertDialogCancel = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    ref={ref}\r\n    className={cn(\r\n      buttonVariants({ variant: \"outline\" }),\r\n      \"mt-2 sm:mt-0\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName;\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,2KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,2KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,2KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,2KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,2KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3314, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/call/CallButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Phone, Video } from \"lucide-react\";\r\nimport { User, Group } from \"@/types/base\";\r\nimport { toast } from \"sonner\";\r\n// import { useCallStore } from \"@/stores/callStore\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ninterface CallButtonProps {\r\n  target: User | Group;\r\n  targetType?: \"USER\" | \"GROUP\";\r\n  variant?: \"icon\" | \"default\";\r\n  size?: \"sm\" | \"md\" | \"lg\";\r\n  showVideoCall?: boolean;\r\n}\r\n\r\nexport default function CallButton({\r\n  target,\r\n  targetType = \"USER\",\r\n  variant = \"default\",\r\n  size = \"md\",\r\n  showVideoCall = true,\r\n}: CallButtonProps) {\r\n  // const { startCall, startGroupCall, isLoading } = useCallStore();\r\n  const router = useRouter();\r\n\r\n  // Chức năng gọi điện đã bị vô hiệu hóa\r\n  const handleCall = async () => {\r\n    toast.info(\r\n      \"Chức năng gọi điện đang được phát triển và sẽ sớm được cập nhật.\",\r\n    );\r\n  };\r\n\r\n  // Chức năng gọi video đã bị vô hiệu hóa\r\n  const handleVideoCall = async () => {\r\n    toast.info(\r\n      \"Chức năng gọi video đang được phát triển và sẽ sớm được cập nhật.\",\r\n    );\r\n  };\r\n\r\n  if (variant === \"icon\") {\r\n    return (\r\n      <>\r\n        <div className=\"flex gap-2\">\r\n          <Button\r\n            onClick={handleCall}\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            disabled={true}\r\n            className={`rounded-full ${size === \"sm\" ? \"h-8 w-8\" : size === \"lg\" ? \"h-12 w-12\" : \"h-10 w-10\"}`}\r\n          >\r\n            <Phone\r\n              className={`${size === \"sm\" ? \"h-4 w-4\" : size === \"lg\" ? \"h-6 w-6\" : \"h-5 w-5\"}`}\r\n            />\r\n          </Button>\r\n\r\n          {showVideoCall && (\r\n            <Button\r\n              onClick={handleVideoCall}\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              disabled={true}\r\n              className={`rounded-full ${size === \"sm\" ? \"h-8 w-8\" : size === \"lg\" ? \"h-12 w-12\" : \"h-10 w-10\"}`}\r\n            >\r\n              <Video\r\n                className={`${size === \"sm\" ? \"h-4 w-4\" : size === \"lg\" ? \"h-6 w-6\" : \"h-5 w-5\"}`}\r\n              />\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex gap-2\">\r\n        <Button\r\n          onClick={handleCall}\r\n          variant=\"outline\"\r\n          disabled={true}\r\n          className={`\r\n            flex items-center gap-2\r\n            ${size === \"sm\" ? \"text-xs py-1 px-2\" : size === \"lg\" ? \"text-base py-2 px-4\" : \"text-sm py-1.5 px-3\"}\r\n          `}\r\n        >\r\n          <Phone\r\n            className={`${size === \"sm\" ? \"h-3 w-3\" : size === \"lg\" ? \"h-5 w-5\" : \"h-4 w-4\"}`}\r\n          />\r\n          Gọi điện\r\n        </Button>\r\n\r\n        {showVideoCall && (\r\n          <Button\r\n            onClick={handleVideoCall}\r\n            variant=\"outline\"\r\n            disabled={true}\r\n            className={`\r\n              flex items-center gap-2\r\n              ${size === \"sm\" ? \"text-xs py-1 px-2\" : size === \"lg\" ? \"text-base py-2 px-4\" : \"text-sm py-1.5 px-3\"}\r\n            `}\r\n          >\r\n            <Video\r\n              className={`${size === \"sm\" ? \"h-3 w-3\" : size === \"lg\" ? \"h-5 w-5\" : \"h-4 w-4\"}`}\r\n            />\r\n            Gọi video\r\n          </Button>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AACA,qDAAqD;AACrD;AAPA;;;;;;AAiBe,SAAS,WAAW,EACjC,MAAM,EACN,aAAa,MAAM,EACnB,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,gBAAgB,IAAI,EACJ;IAChB,mEAAmE;IACnE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,uCAAuC;IACvC,MAAM,aAAa;QACjB,wIAAA,CAAA,QAAK,CAAC,IAAI,CACR;IAEJ;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,wIAAA,CAAA,QAAK,CAAC,IAAI,CACR;IAEJ;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE;sBACE,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAQ;wBACR,MAAK;wBACL,UAAU;wBACV,WAAW,CAAC,aAAa,EAAE,SAAS,OAAO,YAAY,SAAS,OAAO,cAAc,aAAa;kCAElG,cAAA,8OAAC,oMAAA,CAAA,QAAK;4BACJ,WAAW,GAAG,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,WAAW;;;;;;;;;;;oBAIpF,+BACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAQ;wBACR,MAAK;wBACL,UAAU;wBACV,WAAW,CAAC,aAAa,EAAE,SAAS,OAAO,YAAY,SAAS,OAAO,cAAc,aAAa;kCAElG,cAAA,8OAAC,oMAAA,CAAA,QAAK;4BACJ,WAAW,GAAG,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,WAAW;;;;;;;;;;;;;;;;;;IAO/F;IAEA,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,SAAQ;oBACR,UAAU;oBACV,WAAW,CAAC;;YAEV,EAAE,SAAS,OAAO,sBAAsB,SAAS,OAAO,wBAAwB,sBAAsB;UACxG,CAAC;;sCAED,8OAAC,oMAAA,CAAA,QAAK;4BACJ,WAAW,GAAG,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,WAAW;;;;;;wBACjF;;;;;;;gBAIH,+BACC,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,SAAQ;oBACR,UAAU;oBACV,WAAW,CAAC;;cAEV,EAAE,SAAS,OAAO,sBAAsB,SAAS,OAAO,wBAAwB,sBAAsB;YACxG,CAAC;;sCAED,8OAAC,oMAAA,CAAA,QAAK;4BACJ,WAAW,GAAG,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,WAAW;;;;;;wBACjF;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 3454, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/profile/RefreshUserDataButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { RefreshCw } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\nimport { refreshUserData } from \"@/hooks/useUserDataSync\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface RefreshUserDataButtonProps {\r\n  className?: string;\r\n  variant?:\r\n    | \"default\"\r\n    | \"destructive\"\r\n    | \"outline\"\r\n    | \"secondary\"\r\n    | \"ghost\"\r\n    | \"link\";\r\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\";\r\n  showText?: boolean;\r\n}\r\n\r\nexport default function RefreshUserDataButton({\r\n  className = \"\",\r\n  variant = \"ghost\",\r\n  size = \"icon\",\r\n  showText = false,\r\n}: RefreshUserDataButtonProps) {\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n\r\n  const handleRefresh = async () => {\r\n    if (isRefreshing) return;\r\n\r\n    setIsRefreshing(true);\r\n    try {\r\n      const success = await refreshUserData();\r\n      if (success) {\r\n        toast.success(\"Dữ liệu người dùng đã được cập nhật\");\r\n      } else {\r\n        toast.error(\"Không thể cập nhật dữ liệu người dùng\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Lỗi khi làm mới dữ liệu:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi cập nhật dữ liệu\");\r\n    } finally {\r\n      setIsRefreshing(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Button\r\n      variant={variant}\r\n      size={size}\r\n      className={className}\r\n      onClick={handleRefresh}\r\n      disabled={isRefreshing}\r\n    >\r\n      <RefreshCw className={`h-4 w-4 ${isRefreshing ? \"animate-spin\" : \"\"}`} />\r\n      {showText && <span className=\"ml-2\">Làm mới</span>}\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAqBe,SAAS,sBAAsB,EAC5C,YAAY,EAAE,EACd,UAAU,OAAO,EACjB,OAAO,MAAM,EACb,WAAW,KAAK,EACW;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB,IAAI,cAAc;QAElB,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU,MAAM,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD;YACpC,IAAI,SAAS;gBACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,MAAM;QACN,WAAW;QACX,SAAS;QACT,UAAU;;0BAEV,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,iBAAiB,IAAI;;;;;;YACpE,0BAAY,8OAAC;gBAAK,WAAU;0BAAO;;;;;;;;;;;;AAG1C", "debugId": null}}, {"offset": {"line": 3524, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/friend.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport { createAxiosInstance } from \"@/lib/axios\";\r\n// No need to import Friend from @/types/base\r\nimport {\r\n  getCachedRelationship,\r\n  cacheRelationship,\r\n  removeCachedRelationship,\r\n  clearRelationshipCache,\r\n} from \"@/utils/relationshipCache\";\r\n\r\n// Define types based on the API response\r\ninterface UserInfo {\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\ninterface FriendInfo {\r\n  id: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  userInfo: UserInfo;\r\n}\r\n\r\ninterface FriendshipResponse {\r\n  friendshipId: string;\r\n  friend: FriendInfo;\r\n  since: string;\r\n}\r\n\r\ninterface FriendRequest {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  sender: FriendInfo;\r\n  receiver: FriendInfo;\r\n  introduce?: string;\r\n}\r\n\r\n// Simplified Friend type for UI components\r\nexport interface SimpleFriend {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\n// Define types based on the API response\r\n\r\ninterface FriendInfo {\r\n  id: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  userInfo: UserInfo;\r\n}\r\n\r\ninterface FriendshipResponse {\r\n  friendshipId: string;\r\n  friend: FriendInfo;\r\n  since: string;\r\n}\r\n\r\ninterface FriendRequest {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  sender: FriendInfo;\r\n  receiver: FriendInfo;\r\n  introduce?: string;\r\n}\r\n\r\n// Simplified Friend type for UI components\r\nexport interface SimpleFriend {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\n// Lấy danh sách bạn bè của người dùng hiện tại\r\nexport async function getFriendsList(token?: string) {\r\n  try {\r\n    console.log(\r\n      \"Token received in getFriendsList:\",\r\n      token ? `Token exists: ${token.substring(0, 10)}...` : \"No token\",\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n    console.log(\r\n      \"Authorization header:\",\r\n      serverAxios.defaults.headers.common[\"Authorization\"],\r\n    );\r\n    const response = await serverAxios.get(\"/friends/list\");\r\n    const friendships: FriendshipResponse[] = response.data;\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const friends: SimpleFriend[] = friendships.map((friendship) => ({\r\n      id: friendship.friend.id,\r\n      fullName: friendship.friend.userInfo.fullName,\r\n      profilePictureUrl: friendship.friend.userInfo.profilePictureUrl,\r\n      statusMessage: friendship.friend.userInfo.statusMessage,\r\n      lastSeen: friendship.friend.userInfo.lastSeen,\r\n      email: friendship.friend.email,\r\n      phoneNumber: friendship.friend.phoneNumber,\r\n      gender: friendship.friend.userInfo.gender,\r\n      bio: friendship.friend.userInfo.bio,\r\n      dateOfBirth: friendship.friend.userInfo.dateOfBirth,\r\n    }));\r\n\r\n    return { success: true, friends };\r\n  } catch (error) {\r\n    console.error(\"Get friends list failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy danh sách lời mời kết bạn đã nhận\r\nexport async function getReceivedFriendRequests(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/requests/received\");\r\n\r\n    // Log raw API response\r\n    console.log(\"Raw received friend requests:\", response.data);\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const requests = response.data.map((request: FriendRequest) => ({\r\n      id: request.id,\r\n      fullName: request.sender.userInfo.fullName,\r\n      profilePictureUrl: request.sender.userInfo.profilePictureUrl,\r\n      // Get the introduce message from the API response\r\n      message: request.introduce || \"\",\r\n      timeAgo: new Date(request.createdAt).toLocaleDateString(),\r\n      // Add senderId for fetching complete user data\r\n      senderId: request.sender.id,\r\n    }));\r\n\r\n    return { success: true, requests };\r\n  } catch (error) {\r\n    console.error(\"Get received friend requests failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy danh sách lời mời kết bạn đã gửi\r\nexport async function getSentFriendRequests(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/requests/sent\");\r\n\r\n    // Log raw API response\r\n    console.log(\"Raw sent friend requests:\", response.data);\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const requests = response.data.map((request: FriendRequest) => ({\r\n      id: request.id,\r\n      fullName: request.receiver.userInfo.fullName,\r\n      profilePictureUrl: request.receiver.userInfo.profilePictureUrl,\r\n      timeAgo: new Date(request.createdAt).toLocaleDateString(),\r\n      // Add receiverId for fetching complete user data\r\n      receiverId: request.receiver.id,\r\n    }));\r\n\r\n    return { success: true, requests };\r\n  } catch (error) {\r\n    console.error(\"Get sent friend requests failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Gửi lời mời kết bạn\r\nexport async function sendFriendRequest(\r\n  userId: string,\r\n  introduce?: string,\r\n  token?: string,\r\n) {\r\n  try {\r\n    console.log(\r\n      `Sending friend request to user ${userId} with token: ${!!token}`,\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n\r\n    // Tạo payload theo đúng format API yêu cầu\r\n    const payload: { receiverId: string; introduce?: string } = {\r\n      receiverId: userId,\r\n    };\r\n\r\n    // Thêm introduce nếu có\r\n    if (introduce && introduce.trim()) {\r\n      payload.introduce = introduce.trim();\r\n    }\r\n\r\n    console.log(\"Friend request payload:\", payload);\r\n    const response = await serverAxios.post(\"/friends/request\", payload);\r\n    console.log(\"Friend request response:\", response.data);\r\n\r\n    // Update relationship cache to reflect the new pending status\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Send friend request failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    // Network errors or other errors\r\n    if (error instanceof Error) {\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Phản hồi lời mời kết bạn (chấp nhận, từ chối, block)\r\nexport async function respondToFriendRequest(\r\n  requestId: string,\r\n  status: \"ACCEPTED\" | \"DECLINED\" | \"BLOCKED\",\r\n  token?: string,\r\n) {\r\n  try {\r\n    console.log(\r\n      `respondToFriendRequest: requestId=${requestId}, status=${status}, hasToken=${!!token}`,\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n    const payload = {\r\n      requestId,\r\n      status,\r\n    };\r\n    console.log(\"Request payload:\", payload);\r\n    const response = await serverAxios.put(\"/friends/respond\", payload);\r\n    console.log(\"API response:\", response.data);\r\n\r\n    // Clear all relationship caches since we don't know which user this affects\r\n    // This is a simple approach - a more sophisticated one would track which user's request this is\r\n    clearRelationshipCache();\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Respond to friend request failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy lời mời kết bạn\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Chấp nhận lời mời kết bạn (wrapper function for backward compatibility)\r\nexport async function acceptFriendRequest(requestId: string, token?: string) {\r\n  return respondToFriendRequest(requestId, \"ACCEPTED\", token);\r\n}\r\n\r\n// Từ chối lời mời kết bạn (wrapper function for backward compatibility)\r\nexport async function rejectFriendRequest(requestId: string, token?: string) {\r\n  console.log(\r\n    \"rejectFriendRequest called with requestId:\",\r\n    requestId,\r\n    \"and token:\",\r\n    !!token,\r\n  );\r\n  const result = await respondToFriendRequest(requestId, \"DECLINED\", token);\r\n  console.log(\"respondToFriendRequest result:\", result);\r\n  return result;\r\n}\r\n\r\n// Xóa bạn bè\r\nexport async function removeFriend(friendId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/${friendId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(friendId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Remove friend failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    // Network errors or other errors\r\n    if (error instanceof Error) {\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Hủy lời mời kết bạn đã gửi\r\nexport async function cancelFriendRequest(requestId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/request/${requestId}`);\r\n\r\n    // Clear all relationship caches since we don't know which user this affects\r\n    clearRelationshipCache();\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Cancel friend request failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Chặn người dùng\r\nexport async function blockUser(userId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.post(`/friends/block/${userId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Block user failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Bỏ chặn người dùng\r\nexport async function unblockUser(userId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/block/${userId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Unblock user failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Define type for blocked user response\r\ninterface BlockedUserResponse {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  receiver: FriendInfo;\r\n}\r\n\r\n// Lấy danh sách người dùng đã chặn\r\nexport async function getBlockedUsers(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/blocked\");\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const users: SimpleFriend[] = response.data.map(\r\n      (item: BlockedUserResponse) => ({\r\n        id: item.receiver.id,\r\n        fullName: item.receiver.userInfo.fullName,\r\n        profilePictureUrl: item.receiver.userInfo.profilePictureUrl,\r\n        email: item.receiver.email,\r\n        phoneNumber: item.receiver.phoneNumber,\r\n      }),\r\n    );\r\n\r\n    return { success: true, users };\r\n  } catch (error) {\r\n    console.error(\"Get blocked users failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Batch fetch relationships for multiple users is implemented here\r\n// The cache functions are imported from utils/relationshipCache.ts\r\n\r\n// Batch fetch relationships for multiple users\r\nexport async function batchGetRelationships(userIds: string[], token?: string) {\r\n  // Filter out duplicate IDs\r\n  const uniqueIds = [...new Set(userIds)];\r\n\r\n  // Check which relationships are already in cache\r\n  const cachedRelationships: Record<string, { status: string }> = {};\r\n  const idsToFetch: string[] = [];\r\n\r\n  uniqueIds.forEach((id) => {\r\n    const cachedData = getCachedRelationship(id);\r\n    if (cachedData) {\r\n      cachedRelationships[id] = cachedData;\r\n    } else {\r\n      idsToFetch.push(id);\r\n    }\r\n  });\r\n\r\n  // If all relationships are in cache, return immediately\r\n  if (idsToFetch.length === 0) {\r\n    console.log(`All ${uniqueIds.length} relationships found in cache`);\r\n    return { success: true, relationships: cachedRelationships };\r\n  }\r\n\r\n  // Otherwise, fetch the remaining relationships\r\n  try {\r\n    console.log(`Batch fetching ${idsToFetch.length} relationships`);\r\n\r\n    // Fetch each relationship individually (could be optimized with a batch API endpoint)\r\n    const fetchPromises = idsToFetch.map((id) => getRelationship(id, token));\r\n    const results = await Promise.all(fetchPromises);\r\n\r\n    // Process results\r\n    results.forEach((result, index) => {\r\n      if (result.success && result.data) {\r\n        const userId = idsToFetch[index];\r\n        cachedRelationships[userId] = result.data;\r\n      }\r\n    });\r\n\r\n    return { success: true, relationships: cachedRelationships };\r\n  } catch (error) {\r\n    console.error(\"Batch get relationships failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n      relationships: cachedRelationships, // Return any cached relationships we did find\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy mối quan hệ với một người dùng cụ thể\r\nexport async function getRelationship(targetId: string, token?: string) {\r\n  try {\r\n    // Check if relationship data is in cache and still valid\r\n    const cachedData = getCachedRelationship(targetId);\r\n    if (cachedData) {\r\n      console.log(`Using cached relationship data for user ID: ${targetId}`);\r\n      return { success: true, data: cachedData };\r\n    }\r\n\r\n    // Sử dụng serverAxios để gửi token xác thực\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(`/friends/relationship/${targetId}`);\r\n    console.log(\"Relationship response:\", response.data);\r\n\r\n    // Store relationship data in cache\r\n    cacheRelationship(targetId, response.data);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Get relationship failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAilBsB,kBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3537, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/friend.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport { createAxiosInstance } from \"@/lib/axios\";\r\n// No need to import Friend from @/types/base\r\nimport {\r\n  getCachedRelationship,\r\n  cacheRelationship,\r\n  removeCachedRelationship,\r\n  clearRelationshipCache,\r\n} from \"@/utils/relationshipCache\";\r\n\r\n// Define types based on the API response\r\ninterface UserInfo {\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\ninterface FriendInfo {\r\n  id: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  userInfo: UserInfo;\r\n}\r\n\r\ninterface FriendshipResponse {\r\n  friendshipId: string;\r\n  friend: FriendInfo;\r\n  since: string;\r\n}\r\n\r\ninterface FriendRequest {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  sender: FriendInfo;\r\n  receiver: FriendInfo;\r\n  introduce?: string;\r\n}\r\n\r\n// Simplified Friend type for UI components\r\nexport interface SimpleFriend {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\n// Define types based on the API response\r\n\r\ninterface FriendInfo {\r\n  id: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  userInfo: UserInfo;\r\n}\r\n\r\ninterface FriendshipResponse {\r\n  friendshipId: string;\r\n  friend: FriendInfo;\r\n  since: string;\r\n}\r\n\r\ninterface FriendRequest {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  sender: FriendInfo;\r\n  receiver: FriendInfo;\r\n  introduce?: string;\r\n}\r\n\r\n// Simplified Friend type for UI components\r\nexport interface SimpleFriend {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\n// Lấy danh sách bạn bè của người dùng hiện tại\r\nexport async function getFriendsList(token?: string) {\r\n  try {\r\n    console.log(\r\n      \"Token received in getFriendsList:\",\r\n      token ? `Token exists: ${token.substring(0, 10)}...` : \"No token\",\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n    console.log(\r\n      \"Authorization header:\",\r\n      serverAxios.defaults.headers.common[\"Authorization\"],\r\n    );\r\n    const response = await serverAxios.get(\"/friends/list\");\r\n    const friendships: FriendshipResponse[] = response.data;\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const friends: SimpleFriend[] = friendships.map((friendship) => ({\r\n      id: friendship.friend.id,\r\n      fullName: friendship.friend.userInfo.fullName,\r\n      profilePictureUrl: friendship.friend.userInfo.profilePictureUrl,\r\n      statusMessage: friendship.friend.userInfo.statusMessage,\r\n      lastSeen: friendship.friend.userInfo.lastSeen,\r\n      email: friendship.friend.email,\r\n      phoneNumber: friendship.friend.phoneNumber,\r\n      gender: friendship.friend.userInfo.gender,\r\n      bio: friendship.friend.userInfo.bio,\r\n      dateOfBirth: friendship.friend.userInfo.dateOfBirth,\r\n    }));\r\n\r\n    return { success: true, friends };\r\n  } catch (error) {\r\n    console.error(\"Get friends list failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy danh sách lời mời kết bạn đã nhận\r\nexport async function getReceivedFriendRequests(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/requests/received\");\r\n\r\n    // Log raw API response\r\n    console.log(\"Raw received friend requests:\", response.data);\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const requests = response.data.map((request: FriendRequest) => ({\r\n      id: request.id,\r\n      fullName: request.sender.userInfo.fullName,\r\n      profilePictureUrl: request.sender.userInfo.profilePictureUrl,\r\n      // Get the introduce message from the API response\r\n      message: request.introduce || \"\",\r\n      timeAgo: new Date(request.createdAt).toLocaleDateString(),\r\n      // Add senderId for fetching complete user data\r\n      senderId: request.sender.id,\r\n    }));\r\n\r\n    return { success: true, requests };\r\n  } catch (error) {\r\n    console.error(\"Get received friend requests failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy danh sách lời mời kết bạn đã gửi\r\nexport async function getSentFriendRequests(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/requests/sent\");\r\n\r\n    // Log raw API response\r\n    console.log(\"Raw sent friend requests:\", response.data);\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const requests = response.data.map((request: FriendRequest) => ({\r\n      id: request.id,\r\n      fullName: request.receiver.userInfo.fullName,\r\n      profilePictureUrl: request.receiver.userInfo.profilePictureUrl,\r\n      timeAgo: new Date(request.createdAt).toLocaleDateString(),\r\n      // Add receiverId for fetching complete user data\r\n      receiverId: request.receiver.id,\r\n    }));\r\n\r\n    return { success: true, requests };\r\n  } catch (error) {\r\n    console.error(\"Get sent friend requests failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Gửi lời mời kết bạn\r\nexport async function sendFriendRequest(\r\n  userId: string,\r\n  introduce?: string,\r\n  token?: string,\r\n) {\r\n  try {\r\n    console.log(\r\n      `Sending friend request to user ${userId} with token: ${!!token}`,\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n\r\n    // Tạo payload theo đúng format API yêu cầu\r\n    const payload: { receiverId: string; introduce?: string } = {\r\n      receiverId: userId,\r\n    };\r\n\r\n    // Thêm introduce nếu có\r\n    if (introduce && introduce.trim()) {\r\n      payload.introduce = introduce.trim();\r\n    }\r\n\r\n    console.log(\"Friend request payload:\", payload);\r\n    const response = await serverAxios.post(\"/friends/request\", payload);\r\n    console.log(\"Friend request response:\", response.data);\r\n\r\n    // Update relationship cache to reflect the new pending status\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Send friend request failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    // Network errors or other errors\r\n    if (error instanceof Error) {\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Phản hồi lời mời kết bạn (chấp nhận, từ chối, block)\r\nexport async function respondToFriendRequest(\r\n  requestId: string,\r\n  status: \"ACCEPTED\" | \"DECLINED\" | \"BLOCKED\",\r\n  token?: string,\r\n) {\r\n  try {\r\n    console.log(\r\n      `respondToFriendRequest: requestId=${requestId}, status=${status}, hasToken=${!!token}`,\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n    const payload = {\r\n      requestId,\r\n      status,\r\n    };\r\n    console.log(\"Request payload:\", payload);\r\n    const response = await serverAxios.put(\"/friends/respond\", payload);\r\n    console.log(\"API response:\", response.data);\r\n\r\n    // Clear all relationship caches since we don't know which user this affects\r\n    // This is a simple approach - a more sophisticated one would track which user's request this is\r\n    clearRelationshipCache();\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Respond to friend request failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy lời mời kết bạn\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Chấp nhận lời mời kết bạn (wrapper function for backward compatibility)\r\nexport async function acceptFriendRequest(requestId: string, token?: string) {\r\n  return respondToFriendRequest(requestId, \"ACCEPTED\", token);\r\n}\r\n\r\n// Từ chối lời mời kết bạn (wrapper function for backward compatibility)\r\nexport async function rejectFriendRequest(requestId: string, token?: string) {\r\n  console.log(\r\n    \"rejectFriendRequest called with requestId:\",\r\n    requestId,\r\n    \"and token:\",\r\n    !!token,\r\n  );\r\n  const result = await respondToFriendRequest(requestId, \"DECLINED\", token);\r\n  console.log(\"respondToFriendRequest result:\", result);\r\n  return result;\r\n}\r\n\r\n// Xóa bạn bè\r\nexport async function removeFriend(friendId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/${friendId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(friendId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Remove friend failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    // Network errors or other errors\r\n    if (error instanceof Error) {\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Hủy lời mời kết bạn đã gửi\r\nexport async function cancelFriendRequest(requestId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/request/${requestId}`);\r\n\r\n    // Clear all relationship caches since we don't know which user this affects\r\n    clearRelationshipCache();\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Cancel friend request failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Chặn người dùng\r\nexport async function blockUser(userId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.post(`/friends/block/${userId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Block user failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Bỏ chặn người dùng\r\nexport async function unblockUser(userId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/block/${userId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Unblock user failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Define type for blocked user response\r\ninterface BlockedUserResponse {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  receiver: FriendInfo;\r\n}\r\n\r\n// Lấy danh sách người dùng đã chặn\r\nexport async function getBlockedUsers(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/blocked\");\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const users: SimpleFriend[] = response.data.map(\r\n      (item: BlockedUserResponse) => ({\r\n        id: item.receiver.id,\r\n        fullName: item.receiver.userInfo.fullName,\r\n        profilePictureUrl: item.receiver.userInfo.profilePictureUrl,\r\n        email: item.receiver.email,\r\n        phoneNumber: item.receiver.phoneNumber,\r\n      }),\r\n    );\r\n\r\n    return { success: true, users };\r\n  } catch (error) {\r\n    console.error(\"Get blocked users failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Batch fetch relationships for multiple users is implemented here\r\n// The cache functions are imported from utils/relationshipCache.ts\r\n\r\n// Batch fetch relationships for multiple users\r\nexport async function batchGetRelationships(userIds: string[], token?: string) {\r\n  // Filter out duplicate IDs\r\n  const uniqueIds = [...new Set(userIds)];\r\n\r\n  // Check which relationships are already in cache\r\n  const cachedRelationships: Record<string, { status: string }> = {};\r\n  const idsToFetch: string[] = [];\r\n\r\n  uniqueIds.forEach((id) => {\r\n    const cachedData = getCachedRelationship(id);\r\n    if (cachedData) {\r\n      cachedRelationships[id] = cachedData;\r\n    } else {\r\n      idsToFetch.push(id);\r\n    }\r\n  });\r\n\r\n  // If all relationships are in cache, return immediately\r\n  if (idsToFetch.length === 0) {\r\n    console.log(`All ${uniqueIds.length} relationships found in cache`);\r\n    return { success: true, relationships: cachedRelationships };\r\n  }\r\n\r\n  // Otherwise, fetch the remaining relationships\r\n  try {\r\n    console.log(`Batch fetching ${idsToFetch.length} relationships`);\r\n\r\n    // Fetch each relationship individually (could be optimized with a batch API endpoint)\r\n    const fetchPromises = idsToFetch.map((id) => getRelationship(id, token));\r\n    const results = await Promise.all(fetchPromises);\r\n\r\n    // Process results\r\n    results.forEach((result, index) => {\r\n      if (result.success && result.data) {\r\n        const userId = idsToFetch[index];\r\n        cachedRelationships[userId] = result.data;\r\n      }\r\n    });\r\n\r\n    return { success: true, relationships: cachedRelationships };\r\n  } catch (error) {\r\n    console.error(\"Batch get relationships failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n      relationships: cachedRelationships, // Return any cached relationships we did find\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy mối quan hệ với một người dùng cụ thể\r\nexport async function getRelationship(targetId: string, token?: string) {\r\n  try {\r\n    // Check if relationship data is in cache and still valid\r\n    const cachedData = getCachedRelationship(targetId);\r\n    if (cachedData) {\r\n      console.log(`Using cached relationship data for user ID: ${targetId}`);\r\n      return { success: true, data: cachedData };\r\n    }\r\n\r\n    // Sử dụng serverAxios để gửi token xác thực\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(`/friends/relationship/${targetId}`);\r\n    console.log(\"Relationship response:\", response.data);\r\n\r\n    // Store relationship data in cache\r\n    cacheRelationship(targetId, response.data);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Get relationship failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0MsB,oBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3550, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/friend.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport { createAxiosInstance } from \"@/lib/axios\";\r\n// No need to import Friend from @/types/base\r\nimport {\r\n  getCachedRelationship,\r\n  cacheRelationship,\r\n  removeCachedRelationship,\r\n  clearRelationshipCache,\r\n} from \"@/utils/relationshipCache\";\r\n\r\n// Define types based on the API response\r\ninterface UserInfo {\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\ninterface FriendInfo {\r\n  id: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  userInfo: UserInfo;\r\n}\r\n\r\ninterface FriendshipResponse {\r\n  friendshipId: string;\r\n  friend: FriendInfo;\r\n  since: string;\r\n}\r\n\r\ninterface FriendRequest {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  sender: FriendInfo;\r\n  receiver: FriendInfo;\r\n  introduce?: string;\r\n}\r\n\r\n// Simplified Friend type for UI components\r\nexport interface SimpleFriend {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\n// Define types based on the API response\r\n\r\ninterface FriendInfo {\r\n  id: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  userInfo: UserInfo;\r\n}\r\n\r\ninterface FriendshipResponse {\r\n  friendshipId: string;\r\n  friend: FriendInfo;\r\n  since: string;\r\n}\r\n\r\ninterface FriendRequest {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  sender: FriendInfo;\r\n  receiver: FriendInfo;\r\n  introduce?: string;\r\n}\r\n\r\n// Simplified Friend type for UI components\r\nexport interface SimpleFriend {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\n// Lấy danh sách bạn bè của người dùng hiện tại\r\nexport async function getFriendsList(token?: string) {\r\n  try {\r\n    console.log(\r\n      \"Token received in getFriendsList:\",\r\n      token ? `Token exists: ${token.substring(0, 10)}...` : \"No token\",\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n    console.log(\r\n      \"Authorization header:\",\r\n      serverAxios.defaults.headers.common[\"Authorization\"],\r\n    );\r\n    const response = await serverAxios.get(\"/friends/list\");\r\n    const friendships: FriendshipResponse[] = response.data;\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const friends: SimpleFriend[] = friendships.map((friendship) => ({\r\n      id: friendship.friend.id,\r\n      fullName: friendship.friend.userInfo.fullName,\r\n      profilePictureUrl: friendship.friend.userInfo.profilePictureUrl,\r\n      statusMessage: friendship.friend.userInfo.statusMessage,\r\n      lastSeen: friendship.friend.userInfo.lastSeen,\r\n      email: friendship.friend.email,\r\n      phoneNumber: friendship.friend.phoneNumber,\r\n      gender: friendship.friend.userInfo.gender,\r\n      bio: friendship.friend.userInfo.bio,\r\n      dateOfBirth: friendship.friend.userInfo.dateOfBirth,\r\n    }));\r\n\r\n    return { success: true, friends };\r\n  } catch (error) {\r\n    console.error(\"Get friends list failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy danh sách lời mời kết bạn đã nhận\r\nexport async function getReceivedFriendRequests(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/requests/received\");\r\n\r\n    // Log raw API response\r\n    console.log(\"Raw received friend requests:\", response.data);\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const requests = response.data.map((request: FriendRequest) => ({\r\n      id: request.id,\r\n      fullName: request.sender.userInfo.fullName,\r\n      profilePictureUrl: request.sender.userInfo.profilePictureUrl,\r\n      // Get the introduce message from the API response\r\n      message: request.introduce || \"\",\r\n      timeAgo: new Date(request.createdAt).toLocaleDateString(),\r\n      // Add senderId for fetching complete user data\r\n      senderId: request.sender.id,\r\n    }));\r\n\r\n    return { success: true, requests };\r\n  } catch (error) {\r\n    console.error(\"Get received friend requests failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy danh sách lời mời kết bạn đã gửi\r\nexport async function getSentFriendRequests(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/requests/sent\");\r\n\r\n    // Log raw API response\r\n    console.log(\"Raw sent friend requests:\", response.data);\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const requests = response.data.map((request: FriendRequest) => ({\r\n      id: request.id,\r\n      fullName: request.receiver.userInfo.fullName,\r\n      profilePictureUrl: request.receiver.userInfo.profilePictureUrl,\r\n      timeAgo: new Date(request.createdAt).toLocaleDateString(),\r\n      // Add receiverId for fetching complete user data\r\n      receiverId: request.receiver.id,\r\n    }));\r\n\r\n    return { success: true, requests };\r\n  } catch (error) {\r\n    console.error(\"Get sent friend requests failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Gửi lời mời kết bạn\r\nexport async function sendFriendRequest(\r\n  userId: string,\r\n  introduce?: string,\r\n  token?: string,\r\n) {\r\n  try {\r\n    console.log(\r\n      `Sending friend request to user ${userId} with token: ${!!token}`,\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n\r\n    // Tạo payload theo đúng format API yêu cầu\r\n    const payload: { receiverId: string; introduce?: string } = {\r\n      receiverId: userId,\r\n    };\r\n\r\n    // Thêm introduce nếu có\r\n    if (introduce && introduce.trim()) {\r\n      payload.introduce = introduce.trim();\r\n    }\r\n\r\n    console.log(\"Friend request payload:\", payload);\r\n    const response = await serverAxios.post(\"/friends/request\", payload);\r\n    console.log(\"Friend request response:\", response.data);\r\n\r\n    // Update relationship cache to reflect the new pending status\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Send friend request failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    // Network errors or other errors\r\n    if (error instanceof Error) {\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Phản hồi lời mời kết bạn (chấp nhận, từ chối, block)\r\nexport async function respondToFriendRequest(\r\n  requestId: string,\r\n  status: \"ACCEPTED\" | \"DECLINED\" | \"BLOCKED\",\r\n  token?: string,\r\n) {\r\n  try {\r\n    console.log(\r\n      `respondToFriendRequest: requestId=${requestId}, status=${status}, hasToken=${!!token}`,\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n    const payload = {\r\n      requestId,\r\n      status,\r\n    };\r\n    console.log(\"Request payload:\", payload);\r\n    const response = await serverAxios.put(\"/friends/respond\", payload);\r\n    console.log(\"API response:\", response.data);\r\n\r\n    // Clear all relationship caches since we don't know which user this affects\r\n    // This is a simple approach - a more sophisticated one would track which user's request this is\r\n    clearRelationshipCache();\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Respond to friend request failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy lời mời kết bạn\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Chấp nhận lời mời kết bạn (wrapper function for backward compatibility)\r\nexport async function acceptFriendRequest(requestId: string, token?: string) {\r\n  return respondToFriendRequest(requestId, \"ACCEPTED\", token);\r\n}\r\n\r\n// Từ chối lời mời kết bạn (wrapper function for backward compatibility)\r\nexport async function rejectFriendRequest(requestId: string, token?: string) {\r\n  console.log(\r\n    \"rejectFriendRequest called with requestId:\",\r\n    requestId,\r\n    \"and token:\",\r\n    !!token,\r\n  );\r\n  const result = await respondToFriendRequest(requestId, \"DECLINED\", token);\r\n  console.log(\"respondToFriendRequest result:\", result);\r\n  return result;\r\n}\r\n\r\n// Xóa bạn bè\r\nexport async function removeFriend(friendId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/${friendId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(friendId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Remove friend failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    // Network errors or other errors\r\n    if (error instanceof Error) {\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Hủy lời mời kết bạn đã gửi\r\nexport async function cancelFriendRequest(requestId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/request/${requestId}`);\r\n\r\n    // Clear all relationship caches since we don't know which user this affects\r\n    clearRelationshipCache();\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Cancel friend request failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Chặn người dùng\r\nexport async function blockUser(userId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.post(`/friends/block/${userId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Block user failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Bỏ chặn người dùng\r\nexport async function unblockUser(userId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/block/${userId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Unblock user failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Define type for blocked user response\r\ninterface BlockedUserResponse {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  receiver: FriendInfo;\r\n}\r\n\r\n// Lấy danh sách người dùng đã chặn\r\nexport async function getBlockedUsers(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/blocked\");\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const users: SimpleFriend[] = response.data.map(\r\n      (item: BlockedUserResponse) => ({\r\n        id: item.receiver.id,\r\n        fullName: item.receiver.userInfo.fullName,\r\n        profilePictureUrl: item.receiver.userInfo.profilePictureUrl,\r\n        email: item.receiver.email,\r\n        phoneNumber: item.receiver.phoneNumber,\r\n      }),\r\n    );\r\n\r\n    return { success: true, users };\r\n  } catch (error) {\r\n    console.error(\"Get blocked users failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Batch fetch relationships for multiple users is implemented here\r\n// The cache functions are imported from utils/relationshipCache.ts\r\n\r\n// Batch fetch relationships for multiple users\r\nexport async function batchGetRelationships(userIds: string[], token?: string) {\r\n  // Filter out duplicate IDs\r\n  const uniqueIds = [...new Set(userIds)];\r\n\r\n  // Check which relationships are already in cache\r\n  const cachedRelationships: Record<string, { status: string }> = {};\r\n  const idsToFetch: string[] = [];\r\n\r\n  uniqueIds.forEach((id) => {\r\n    const cachedData = getCachedRelationship(id);\r\n    if (cachedData) {\r\n      cachedRelationships[id] = cachedData;\r\n    } else {\r\n      idsToFetch.push(id);\r\n    }\r\n  });\r\n\r\n  // If all relationships are in cache, return immediately\r\n  if (idsToFetch.length === 0) {\r\n    console.log(`All ${uniqueIds.length} relationships found in cache`);\r\n    return { success: true, relationships: cachedRelationships };\r\n  }\r\n\r\n  // Otherwise, fetch the remaining relationships\r\n  try {\r\n    console.log(`Batch fetching ${idsToFetch.length} relationships`);\r\n\r\n    // Fetch each relationship individually (could be optimized with a batch API endpoint)\r\n    const fetchPromises = idsToFetch.map((id) => getRelationship(id, token));\r\n    const results = await Promise.all(fetchPromises);\r\n\r\n    // Process results\r\n    results.forEach((result, index) => {\r\n      if (result.success && result.data) {\r\n        const userId = idsToFetch[index];\r\n        cachedRelationships[userId] = result.data;\r\n      }\r\n    });\r\n\r\n    return { success: true, relationships: cachedRelationships };\r\n  } catch (error) {\r\n    console.error(\"Batch get relationships failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n      relationships: cachedRelationships, // Return any cached relationships we did find\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy mối quan hệ với một người dùng cụ thể\r\nexport async function getRelationship(targetId: string, token?: string) {\r\n  try {\r\n    // Check if relationship data is in cache and still valid\r\n    const cachedData = getCachedRelationship(targetId);\r\n    if (cachedData) {\r\n      console.log(`Using cached relationship data for user ID: ${targetId}`);\r\n      return { success: true, data: cachedData };\r\n    }\r\n\r\n    // Sử dụng serverAxios để gửi token xác thực\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(`/friends/relationship/${targetId}`);\r\n    console.log(\"Relationship response:\", response.data);\r\n\r\n    // Store relationship data in cache\r\n    cacheRelationship(targetId, response.data);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Get relationship failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0XsB,eAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3563, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/profile/ImageViewerDialog.tsx"], "sourcesContent": ["\"use client\";\r\nimport { <PERSON><PERSON>, DialogContent, DialogTitle } from \"@/components/ui/dialog\";\r\nimport Image from \"next/image\";\r\nimport { X, Download } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { VisuallyHidden } from \"@radix-ui/react-visually-hidden\";\r\n\r\ninterface ImageViewerDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  imageUrl: string;\r\n  alt?: string;\r\n}\r\n\r\nexport default function ImageViewerDialog({\r\n  isOpen,\r\n  onClose,\r\n  imageUrl,\r\n  alt = \"Hình ảnh\",\r\n}: ImageViewerDialogProps) {\r\n  // Function to handle image download\r\n  const handleDownload = async () => {\r\n    try {\r\n      // Fetch the image\r\n      const response = await fetch(imageUrl);\r\n      const blob = await response.blob();\r\n\r\n      // Create a download link\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement(\"a\");\r\n      link.href = url;\r\n\r\n      // Create a proper filename with .jpg extension\r\n      let filename = \"image.jpg\";\r\n\r\n      // Try to extract a meaningful name from the URL if possible\r\n      if (imageUrl) {\r\n        // Remove query parameters\r\n        const urlWithoutParams = imageUrl.split(\"?\")[0];\r\n        // Get the last part of the path\r\n        const urlParts = urlWithoutParams.split(\"/\");\r\n        const lastPart = urlParts[urlParts.length - 1];\r\n\r\n        if (lastPart) {\r\n          // Check if it has a file extension\r\n          const hasExtension = /\\.(jpg|jpeg|png|gif|webp)$/i.test(lastPart);\r\n\r\n          if (hasExtension) {\r\n            // Use the filename as is\r\n            filename = lastPart;\r\n          } else {\r\n            // Add .jpg extension\r\n            filename = `${lastPart}.jpg`;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Set the download attribute with the filename\r\n      link.setAttribute(\"download\", filename);\r\n\r\n      // Trigger download\r\n      document.body.appendChild(link);\r\n      link.click();\r\n\r\n      // Cleanup\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n    } catch (error) {\r\n      console.error(\"Error downloading image:\", error);\r\n    }\r\n  };\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <DialogContent className=\"max-w-[90vw] max-h-[90vh] p-0 overflow-hidden bg-black/90 border-none\">\r\n        <VisuallyHidden>\r\n          <DialogTitle>Xem hình ảnh</DialogTitle>\r\n        </VisuallyHidden>\r\n        <div className=\"relative w-full h-full flex items-center justify-center\">\r\n          <div className=\"absolute top-2 right-2 z-10 flex space-x-2\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"bg-black/50 hover:bg-black/70 text-white rounded-full\"\r\n              onClick={handleDownload}\r\n              title=\"Tải xuống\"\r\n            >\r\n              <Download className=\"h-5 w-5\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"bg-black/50 hover:bg-black/70 text-white rounded-full\"\r\n              onClick={onClose}\r\n              title=\"Đóng\"\r\n            >\r\n              <X className=\"h-5 w-5\" />\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"relative w-full h-full max-h-[85vh] flex items-center justify-center\">\r\n            <Image\r\n              src={imageUrl}\r\n              alt={alt}\r\n              className=\"object-contain max-h-[85vh] max-w-[85vw]\"\r\n              width={1200}\r\n              height={800}\r\n              unoptimized={true}\r\n              priority={true}\r\n            />\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AACA;AALA;;;;;;;AAce,SAAS,kBAAkB,EACxC,MAAM,EACN,OAAO,EACP,QAAQ,EACR,MAAM,UAAU,EACO;IACvB,oCAAoC;IACpC,MAAM,iBAAiB;QACrB,IAAI;YACF,kBAAkB;YAClB,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,yBAAyB;YACzB,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YAEZ,+CAA+C;YAC/C,IAAI,WAAW;YAEf,4DAA4D;YAC5D,IAAI,UAAU;gBACZ,0BAA0B;gBAC1B,MAAM,mBAAmB,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC/C,gCAAgC;gBAChC,MAAM,WAAW,iBAAiB,KAAK,CAAC;gBACxC,MAAM,WAAW,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;gBAE9C,IAAI,UAAU;oBACZ,mCAAmC;oBACnC,MAAM,eAAe,8BAA8B,IAAI,CAAC;oBAExD,IAAI,cAAc;wBAChB,yBAAyB;wBACzB,WAAW;oBACb,OAAO;wBACL,qBAAqB;wBACrB,WAAW,GAAG,SAAS,IAAI,CAAC;oBAC9B;gBACF;YACF;YAEA,+CAA+C;YAC/C,KAAK,YAAY,CAAC,YAAY;YAE9B,mBAAmB;YACnB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YAEV,UAAU;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IACA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,8KAAA,CAAA,iBAAc;8BACb,cAAA,8OAAC,kIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAEf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;oCACT,OAAM;8CAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;oCACT,OAAM;8CAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK;gCACL,WAAU;gCACV,OAAO;gCACP,QAAQ;gCACR,aAAa;gCACb,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 3735, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/utils/userUtils.ts"], "sourcesContent": ["import { User } from \"@/types/base\";\r\n\r\n/**\r\n * Get user initials for avatar display\r\n * @param user User object\r\n * @returns String with user initials or fallback\r\n */\r\nexport const getUserInitials = (user?: User | null): string => {\r\n  if (!user) return \"??\";\r\n\r\n  // Try to get initials from fullName\r\n  if (user.userInfo?.fullName) {\r\n    const nameParts = user.userInfo.fullName.split(\" \");\r\n    if (nameParts.length > 0 && nameParts[0]) {\r\n      if (nameParts.length > 1 && nameParts[1]) {\r\n        // If we have at least two parts, use first letter of each\r\n        return (nameParts[0][0] + nameParts[1][0]).toUpperCase();\r\n      }\r\n      // Otherwise use first two letters of first part\r\n      return nameParts[0].slice(0, 2).toUpperCase();\r\n    }\r\n  }\r\n\r\n  // Try to get initials from email\r\n  if (user.email) {\r\n    // If email has a name part (before @), use first two characters\r\n    const emailParts = user.email.split(\"@\");\r\n    if (emailParts[0]) {\r\n      // If email name part contains a dot or underscore, treat as separate words\r\n      if (emailParts[0].includes(\".\") || emailParts[0].includes(\"_\")) {\r\n        const nameParts = emailParts[0].split(/[._]/);\r\n        if (nameParts.length > 1 && nameParts[0] && nameParts[1]) {\r\n          return (nameParts[0][0] + nameParts[1][0]).toUpperCase();\r\n        }\r\n      }\r\n      // Otherwise just use first two characters of email\r\n      return emailParts[0].slice(0, 2).toUpperCase();\r\n    }\r\n  }\r\n\r\n  // Try to use phone number\r\n  if (user.phoneNumber) {\r\n    return user.phoneNumber.slice(0, 2);\r\n  }\r\n\r\n  // Try to use id as last resort\r\n  if (user.id) {\r\n    return user.id.slice(0, 2).toUpperCase();\r\n  }\r\n\r\n  // Fallback\r\n  return \"US\";\r\n};\r\n\r\n// Helper function to get user data from localStorage conversations\r\nconst getUserFromLocalStorage = (userId: string): User | null => {\r\n  try {\r\n    if (typeof window === \"undefined\") return null;\r\n\r\n    const storedData = localStorage.getItem(\"conversations-store\");\r\n    if (!storedData) return null;\r\n\r\n    const parsedData = JSON.parse(storedData);\r\n    if (!parsedData?.state?.conversations) return null;\r\n\r\n    const conversation = parsedData.state.conversations.find(\r\n      (conv: any) => conv.contact?.id === userId,\r\n    );\r\n\r\n    return conversation?.contact || null;\r\n  } catch (error) {\r\n    console.error(\"Error reading user from localStorage:\", error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Get display name for a user\r\n * @param user User object\r\n * @returns String with user's display name\r\n */\r\nexport const getUserDisplayName = (user?: User | null): string => {\r\n  if (!user) return \"Người dùng\";\r\n\r\n  // Try to use fullName from userInfo, but avoid \"Unknown\"\r\n  if (user.userInfo?.fullName && user.userInfo.fullName !== \"Unknown\") {\r\n    return user.userInfo.fullName;\r\n  }\r\n\r\n  // Try to get fullName from localStorage if available\r\n  if (user.id) {\r\n    const localStorageUser = getUserFromLocalStorage(user.id);\r\n    if (\r\n      localStorageUser?.userInfo?.fullName &&\r\n      localStorageUser.userInfo.fullName !== \"Unknown\"\r\n    ) {\r\n      return localStorageUser.userInfo.fullName;\r\n    }\r\n  }\r\n\r\n  // Try to extract name from email\r\n  if (user.email) {\r\n    const emailParts = user.email.split(\"@\");\r\n    if (emailParts[0]) {\r\n      // If email contains dots or underscores, format as name\r\n      if (emailParts[0].includes(\".\") || emailParts[0].includes(\"_\")) {\r\n        return emailParts[0]\r\n          .split(/[._]/)\r\n          .map((part) => part.charAt(0).toUpperCase() + part.slice(1))\r\n          .join(\" \");\r\n      }\r\n      // Otherwise just capitalize the email username\r\n      return emailParts[0].charAt(0).toUpperCase() + emailParts[0].slice(1);\r\n    }\r\n  }\r\n\r\n  // Use phone number if available\r\n  if (user.phoneNumber) {\r\n    return user.phoneNumber;\r\n  }\r\n\r\n  // Use user ID as last resort with a friendly format\r\n  if (user.id) {\r\n    return `Người dùng ${user.id.slice(-4)}`;\r\n  }\r\n\r\n  // Final fallback\r\n  return \"Người dùng\";\r\n};\r\n"], "names": [], "mappings": ";;;;AAOO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,CAAC,MAAM,OAAO;IAElB,oCAAoC;IACpC,IAAI,KAAK,QAAQ,EAAE,UAAU;QAC3B,MAAM,YAAY,KAAK,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC/C,IAAI,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,EAAE;YACxC,IAAI,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,EAAE;gBACxC,0DAA0D;gBAC1D,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW;YACxD;YACA,gDAAgD;YAChD,OAAO,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;QAC7C;IACF;IAEA,iCAAiC;IACjC,IAAI,KAAK,KAAK,EAAE;QACd,gEAAgE;QAChE,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,CAAC;QACpC,IAAI,UAAU,CAAC,EAAE,EAAE;YACjB,2EAA2E;YAC3E,IAAI,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;gBAC9D,MAAM,YAAY,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC;gBACtC,IAAI,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE,EAAE;oBACxD,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW;gBACxD;YACF;YACA,mDAAmD;YACnD,OAAO,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;QAC9C;IACF;IAEA,0BAA0B;IAC1B,IAAI,KAAK,WAAW,EAAE;QACpB,OAAO,KAAK,WAAW,CAAC,KAAK,CAAC,GAAG;IACnC;IAEA,+BAA+B;IAC/B,IAAI,KAAK,EAAE,EAAE;QACX,OAAO,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;IACxC;IAEA,WAAW;IACX,OAAO;AACT;AAEA,mEAAmE;AACnE,MAAM,0BAA0B,CAAC;IAC/B,IAAI;QACF,wCAAmC,OAAO;;QAE1C,MAAM;QAGN,MAAM;QAGN,MAAM;IAKR,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;IACT;AACF;AAOO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,MAAM,OAAO;IAElB,yDAAyD;IACzD,IAAI,KAAK,QAAQ,EAAE,YAAY,KAAK,QAAQ,CAAC,QAAQ,KAAK,WAAW;QACnE,OAAO,KAAK,QAAQ,CAAC,QAAQ;IAC/B;IAEA,qDAAqD;IACrD,IAAI,KAAK,EAAE,EAAE;QACX,MAAM,mBAAmB,wBAAwB,KAAK,EAAE;QACxD,IACE,kBAAkB,UAAU,YAC5B,iBAAiB,QAAQ,CAAC,QAAQ,KAAK,WACvC;YACA,OAAO,iBAAiB,QAAQ,CAAC,QAAQ;QAC3C;IACF;IAEA,iCAAiC;IACjC,IAAI,KAAK,KAAK,EAAE;QACd,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,CAAC;QACpC,IAAI,UAAU,CAAC,EAAE,EAAE;YACjB,wDAAwD;YACxD,IAAI,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;gBAC9D,OAAO,UAAU,CAAC,EAAE,CACjB,KAAK,CAAC,QACN,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACxD,IAAI,CAAC;YACV;YACA,+CAA+C;YAC/C,OAAO,UAAU,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC;QACrE;IACF;IAEA,gCAAgC;IAChC,IAAI,KAAK,WAAW,EAAE;QACpB,OAAO,KAAK,WAAW;IACzB;IAEA,oDAAoD;IACpD,IAAI,KAAK,EAAE,EAAE;QACX,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI;IAC1C;IAEA,iBAAiB;IACjB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3835, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/profile/UserAvatar.tsx"], "sourcesContent": ["import { User } from \"@/types/base\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"../ui/avatar\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { HTMLProps, useEffect, useState } from \"react\";\r\nimport { getUserInitials } from \"@/utils/userUtils\";\r\n\r\nexport default function UserAvatar({\r\n  user,\r\n  className,\r\n}: {\r\n  user: User;\r\n  className?: HTMLProps<HTMLElement>[\"className\"];\r\n}) {\r\n  // Sử dụng state thay vì useMemo để đảm bảo component re-render khi URL thay đổi\r\n  const [profileImageSrc, setProfileImageSrc] = useState<string | null>(null);\r\n  // Thêm state để theo dõi khi nào cần cập nhật URL\r\n  const [imageVersion, setImageVersion] = useState<number>(0);\r\n\r\n  // Cập nhật URL khi user hoặc imageVersion thay đổi\r\n  useEffect(() => {\r\n    if (\r\n      !user?.userInfo?.profilePictureUrl ||\r\n      user.userInfo.profilePictureUrl === \"\"\r\n    ) {\r\n      setProfileImageSrc(null);\r\n      return;\r\n    }\r\n\r\n    // Tạo URL mới với timestamp để tránh cache\r\n    const newSrc = `${user.userInfo.profilePictureUrl}?t=${new Date().getTime()}-v${imageVersion}`;\r\n    setProfileImageSrc(newSrc);\r\n  }, [user?.userInfo?.profilePictureUrl, imageVersion]);\r\n\r\n  // Cập nhật imageVersion mỗi khi component mount để đảm bảo luôn tải hình ảnh mới nhất\r\n  useEffect(() => {\r\n    setImageVersion((prev) => prev + 1);\r\n  }, []);\r\n\r\n  return (\r\n    <Avatar\r\n      className={cn(\r\n        \"cursor-pointer h-12 w-12 border-2 border-white hover:border-blue-300 transition-all\",\r\n        className,\r\n      )}\r\n    >\r\n      <AvatarImage\r\n        className=\"object-cover\"\r\n        src={profileImageSrc || undefined}\r\n        onError={() => {\r\n          // Nếu hình ảnh không tải được, thử lại với version mới\r\n          setImageVersion((prev) => prev + 1);\r\n        }}\r\n      />\r\n      <AvatarFallback className=\"text-gray\">\r\n        {getUserInitials(user)}\r\n      </AvatarFallback>\r\n    </Avatar>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAEe,SAAS,WAAW,EACjC,IAAI,EACJ,SAAS,EAIV;IACC,gFAAgF;IAChF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,kDAAkD;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IACE,CAAC,MAAM,UAAU,qBACjB,KAAK,QAAQ,CAAC,iBAAiB,KAAK,IACpC;YACA,mBAAmB;YACnB;QACF;QAEA,2CAA2C;QAC3C,MAAM,SAAS,GAAG,KAAK,QAAQ,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,GAAG,EAAE,EAAE,cAAc;QAC9F,mBAAmB;IACrB,GAAG;QAAC,MAAM,UAAU;QAAmB;KAAa;IAEpD,sFAAsF;IACtF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,CAAC,OAAS,OAAO;IACnC,GAAG,EAAE;IAEL,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA;;0BAGF,8OAAC,kIAAA,CAAA,cAAW;gBACV,WAAU;gBACV,KAAK,mBAAmB;gBACxB,SAAS;oBACP,uDAAuD;oBACvD,gBAAgB,CAAC,OAAS,OAAO;gBACnC;;;;;;0BAEF,8OAAC,kIAAA,CAAA,iBAAc;gBAAC,WAAU;0BACvB,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE;;;;;;;;;;;;AAIzB", "debugId": null}}, {"offset": {"line": 3906, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/profile/DateOfBirthPicker.tsx"], "sourcesContent": ["import React, { useEffect, useMemo } from \"react\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface DateOfBirthPickerProps {\r\n  day: string;\r\n  month: string;\r\n  year: string;\r\n  onDayChange: (day: string) => void;\r\n  onMonthChange: (month: string) => void;\r\n  onYearChange: (year: string) => void;\r\n  className?: string;\r\n  showFutureWarning?: boolean;\r\n}\r\n\r\n// Danh sách tháng từ 1-12 với tên tháng - định nghĩa bên ngoài component để tránh tạo lại\r\nconst MONTH_NAMES = [\r\n  \"Tháng 1\",\r\n  \"Tháng 2\",\r\n  \"Tháng 3\",\r\n  \"Tháng 4\",\r\n  \"Tháng 5\",\r\n  \"Tháng 6\",\r\n  \"Tháng 7\",\r\n  \"Tháng 8\",\r\n  \"Tháng 9\",\r\n  \"Tháng 10\",\r\n  \"Tháng 11\",\r\n  \"Tháng 12\",\r\n];\r\n\r\n// H<PERSON>m kiểm tra năm nhuận - định nghĩa bên ngoài component\r\nconst isLeapYear = (year: number): boolean => {\r\n  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;\r\n};\r\n\r\n// Hàm tính số ngày trong tháng - định nghĩa bên ngoài component\r\nconst getDaysInMonth = (month: number, year: number): number => {\r\n  // Tháng 2\r\n  if (month === 2) {\r\n    return isLeapYear(year) ? 29 : 28;\r\n  }\r\n  // Tháng 4, 6, 9, 11 có 30 ngày\r\n  else if ([4, 6, 9, 11].includes(month)) {\r\n    return 30;\r\n  }\r\n  // Các tháng còn lại có 31 ngày\r\n  else {\r\n    return 31;\r\n  }\r\n};\r\n\r\n// Tạo mảng tháng từ 1-12 - định nghĩa bên ngoài component\r\nconst MONTHS = Array.from({ length: 12 }, (_, i) => String(i + 1));\r\n\r\nconst DateOfBirthPicker: React.FC<DateOfBirthPickerProps> = ({\r\n  day,\r\n  month,\r\n  year,\r\n  onDayChange,\r\n  onMonthChange,\r\n  onYearChange,\r\n  className,\r\n  showFutureWarning = true,\r\n}) => {\r\n  // Lấy năm hiện tại một lần duy nhất\r\n  const currentYear = useMemo(() => new Date().getFullYear(), []);\r\n\r\n  // Memoize danh sách năm để tránh tạo lại mỗi lần render\r\n  const years = useMemo(\r\n    () => Array.from({ length: 100 }, (_, i) => String(currentYear - i)),\r\n    [currentYear],\r\n  );\r\n\r\n  // Tính toán số ngày trong tháng và tạo mảng ngày\r\n  const days = useMemo(() => {\r\n    const monthNum = parseInt(month, 10) || 1;\r\n    const yearNum = parseInt(year, 10) || currentYear;\r\n    const daysInMonth = getDaysInMonth(monthNum, yearNum);\r\n    return Array.from({ length: daysInMonth }, (_, i) => String(i + 1));\r\n  }, [month, year, currentYear]);\r\n\r\n  // Xử lý khi ngày vượt quá số ngày trong tháng\r\n  useEffect(() => {\r\n    const monthNum = parseInt(month, 10) || 1;\r\n    const yearNum = parseInt(year, 10) || currentYear;\r\n    const daysInMonth = getDaysInMonth(monthNum, yearNum);\r\n\r\n    if (parseInt(day, 10) > daysInMonth) {\r\n      onDayChange(String(daysInMonth));\r\n    }\r\n  }, [month, year, day, onDayChange, currentYear]);\r\n\r\n  // Kiểm tra ngày tương lai - chỉ khi cả ba giá trị đều có\r\n  useEffect(() => {\r\n    // Chỉ kiểm tra khi cả ba giá trị đều hợp lệ\r\n    if (!day || !month || !year || !showFutureWarning) return;\r\n\r\n    const selectedDay = parseInt(day, 10);\r\n    const selectedMonth = parseInt(month, 10);\r\n    const selectedYear = parseInt(year, 10);\r\n\r\n    if (isNaN(selectedDay) || isNaN(selectedMonth) || isNaN(selectedYear)) {\r\n      return;\r\n    }\r\n\r\n    const selectedDate = new Date(selectedYear, selectedMonth - 1, selectedDay);\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n\r\n    if (selectedDate > today) {\r\n      // Sử dụng setTimeout để tránh hiển thị cảnh báo quá sớm\r\n      const timer = setTimeout(() => {\r\n        toast.warning(\"Ngày sinh không thể là ngày trong tương lai.\");\r\n      }, 500);\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [day, month, year, showFutureWarning]);\r\n\r\n  return (\r\n    <div className={`flex gap-2 ${className || \"\"}`}>\r\n      <Select value={day} onValueChange={onDayChange} name=\"day\">\r\n        <SelectTrigger className={`w-[80px] ${className || \"\"}`}>\r\n          <SelectValue placeholder=\"Ngày\" />\r\n        </SelectTrigger>\r\n        <SelectContent className=\"max-h-[200px] overflow-y-auto\">\r\n          {days.map((d) => (\r\n            <SelectItem key={d} value={d}>\r\n              {d}\r\n            </SelectItem>\r\n          ))}\r\n        </SelectContent>\r\n      </Select>\r\n\r\n      <Select value={month} onValueChange={onMonthChange} name=\"month\">\r\n        <SelectTrigger className={`w-[120px] ${className || \"\"}`}>\r\n          <SelectValue placeholder=\"Tháng\" />\r\n        </SelectTrigger>\r\n        <SelectContent className=\"max-h-[200px] overflow-y-auto\">\r\n          {MONTHS.map((m, index) => (\r\n            <SelectItem key={m} value={m}>\r\n              {MONTH_NAMES[index]}\r\n            </SelectItem>\r\n          ))}\r\n        </SelectContent>\r\n      </Select>\r\n\r\n      <Select value={year} onValueChange={onYearChange} name=\"year\">\r\n        <SelectTrigger className={`w-[100px] ${className || \"\"}`}>\r\n          <SelectValue placeholder=\"Năm\" />\r\n        </SelectTrigger>\r\n        <SelectContent className=\"max-h-[200px] overflow-y-auto\">\r\n          {years.map((y) => (\r\n            <SelectItem key={y} value={y}>\r\n              {y}\r\n            </SelectItem>\r\n          ))}\r\n        </SelectContent>\r\n      </Select>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default React.memo(DateOfBirthPicker);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAOA;;;;;AAaA,0FAA0F;AAC1F,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,0DAA0D;AAC1D,MAAM,aAAa,CAAC;IAClB,OAAO,AAAC,OAAO,MAAM,KAAK,OAAO,QAAQ,KAAM,OAAO,QAAQ;AAChE;AAEA,gEAAgE;AAChE,MAAM,iBAAiB,CAAC,OAAe;IACrC,UAAU;IACV,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,QAAQ,KAAK;IACjC,OAEK,IAAI;QAAC;QAAG;QAAG;QAAG;KAAG,CAAC,QAAQ,CAAC,QAAQ;QACtC,OAAO;IACT,OAEK;QACH,OAAO;IACT;AACF;AAEA,0DAA0D;AAC1D,MAAM,SAAS,MAAM,IAAI,CAAC;IAAE,QAAQ;AAAG,GAAG,CAAC,GAAG,IAAM,OAAO,IAAI;AAE/D,MAAM,oBAAsD,CAAC,EAC3D,GAAG,EACH,KAAK,EACL,IAAI,EACJ,WAAW,EACX,aAAa,EACb,YAAY,EACZ,SAAS,EACT,oBAAoB,IAAI,EACzB;IACC,oCAAoC;IACpC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,OAAO,WAAW,IAAI,EAAE;IAE9D,wDAAwD;IACxD,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAClB,IAAM,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAI,GAAG,CAAC,GAAG,IAAM,OAAO,cAAc,KACjE;QAAC;KAAY;IAGf,iDAAiD;IACjD,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnB,MAAM,WAAW,SAAS,OAAO,OAAO;QACxC,MAAM,UAAU,SAAS,MAAM,OAAO;QACtC,MAAM,cAAc,eAAe,UAAU;QAC7C,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAY,GAAG,CAAC,GAAG,IAAM,OAAO,IAAI;IAClE,GAAG;QAAC;QAAO;QAAM;KAAY;IAE7B,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,SAAS,OAAO,OAAO;QACxC,MAAM,UAAU,SAAS,MAAM,OAAO;QACtC,MAAM,cAAc,eAAe,UAAU;QAE7C,IAAI,SAAS,KAAK,MAAM,aAAa;YACnC,YAAY,OAAO;QACrB;IACF,GAAG;QAAC;QAAO;QAAM;QAAK;QAAa;KAAY;IAE/C,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4CAA4C;QAC5C,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAmB;QAEnD,MAAM,cAAc,SAAS,KAAK;QAClC,MAAM,gBAAgB,SAAS,OAAO;QACtC,MAAM,eAAe,SAAS,MAAM;QAEpC,IAAI,MAAM,gBAAgB,MAAM,kBAAkB,MAAM,eAAe;YACrE;QACF;QAEA,MAAM,eAAe,IAAI,KAAK,cAAc,gBAAgB,GAAG;QAC/D,MAAM,QAAQ,IAAI;QAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;QAExB,IAAI,eAAe,OAAO;YACxB,wDAAwD;YACxD,MAAM,QAAQ,WAAW;gBACvB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAK;QAAO;QAAM;KAAkB;IAExC,qBACE,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;;0BAC7C,8OAAC,kIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAK,eAAe;gBAAa,MAAK;;kCACnD,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAW,CAAC,SAAS,EAAE,aAAa,IAAI;kCACrD,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,aAAY;;;;;;;;;;;kCAE3B,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;kCACtB,KAAK,GAAG,CAAC,CAAC,kBACT,8OAAC,kIAAA,CAAA,aAAU;gCAAS,OAAO;0CACxB;+BADc;;;;;;;;;;;;;;;;0BAOvB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAO,eAAe;gBAAe,MAAK;;kCACvD,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAW,CAAC,UAAU,EAAE,aAAa,IAAI;kCACtD,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,aAAY;;;;;;;;;;;kCAE3B,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;kCACtB,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,8OAAC,kIAAA,CAAA,aAAU;gCAAS,OAAO;0CACxB,WAAW,CAAC,MAAM;+BADJ;;;;;;;;;;;;;;;;0BAOvB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAM,eAAe;gBAAc,MAAK;;kCACrD,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAW,CAAC,UAAU,EAAE,aAAa,IAAI;kCACtD,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,aAAY;;;;;;;;;;;kCAE3B,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;kCACtB,MAAM,GAAG,CAAC,CAAC,kBACV,8OAAC,kIAAA,CAAA,aAAU;gCAAS,OAAO;0CACxB;+BADc;;;;;;;;;;;;;;;;;;;;;;AAQ7B;qDAEe,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 4156, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/profile/ProfileEditForm.tsx"], "sourcesContent": ["import { memo, useRef, useCallback, useEffect, useState } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport DateOfBirthPicker from \"./DateOfBirthPicker\";\r\nimport { CalendarIcon } from \"lucide-react\";\r\n\r\n// Define form type for better type safety\r\nexport type ProfileFormValues = {\r\n  fullName: string;\r\n  gender: string;\r\n  bio: string;\r\n  day: string;\r\n  month: string;\r\n  year: string;\r\n};\r\n\r\ninterface ProfileEditFormProps {\r\n  initialValues: ProfileFormValues;\r\n  onSubmit: (values: ProfileFormValues) => void;\r\n  onCancel: () => void;\r\n  days?: string[];\r\n  months?: string[];\r\n  years?: string[];\r\n}\r\n\r\n// Optimized input component that doesn't cause re-renders\r\nconst OptimizedInput = memo(\r\n  ({\r\n    id,\r\n    defaultValue,\r\n    placeholder,\r\n    className,\r\n  }: {\r\n    id: string;\r\n    defaultValue: string;\r\n    placeholder?: string;\r\n    className?: string;\r\n  }) => {\r\n    // Using a ref to avoid re-renders\r\n    const inputRef = useRef<HTMLInputElement>(null);\r\n\r\n    // Sử dụng useRef để lưu trữ giá trị thay vì useState để tránh re-render\r\n    const valueRef = useRef(defaultValue);\r\n\r\n    // Sử dụng debounce để giảm số lần cập nhật\r\n    const handleChange = useCallback(\r\n      (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        // Lưu giá trị vào ref thay vì state\r\n        valueRef.current = e.target.value;\r\n      },\r\n      [],\r\n    );\r\n\r\n    return (\r\n      <Input\r\n        ref={inputRef}\r\n        id={id}\r\n        name={id}\r\n        defaultValue={defaultValue}\r\n        placeholder={placeholder}\r\n        className={cn(\"h-10\", className)}\r\n        onChange={handleChange}\r\n      />\r\n    );\r\n  },\r\n);\r\n\r\nOptimizedInput.displayName = \"OptimizedInput\";\r\n\r\nconst ProfileEditForm = memo(\r\n  ({ initialValues, onSubmit, onCancel }: ProfileEditFormProps) => {\r\n    // Use refs for form elements\r\n    const formRef = useRef<HTMLFormElement>(null);\r\n\r\n    // State để lưu trữ giá trị ngày tháng năm hiện tại\r\n    const [dateValues, setDateValues] = useState({\r\n      day: initialValues.day,\r\n      month: initialValues.month,\r\n      year: initialValues.year,\r\n    });\r\n\r\n    // Cập nhật state khi initialValues thay đổi\r\n    useEffect(() => {\r\n      setDateValues({\r\n        day: initialValues.day,\r\n        month: initialValues.month,\r\n        year: initialValues.year,\r\n      });\r\n    }, [initialValues.day, initialValues.month, initialValues.year]);\r\n\r\n    // Các hàm xử lý sự kiện thay đổi ngày tháng năm\r\n    const handleDayChange = useCallback((value: string) => {\r\n      setDateValues((prev) => ({ ...prev, day: value }));\r\n    }, []);\r\n\r\n    const handleMonthChange = useCallback((value: string) => {\r\n      setDateValues((prev) => ({ ...prev, month: value }));\r\n    }, []);\r\n\r\n    const handleYearChange = useCallback((value: string) => {\r\n      setDateValues((prev) => ({ ...prev, year: value }));\r\n    }, []);\r\n\r\n    // Handle form submission using form data API\r\n    const handleSubmit = useCallback(\r\n      (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n\r\n        if (!formRef.current) return;\r\n\r\n        const formData = new FormData(formRef.current);\r\n        const values: ProfileFormValues = {\r\n          fullName: (formData.get(\"fullName\") as string) || \"\",\r\n          bio: (formData.get(\"bio\") as string) || \"\",\r\n          gender: (formData.get(\"gender\") as string) || \"MALE\",\r\n          day: dateValues.day,\r\n          month: dateValues.month,\r\n          year: dateValues.year,\r\n        };\r\n\r\n        onSubmit(values);\r\n      },\r\n      [onSubmit, dateValues],\r\n    );\r\n\r\n    return (\r\n      <form\r\n        ref={formRef}\r\n        onSubmit={handleSubmit}\r\n        className=\"p-6 space-y-6 overflow-auto no-scrollbar\"\r\n      >\r\n        <div className=\"space-y-2\">\r\n          <Label htmlFor=\"fullName\">Tên hiển thị</Label>\r\n          <OptimizedInput id=\"fullName\" defaultValue={initialValues.fullName} />\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <Label htmlFor=\"bio\">Giới thiệu</Label>\r\n          <OptimizedInput\r\n            id=\"bio\"\r\n            defaultValue={initialValues.bio}\r\n            placeholder=\"Thêm giới thiệu về bạn\"\r\n          />\r\n        </div>\r\n\r\n        <div className=\"space-y-4\">\r\n          <h3 className=\"text-sm font-medium\">Thông tin cá nhân</h3>\r\n\r\n          <div className=\"space-y-2\">\r\n            <Label>Giới tính</Label>\r\n            <RadioGroup\r\n              name=\"gender\"\r\n              defaultValue={initialValues.gender}\r\n              className=\"flex gap-4\"\r\n            >\r\n              <div className=\"flex items-center space-x-2\">\r\n                <RadioGroupItem value=\"MALE\" id=\"male\" />\r\n                <Label htmlFor=\"male\">Nam</Label>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <RadioGroupItem value=\"FEMALE\" id=\"female\" />\r\n                <Label htmlFor=\"female\">Nữ</Label>\r\n              </div>\r\n            </RadioGroup>\r\n          </div>\r\n\r\n          <div className=\"space-y-2\">\r\n            <Label>Ngày sinh</Label>\r\n            <div className=\"flex items-center gap-2 border border-input rounded-md px-3 py-1\">\r\n              <CalendarIcon className=\"w-4 h-4 text-muted-foreground flex-shrink-0\" />\r\n              <DateOfBirthPicker\r\n                day={dateValues.day}\r\n                month={dateValues.month}\r\n                year={dateValues.year}\r\n                onDayChange={handleDayChange}\r\n                onMonthChange={handleMonthChange}\r\n                onYearChange={handleYearChange}\r\n                className=\"border-none shadow-none focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex justify-end gap-2 pt-4\">\r\n          <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\r\n            Huỷ\r\n          </Button>\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"bg-blue-500 hover:bg-blue-600 text-white\"\r\n          >\r\n            Cập nhật\r\n          </Button>\r\n        </div>\r\n      </form>\r\n    );\r\n  },\r\n);\r\n\r\nProfileEditForm.displayName = \"ProfileEditForm\";\r\n\r\nexport default ProfileEditForm;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAqBA,0DAA0D;AAC1D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EACxB,CAAC,EACC,EAAE,EACF,YAAY,EACZ,WAAW,EACX,SAAS,EAMV;IACC,kCAAkC;IAClC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,wEAAwE;IACxE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,2CAA2C;IAC3C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,CAAC;QACC,oCAAoC;QACpC,SAAS,OAAO,GAAG,EAAE,MAAM,CAAC,KAAK;IACnC,GACA,EAAE;IAGJ,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,IAAI;QACJ,MAAM;QACN,cAAc;QACd,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACtB,UAAU;;;;;;AAGhB;AAGF,eAAe,WAAW,GAAG;AAE7B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EACzB,CAAC,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAwB;IAC1D,6BAA6B;IAC7B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;IAExC,mDAAmD;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,KAAK,cAAc,GAAG;QACtB,OAAO,cAAc,KAAK;QAC1B,MAAM,cAAc,IAAI;IAC1B;IAEA,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;YACZ,KAAK,cAAc,GAAG;YACtB,OAAO,cAAc,KAAK;YAC1B,MAAM,cAAc,IAAI;QAC1B;IACF,GAAG;QAAC,cAAc,GAAG;QAAE,cAAc,KAAK;QAAE,cAAc,IAAI;KAAC;IAE/D,gDAAgD;IAChD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,cAAc,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,KAAK;YAAM,CAAC;IAClD,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,cAAc,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,OAAO;YAAM,CAAC;IACpD,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,cAAc,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAM,CAAC;IACnD,GAAG,EAAE;IAEL,6CAA6C;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,CAAC;QACC,EAAE,cAAc;QAEhB,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,MAAM,WAAW,IAAI,SAAS,QAAQ,OAAO;QAC7C,MAAM,SAA4B;YAChC,UAAU,AAAC,SAAS,GAAG,CAAC,eAA0B;YAClD,KAAK,AAAC,SAAS,GAAG,CAAC,UAAqB;YACxC,QAAQ,AAAC,SAAS,GAAG,CAAC,aAAwB;YAC9C,KAAK,WAAW,GAAG;YACnB,OAAO,WAAW,KAAK;YACvB,MAAM,WAAW,IAAI;QACvB;QAEA,SAAS;IACX,GACA;QAAC;QAAU;KAAW;IAGxB,qBACE,8OAAC;QACC,KAAK;QACL,UAAU;QACV,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAW;;;;;;kCAC1B,8OAAC;wBAAe,IAAG;wBAAW,cAAc,cAAc,QAAQ;;;;;;;;;;;;0BAGpE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAM;;;;;;kCACrB,8OAAC;wBACC,IAAG;wBACH,cAAc,cAAc,GAAG;wBAC/B,aAAY;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsB;;;;;;kCAEpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC,0IAAA,CAAA,aAAU;gCACT,MAAK;gCACL,cAAc,cAAc,MAAM;gCAClC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0IAAA,CAAA,iBAAc;gDAAC,OAAM;gDAAO,IAAG;;;;;;0DAChC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAO;;;;;;;;;;;;kDAExB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0IAAA,CAAA,iBAAc;gDAAC,OAAM;gDAAS,IAAG;;;;;;0DAClC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;kCAK9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC,kJAAA,CAAA,UAAiB;wCAChB,KAAK,WAAW,GAAG;wCACnB,OAAO,WAAW,KAAK;wCACvB,MAAM,WAAW,IAAI;wCACrB,aAAa;wCACb,eAAe;wCACf,cAAc;wCACd,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAS,SAAQ;wBAAU,SAAS;kCAAU;;;;;;kCAG3D,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;AAGF,gBAAgB,WAAW,GAAG;uCAEf", "debugId": null}}, {"offset": {"line": 4499, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/profile/ProfileDialog.tsx"], "sourcesContent": ["import {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  <PERSON><PERSON>Header,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  AlertTriangle,\r\n  Ban,\r\n  Camera,\r\n  ChevronLeft,\r\n  Pencil,\r\n  Share2,\r\n  UserMinus,\r\n  UserPlus,\r\n} from \"lucide-react\";\r\nimport CallButton from \"@/components/call/CallButton\";\r\nimport RefreshUserDataButton from \"./RefreshUserDataButton\";\r\nimport { User } from \"@/types/base\";\r\nimport Image from \"next/image\";\r\nimport { useState, useRef, useEffect, useCallback, useMemo } from \"react\";\r\nimport {\r\n  updateProfilePicture,\r\n  updateCoverImage,\r\n  updateUserBasicInfo,\r\n} from \"@/actions/user.action\";\r\nimport {\r\n  getRelationship,\r\n  sendFriendRequest,\r\n  removeFriend,\r\n  blockUser,\r\n} from \"@/actions/friend.action\";\r\nimport ImageViewerDialog from \"./ImageViewerDialog\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { toast } from \"sonner\";\r\nimport { useRouter } from \"next/navigation\";\r\n// Removed framer-motion imports to improve performance\r\nimport UserAvatar from \"./UserAvatar\";\r\nimport ProfileEditForm, { ProfileFormValues } from \"./ProfileEditForm\";\r\n\r\ninterface ProfileDialogProps {\r\n  user: User | null;\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  isOwnProfile?: boolean;\r\n  onChat?: () => void;\r\n  onCall?: () => void;\r\n  initialShowFriendRequestForm?: boolean;\r\n}\r\n\r\nexport default function ProfileDialog({\r\n  user,\r\n  isOpen,\r\n  onOpenChange,\r\n  isOwnProfile = false,\r\n  onChat,\r\n  onCall,\r\n  initialShowFriendRequestForm = false,\r\n}: ProfileDialogProps) {\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [coverImageUrl, setCoverImageUrl] = useState<string | null>(null);\r\n  const [relationship, setRelationship] = useState<string | null>(null);\r\n  const [isLoadingRelationship, setIsLoadingRelationship] = useState(false);\r\n  const [showFriendRequestForm, setShowFriendRequestForm] = useState(\r\n    initialShowFriendRequestForm,\r\n  );\r\n  const [requestMessage, setRequestMessage] = useState(\"\");\r\n  const [isSendingRequest, setIsSendingRequest] = useState(false);\r\n  const [isAcceptingRequest, setIsAcceptingRequest] = useState(false);\r\n  const [isRejectingRequest, setIsRejectingRequest] = useState(false);\r\n  const [requestId, setRequestId] = useState<string | null>(null);\r\n  const [showBlockDialog, setShowBlockDialog] = useState(false);\r\n  const [isBlocking, setIsBlocking] = useState(false);\r\n  const [showRemoveFriendDialog, setShowRemoveFriendDialog] = useState(false);\r\n  const [isRemovingFriend, setIsRemovingFriend] = useState(false);\r\n  const profilePictureInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // State for image viewer\r\n  const [isImageViewerOpen, setIsImageViewerOpen] = useState(false);\r\n  const [viewerImageUrl, setViewerImageUrl] = useState(\"\");\r\n  const [viewerImageAlt, setViewerImageAlt] = useState(\"\");\r\n\r\n  // Lấy các hàm từ stores\r\n  const { acceptRequest, rejectRequest } = useFriendStore();\r\n  const router = useRouter();\r\n\r\n  // Lấy hàm openChat từ chatStore\r\n  const { openChat } = useChatStore();\r\n\r\n  // Lấy user từ store để luôn có dữ liệu mới nhất\r\n  const storeUser = useAuthStore((state) => state.user);\r\n\r\n  // Sử dụng user từ store nếu đang xem profile của chính mình\r\n  const currentUser = isOwnProfile && storeUser ? storeUser : user;\r\n\r\n  // Kiểm tra mối quan hệ khi user thay đổi hoặc dialog mở\r\n  useEffect(() => {\r\n    // Không cần kiểm tra mối quan hệ nếu đang xem profile của chính mình\r\n    if (isOwnProfile || !user?.id || !isOpen) return;\r\n\r\n    console.log(\r\n      \"Checking relationship for user:\",\r\n      user.id,\r\n      user.userInfo?.fullName,\r\n    );\r\n\r\n    const checkRelationship = async () => {\r\n      try {\r\n        setIsLoadingRelationship(true);\r\n        const accessToken = useAuthStore.getState().accessToken || undefined;\r\n        console.log(\"Calling getRelationship with userId:\", user.id);\r\n        const result = await getRelationship(user.id, accessToken);\r\n        console.log(\"Full relationship response:\", result);\r\n\r\n        if (result.success && result.data) {\r\n          // Lấy trạng thái từ API response\r\n          // API trả về status ở cấp cao nhất, ví dụ: \"FRIEND\", \"PENDING_SENT\", \"PENDING_RECEIVED\", \"DECLINED_RECEIVED\", v.v.\r\n          console.log(\"Setting relationship to:\", result.data.status);\r\n          setRelationship(result.data.status || \"NONE\");\r\n\r\n          // Xử lý các trường hợp đặc biệt\r\n          if (\r\n            result.data.status === \"PENDING_RECEIVED\" &&\r\n            result.data.relationship\r\n          ) {\r\n            console.log(\r\n              \"Found PENDING_RECEIVED relationship with data:\",\r\n              result.data.relationship,\r\n            );\r\n            // Lấy ID của lời mời kết bạn từ trường relationship\r\n            const relationshipData = result.data.relationship;\r\n            if (relationshipData.id) {\r\n              console.log(\r\n                \"Setting requestId to relationship.id:\",\r\n                relationshipData.id,\r\n              );\r\n              setRequestId(relationshipData.id);\r\n            } else {\r\n              console.error(\"No id found in PENDING_RECEIVED relationship\");\r\n            }\r\n          }\r\n        } else {\r\n          setRelationship(\"NONE\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking relationship:\", error);\r\n        setRelationship(\"NONE\");\r\n      } finally {\r\n        setIsLoadingRelationship(false);\r\n      }\r\n    };\r\n\r\n    checkRelationship();\r\n  }, [isOwnProfile, user?.id, user?.userInfo?.fullName, isOpen]);\r\n\r\n  // Default date of birth\r\n  const defaultDob = useMemo(() => new Date(\"2003-11-03\"), []);\r\n\r\n  // Get initial form values from user data\r\n  const getInitialFormValues = useCallback((): ProfileFormValues => {\r\n    const dob = currentUser?.userInfo?.dateOfBirth\r\n      ? new Date(currentUser.userInfo.dateOfBirth)\r\n      : defaultDob;\r\n\r\n    return {\r\n      fullName: currentUser?.userInfo?.fullName || \"\",\r\n      gender: currentUser?.userInfo?.gender || \"MALE\",\r\n      bio: currentUser?.userInfo?.bio || \"\",\r\n      day: dob.getDate().toString().padStart(2, \"0\"),\r\n      month: (dob.getMonth() + 1).toString().padStart(2, \"0\"),\r\n      year: dob.getFullYear().toString(),\r\n    };\r\n  }, [\r\n    currentUser?.userInfo?.bio,\r\n    currentUser?.userInfo?.dateOfBirth,\r\n    currentUser?.userInfo?.fullName,\r\n    currentUser?.userInfo?.gender,\r\n    defaultDob,\r\n  ]);\r\n\r\n  // Memoize form values to prevent unnecessary re-renders\r\n  const initialFormValues = useMemo(\r\n    () => getInitialFormValues(),\r\n    [getInitialFormValues],\r\n  );\r\n\r\n  // Update cover image URL when user changes\r\n  useEffect(() => {\r\n    if (currentUser) {\r\n      // Always update cover image URL when currentUser changes to ensure real-time updates\r\n      setCoverImageUrl(currentUser.userInfo?.coverImgUrl || null);\r\n    }\r\n  }, [currentUser]);\r\n\r\n  // Handle send friend request\r\n  const handleSendFriendRequest = async () => {\r\n    if (!user?.id) return;\r\n\r\n    try {\r\n      setIsSendingRequest(true);\r\n      const accessToken = useAuthStore.getState().accessToken || undefined;\r\n      const result = await sendFriendRequest(\r\n        user.id,\r\n        requestMessage,\r\n        accessToken,\r\n      );\r\n      if (result.success) {\r\n        toast.success(\"Lời mời kết bạn đã được gửi!\");\r\n        setRelationship(\"PENDING_SENT\");\r\n        setShowFriendRequestForm(false);\r\n      } else {\r\n        toast.error(`Không thể gửi lời mời kết bạn: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error sending friend request:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi gửi lời mời kết bạn\");\r\n    } finally {\r\n      setIsSendingRequest(false);\r\n    }\r\n  };\r\n\r\n  // Toggle friend request form\r\n  const toggleFriendRequestForm = () => {\r\n    setShowFriendRequestForm(!showFriendRequestForm);\r\n    if (showFriendRequestForm) {\r\n      setRequestMessage(\"\");\r\n    }\r\n  };\r\n\r\n  // Handle remove friend\r\n  const handleRemoveFriend = async () => {\r\n    if (!user?.id) return;\r\n\r\n    setIsRemovingFriend(true);\r\n    try {\r\n      const accessToken = useAuthStore.getState().accessToken || undefined;\r\n      const result = await removeFriend(user.id, accessToken);\r\n      if (result.success) {\r\n        toast.success(\"Xóa kết bạn thành công!\");\r\n        setRelationship(\"NONE\");\r\n\r\n        // Force cleanup of any potential overlay issues\r\n        document.body.style.pointerEvents = \"auto\";\r\n\r\n        // Close dialog with a slight delay\r\n        setTimeout(() => {\r\n          setShowRemoveFriendDialog(false);\r\n        }, 50);\r\n      } else {\r\n        toast.error(`Không thể xóa kết bạn: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error removing friend:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi xóa kết bạn\");\r\n    } finally {\r\n      setIsRemovingFriend(false);\r\n    }\r\n  };\r\n\r\n  // Handle block user\r\n  const handleBlockUser = async () => {\r\n    if (!user?.id) return;\r\n\r\n    setIsBlocking(true);\r\n    try {\r\n      const accessToken = useAuthStore.getState().accessToken || undefined;\r\n      const result = await blockUser(user.id, accessToken);\r\n      if (result.success) {\r\n        toast.success(`Đã chặn ${user.userInfo?.fullName || \"người dùng này\"}`);\r\n\r\n        // Force cleanup of any potential overlay issues\r\n        document.body.style.pointerEvents = \"auto\";\r\n\r\n        // Close dialogs with a slight delay\r\n        setTimeout(() => {\r\n          setShowBlockDialog(false);\r\n          onOpenChange(false); // Close the profile dialog\r\n        }, 50);\r\n\r\n        // Update relationship status\r\n        setRelationship(\"BLOCKED\");\r\n      } else {\r\n        toast.error(`Không thể chặn người dùng: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error blocking user:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi chặn người dùng\");\r\n    } finally {\r\n      setIsBlocking(false);\r\n    }\r\n  };\r\n\r\n  // Handle accept friend request\r\n  const handleAcceptRequest = async () => {\r\n    if (!user?.id || !requestId) return;\r\n\r\n    setIsAcceptingRequest(true);\r\n    try {\r\n      const success = await acceptRequest(requestId);\r\n      if (success) {\r\n        toast.success(\r\n          `Đã chấp nhận lời mời kết bạn từ ${user.userInfo?.fullName || \"người dùng\"}`,\r\n        );\r\n        setRelationship(\"FRIEND\");\r\n      } else {\r\n        toast.error(\r\n          \"Không thể chấp nhận lời mời kết bạn. Vui lòng thử lại sau.\",\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error accepting friend request:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi chấp nhận lời mời kết bạn\");\r\n    } finally {\r\n      setIsAcceptingRequest(false);\r\n    }\r\n  };\r\n\r\n  // Handle reject friend request\r\n  const handleRejectRequest = async () => {\r\n    console.log(\"handleRejectRequest called with requestId:\", requestId);\r\n    if (!user?.id || !requestId) {\r\n      console.error(\"Cannot reject request: missing user.id or requestId\");\r\n      return;\r\n    }\r\n\r\n    setIsRejectingRequest(true);\r\n    try {\r\n      console.log(\"Calling rejectRequest with requestId:\", requestId);\r\n      const success = await rejectRequest(requestId);\r\n      console.log(\"rejectRequest result:\", success);\r\n      if (success) {\r\n        toast.success(\r\n          `Đã từ chối lời mời kết bạn từ ${user.userInfo?.fullName || \"người dùng\"}`,\r\n        );\r\n        setRelationship(\"NONE\");\r\n      } else {\r\n        toast.error(\"Không thể từ chối lời mời kết bạn. Vui lòng thử lại sau.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error rejecting friend request:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi từ chối lời mời kết bạn\");\r\n    } finally {\r\n      setIsRejectingRequest(false);\r\n    }\r\n  };\r\n\r\n  // State để theo dõi khi nào cần cập nhật UI\r\n  const [profileUpdateTrigger, setProfileUpdateTrigger] = useState<number>(0);\r\n\r\n  // Cập nhật UI khi profileUpdateTrigger thay đổi\r\n  useEffect(() => {\r\n    if (profileUpdateTrigger > 0) {\r\n      // Cập nhật lại thông tin người dùng từ store\r\n      const updatedUser = useAuthStore.getState().user;\r\n      if (updatedUser && isOwnProfile) {\r\n        // Force re-render component với dữ liệu mới nhất\r\n        console.log(\"Cập nhật UI sau khi thay đổi ảnh đại diện\");\r\n      }\r\n    }\r\n  }, [profileUpdateTrigger, isOwnProfile]);\r\n\r\n  const handleProfilePictureUpload = async (\r\n    e: React.ChangeEvent<HTMLInputElement>,\r\n  ) => {\r\n    if (!e.target.files || e.target.files.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const file = e.target.files[0];\r\n    setIsUploading(true);\r\n\r\n    try {\r\n      const result = await updateProfilePicture(file);\r\n      if (result.success && result.url) {\r\n        toast.success(result.message || \"Cập nhật ảnh đại diện thành công\");\r\n\r\n        // Kích hoạt cập nhật UI\r\n        setProfileUpdateTrigger((prev) => prev + 1);\r\n\r\n        // Đảm bảo rằng store đã được cập nhật\r\n        const currentUser = useAuthStore.getState().user;\r\n        if (currentUser && currentUser.userInfo) {\r\n          // Cập nhật lại store với URL mới và timestamp mới\r\n          const updatedUser = {\r\n            ...currentUser,\r\n            userInfo: {\r\n              ...currentUser.userInfo,\r\n              profilePictureUrl: `${result.url}?t=${new Date().getTime()}`,\r\n            },\r\n          };\r\n          useAuthStore.getState().updateUser(updatedUser);\r\n        }\r\n      } else {\r\n        toast.error(result.error || \"Cập nhật ảnh đại diện thất bại\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Lỗi khi tải lên ảnh đại diện:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi tải lên ảnh đại diện\");\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  // Thêm state để kích hoạt cập nhật UI sau khi cập nhật thông tin cơ bản\r\n  const [basicInfoUpdateTrigger, setBasicInfoUpdateTrigger] =\r\n    useState<number>(0);\r\n\r\n  // Cập nhật UI khi basicInfoUpdateTrigger thay đổi\r\n  useEffect(() => {\r\n    if (basicInfoUpdateTrigger > 0) {\r\n      // Cập nhật lại thông tin người dùng từ store\r\n      const updatedUser = useAuthStore.getState().user;\r\n      if (updatedUser && isOwnProfile) {\r\n        // Force re-render component với dữ liệu mới nhất\r\n        console.log(\"Cập nhật UI sau khi thay đổi thông tin cơ bản\");\r\n      }\r\n    }\r\n  }, [basicInfoUpdateTrigger, isOwnProfile]);\r\n\r\n  const handleSubmit = useCallback(async (data: ProfileFormValues) => {\r\n    // Create date from day, month, year\r\n    const dateOfBirth = new Date(`${data.year}-${data.month}-${data.day}`);\r\n\r\n    // Call API to update user basic info\r\n    const result = await updateUserBasicInfo({\r\n      fullName: data.fullName,\r\n      gender: data.gender,\r\n      dateOfBirth: dateOfBirth,\r\n      bio: data.bio,\r\n    });\r\n\r\n    if (result.success) {\r\n      toast.success(\"Thông tin cá nhân đã được cập nhật thành công\");\r\n      setIsEditing(false);\r\n\r\n      // Kích hoạt cập nhật UI\r\n      setBasicInfoUpdateTrigger((prev) => prev + 1);\r\n\r\n      // Đảm bảo rằng store đã được cập nhật\r\n      const currentUser = useAuthStore.getState().user;\r\n      if (currentUser && currentUser.userInfo) {\r\n        // Cập nhật lại store với thông tin mới nhất\r\n        const updatedUser = {\r\n          ...currentUser,\r\n          userInfo: {\r\n            ...currentUser.userInfo,\r\n            fullName: data.fullName || currentUser.userInfo.fullName,\r\n            gender: (data.gender as any) || currentUser.userInfo.gender,\r\n            dateOfBirth: dateOfBirth || currentUser.userInfo.dateOfBirth,\r\n            bio: data.bio || currentUser.userInfo.bio,\r\n          },\r\n        };\r\n        useAuthStore.getState().updateUser(updatedUser);\r\n      }\r\n    } else {\r\n      toast.error(result.error || \"Cập nhật thông tin cá nhân thất bại\");\r\n    }\r\n  }, []);\r\n\r\n  // Memoize handlers\r\n  const handleCancel = useCallback(() => {\r\n    setIsEditing(false);\r\n  }, []);\r\n\r\n  const handleStartEditing = useCallback(() => {\r\n    setIsEditing(true);\r\n  }, []);\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent\r\n        className={`sm:max-w-[425px] h-auto !p-0 mt-0 mb-16 max-h-[90vh] overflow-y-auto no-scrollbar`}\r\n      >\r\n        {/* Disable animations completely to improve performance */}\r\n        {isEditing && isOwnProfile ? (\r\n          <div className=\"p-0\">\r\n            <div className=\"flex items-center px-6 py-2 border-b border-gray-200\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"p-0 mr-2\"\r\n                onClick={handleCancel}\r\n              >\r\n                <ChevronLeft className=\"h-5 w-5\" />\r\n              </Button>\r\n              <DialogHeader>\r\n                <DialogTitle className=\"text-base font-medium\">\r\n                  Cập nhật thông tin cá nhân của bạn\r\n                </DialogTitle>\r\n              </DialogHeader>\r\n            </div>\r\n\r\n            <ProfileEditForm\r\n              initialValues={initialFormValues}\r\n              onSubmit={handleSubmit}\r\n              onCancel={handleCancel}\r\n            />\r\n          </div>\r\n        ) : (\r\n          <div className=\"profile-view\">\r\n            <DialogHeader className=\"px-6 py-0 flex flex-row justify-between items-center h-10\">\r\n              <DialogTitle className=\"text-base font-semibold flex items-center h-10\">\r\n                Thông tin cá nhân\r\n              </DialogTitle>\r\n\r\n              <div className=\"flex items-center space-x-1\">\r\n                {isOwnProfile && (\r\n                  <RefreshUserDataButton\r\n                    size=\"icon\"\r\n                    variant=\"ghost\"\r\n                    className=\"h-8 w-8\"\r\n                  />\r\n                )}\r\n              </div>\r\n\r\n              <DialogDescription className=\"sr-only\">\r\n                Xem và chỉnh sửa thông tin tài khoản của bạn\r\n              </DialogDescription>\r\n            </DialogHeader>\r\n            <div className=\"flex flex-col overflow-auto no-scrollbar\">\r\n              {/* Cover Image */}\r\n              <div className=\"relative\">\r\n                <div className=\"relative w-full h-[180px] bg-gray-200\">\r\n                  <div\r\n                    className=\"absolute inset-0 cursor-pointer\"\r\n                    onClick={() => {\r\n                      const imageUrl =\r\n                        coverImageUrl ||\r\n                        (currentUser?.userInfo?.coverImgUrl\r\n                          ? `${currentUser.userInfo.coverImgUrl}?t=${new Date().getTime()}`\r\n                          : \"https://i.ibb.co/yncCwjg/default-cover.jpg\");\r\n                      setViewerImageUrl(imageUrl);\r\n                      setViewerImageAlt(\"Cover Photo\");\r\n                      setIsImageViewerOpen(true);\r\n                    }}\r\n                  >\r\n                    <Image\r\n                      src={\r\n                        coverImageUrl ||\r\n                        (currentUser?.userInfo?.coverImgUrl\r\n                          ? `${currentUser.userInfo.coverImgUrl}?t=${new Date().getTime()}`\r\n                          : \"https://i.ibb.co/yncCwjg/default-cover.jpg\")\r\n                      }\r\n                      alt=\"Cover Photo\"\r\n                      fill\r\n                      className=\"object-cover h-full w-full\"\r\n                      priority={true}\r\n                      key={\r\n                        currentUser?.userInfo?.coverImgUrl || \"default-cover\"\r\n                      }\r\n                      unoptimized={true}\r\n                    />\r\n                  </div>\r\n                  {isOwnProfile && (\r\n                    <div className=\"absolute bottom-2 right-2\">\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"cover-image-upload\"\r\n                        className=\"hidden\"\r\n                        accept=\"image/*\"\r\n                        onChange={async (e) => {\r\n                          if (!e.target.files || e.target.files.length === 0)\r\n                            return;\r\n                          const file = e.target.files[0];\r\n                          try {\r\n                            const result = await updateCoverImage(file);\r\n                            if (result.success && result.url) {\r\n                              toast.success(\r\n                                result.message || \"Cập nhật ảnh bìa thành công\",\r\n                              );\r\n                              // Store đã được cập nhật trong hàm updateCoverImage\r\n                              // và useEffect sẽ tự động cập nhật UI khi currentUser thay đổi\r\n                              // Tuy nhiên, vẫn cập nhật state để đảm bảo UI cập nhật ngay lập tức\r\n                              setCoverImageUrl(\r\n                                result.url + \"?t=\" + new Date().getTime(),\r\n                              );\r\n                            } else {\r\n                              toast.error(\r\n                                result.error || \"Cập nhật ảnh bìa thất bại\",\r\n                              );\r\n                            }\r\n                          } catch (error) {\r\n                            console.error(\"Lỗi khi cập nhật ảnh bìa:\", error);\r\n                            toast.error(\"Đã xảy ra lỗi khi cập nhật ảnh bìa\");\r\n                          }\r\n                        }}\r\n                      />\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"secondary\"\r\n                        className=\"bg-white/80 hover:bg-white rounded-full p-2 h-8 w-8 flex items-center justify-center\"\r\n                        onClick={() =>\r\n                          document.getElementById(\"cover-image-upload\")?.click()\r\n                        }\r\n                      >\r\n                        <Camera className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Profile Picture and Name */}\r\n              <div className=\"flex flex-col items-center -mt-12 mb-1.5\">\r\n                <div className=\"relative\">\r\n                  {currentUser && (\r\n                    <div\r\n                      className=\"cursor-pointer\"\r\n                      onClick={() => {\r\n                        if (currentUser?.userInfo?.profilePictureUrl) {\r\n                          setViewerImageUrl(\r\n                            `${currentUser.userInfo.profilePictureUrl}?t=${new Date().getTime()}`,\r\n                          );\r\n                          setViewerImageAlt(\r\n                            currentUser.userInfo.fullName || \"Profile Picture\",\r\n                          );\r\n                          setIsImageViewerOpen(true);\r\n                        }\r\n                      }}\r\n                    >\r\n                      <UserAvatar user={currentUser} className=\"h-24 w-24\" />\r\n                    </div>\r\n                  )}\r\n                  {isOwnProfile && (\r\n                    <div className=\"absolute bottom-0 right-0\">\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"secondary\"\r\n                        className=\"bg-white rounded-full p-1 h-5 w-5 flex items-center justify-center\"\r\n                        onClick={() => profilePictureInputRef.current?.click()}\r\n                        disabled={isUploading}\r\n                      >\r\n                        <Camera className=\"h-3 w-3\" />\r\n                        <span className=\"sr-only\">Thay đổi ảnh đại diện</span>\r\n                      </Button>\r\n                      <input\r\n                        type=\"file\"\r\n                        ref={profilePictureInputRef}\r\n                        className=\"hidden\"\r\n                        accept=\"image/*\"\r\n                        onChange={handleProfilePictureUpload}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                <div className=\"flex items-center mt-2\">\r\n                  <h3 className=\"text-base font-semibold\">\r\n                    {currentUser?.userInfo?.fullName || \"\"}\r\n                  </h3>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Action Buttons (for other users) */}\r\n              {!isOwnProfile && (\r\n                <div className=\"flex gap-2 px-4\">\r\n                  {isLoadingRelationship ? (\r\n                    <Button disabled className=\"w-full\">\r\n                      <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                      Đang tải...\r\n                    </Button>\r\n                  ) : relationship === \"FRIEND\" ? (\r\n                    <div className=\"flex gap-6 w-full p-2 pb-4\">\r\n                      {user && (\r\n                        <CallButton\r\n                          target={user}\r\n                          targetType=\"USER\"\r\n                          showVideoCall={false}\r\n                        />\r\n                      )}\r\n                      <Button\r\n                        onClick={async () => {\r\n                          if (user?.id) {\r\n                            // Close the dialog\r\n                            onOpenChange(false);\r\n\r\n                            // Open the chat with this user\r\n                            await openChat(user.id, \"USER\");\r\n\r\n                            // Navigate to chat page if not already there\r\n                            router.push(\"/dashboard/chat\");\r\n\r\n                            // Call the onChat callback if provided\r\n                            if (onChat) onChat();\r\n                          }\r\n                        }}\r\n                        className=\"flex-1 bg-[#dbebff] text-[#094bad] font-semibold hover:bg-[#9FC5EA] py-2 px-4 h-8 !border-none !rounded-none\"\r\n                      >\r\n                        Nhắn tin\r\n                      </Button>\r\n                    </div>\r\n                  ) : relationship === \"PENDING_SENT\" ? (\r\n                    <div className=\"flex gap-6 w-full p-2 pb-4\">\r\n                      <Button\r\n                        disabled\r\n                        className=\"flex-1 bg-gray-300 text-gray-700 font-semibold cursor-not-allowed h-8 !border-none !rounded-none\"\r\n                      >\r\n                        Đã gửi lời mời kết bạn\r\n                      </Button>\r\n                    </div>\r\n                  ) : relationship === \"PENDING_RECEIVED\" ? (\r\n                    <div className=\"flex gap-6 w-full p-2 pb-4\">\r\n                      <div className=\"flex gap-2 flex-1\">\r\n                        <Button\r\n                          className=\"flex-1 bg-[#dbebff] text-[#094bad] font-semibold hover:bg-[#9FC5EA] h-8 !border-none !rounded-none\"\r\n                          onClick={handleAcceptRequest}\r\n                          disabled={isAcceptingRequest}\r\n                        >\r\n                          {isAcceptingRequest ? (\r\n                            <>\r\n                              <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                              Đang chấp nhận...\r\n                            </>\r\n                          ) : (\r\n                            \"Chấp nhận\"\r\n                          )}\r\n                        </Button>\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          className=\"flex-1 bg-[#ebecf0] font-semibold hover:bg-[#B3B6B9] py-2 px-4 h-8 !border-none !rounded-none\"\r\n                          onClick={handleRejectRequest}\r\n                          disabled={isRejectingRequest}\r\n                        >\r\n                          {isRejectingRequest ? \"Đang từ chối...\" : \"Từ chối\"}\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  ) : showFriendRequestForm ? (\r\n                    <div className=\"w-full space-y-4\">\r\n                      <div>\r\n                        <label\r\n                          htmlFor=\"message\"\r\n                          className=\"text-sm font-medium\"\r\n                        >\r\n                          Lời nhắn (không bắt buộc)\r\n                        </label>\r\n                        <textarea\r\n                          id=\"message\"\r\n                          placeholder=\"Xin chào, tôi muốn kết bạn với bạn...\"\r\n                          value={requestMessage}\r\n                          onChange={(e) => setRequestMessage(e.target.value)}\r\n                          className=\"mt-1 w-full p-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-0 focus:border-gray-300\"\r\n                          maxLength={150}\r\n                        />\r\n                        <div className=\"text-xs text-right text-gray-500 mt-1\">\r\n                          {requestMessage.length}/150 ký tự\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"flex gap-6 w-full p-2 pb-4\">\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          onClick={toggleFriendRequestForm}\r\n                          className=\"flex-1 bg-[#ebecf0] font-semibold hover:bg-[#B3B6B9] py-2 px-4 h-8 !border-none !rounded-none\"\r\n                        >\r\n                          Hủy\r\n                        </Button>\r\n                        <Button\r\n                          onClick={handleSendFriendRequest}\r\n                          className=\"flex-1 bg-[#dbebff] text-[#094bad] font-semibold hover:bg-[#9FC5EA] py-2 px-4 h-8 !border-none !rounded-none\"\r\n                          disabled={isSendingRequest}\r\n                        >\r\n                          {isSendingRequest ? (\r\n                            <>\r\n                              <div className=\"animate-spin h-4 w-4 border-2 border-[#094bad] rounded-full border-t-transparent mr-2\"></div>\r\n                              Đang gửi...\r\n                            </>\r\n                          ) : (\r\n                            \"Gửi lời mời\"\r\n                          )}\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"flex gap-6 w-full p-2 pb-4\">\r\n                      <Button\r\n                        onClick={toggleFriendRequestForm}\r\n                        className=\"flex-1 bg-[#dbebff] text-[#094bad] font-semibold hover:bg-[#9FC5EA] py-2 px-4 h-8 !border-none !rounded-none\"\r\n                      >\r\n                        <UserPlus className=\"mr-2 h-4 w-4\" />\r\n                        Kết bạn\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Personal Information */}\r\n              <div className=\"px-6 py-4 border-t-4 border-gray-200 bg-white overflow-auto no-scrollbar\">\r\n                <h4 className=\"font-semibold text-sm mb-3\">\r\n                  Thông tin cá nhân\r\n                </h4>\r\n                <div className=\"space-y-2.5\">\r\n                  {/* Chỉ hiển thị bio khi có dữ liệu */}\r\n                  {currentUser?.userInfo?.bio && (\r\n                    <div className=\"grid grid-cols-[100px_1fr] gap-1\">\r\n                      <span className=\"text-xs text-gray-500\">Giới thiệu</span>\r\n                      <span className=\"text-xs text-left\">\r\n                        {currentUser.userInfo.bio}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Chỉ hiển thị giới tính khi có dữ liệu */}\r\n                  {currentUser?.userInfo?.gender && (\r\n                    <div className=\"grid grid-cols-[100px_1fr] gap-1\">\r\n                      <span className=\"text-xs text-gray-500\">Giới tính</span>\r\n                      <span className=\"text-xs text-left\">\r\n                        {currentUser.userInfo.gender === \"FEMALE\"\r\n                          ? \"Nữ\"\r\n                          : currentUser.userInfo.gender === \"MALE\"\r\n                            ? \"Nam\"\r\n                            : \"Khác\"}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Chỉ hiển thị ngày sinh khi có dữ liệu */}\r\n                  {currentUser?.userInfo?.dateOfBirth && (\r\n                    <div className=\"grid grid-cols-[100px_1fr] gap-1\">\r\n                      <span className=\"text-xs text-gray-500\">Ngày sinh</span>\r\n                      <span className=\"text-xs text-left\">\r\n                        {new Date(\r\n                          currentUser.userInfo.dateOfBirth,\r\n                        ).toLocaleDateString(\"vi-VN\", {\r\n                          day: \"2-digit\",\r\n                          month: \"2-digit\",\r\n                          year: \"numeric\",\r\n                        })}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Chỉ hiển thị số điện thoại khi có dữ liệu và là profile của chính mình hoặc là bạn bè */}\r\n                  {currentUser?.phoneNumber &&\r\n                    (isOwnProfile || relationship === \"FRIEND\") && (\r\n                      <div className=\"grid grid-cols-[100px_1fr] gap-1\">\r\n                        <span className=\"text-xs text-gray-500\">\r\n                          Số điện thoại\r\n                        </span>\r\n                        <span className=\"text-xs text-left\">\r\n                          {currentUser.phoneNumber}\r\n                        </span>\r\n                      </div>\r\n                    )}\r\n\r\n                  {/* Chỉ hiển thị email khi có dữ liệu và là profile của chính mình hoặc là bạn bè */}\r\n                  {currentUser?.email &&\r\n                    (isOwnProfile || relationship === \"FRIEND\") && (\r\n                      <div className=\"grid grid-cols-[100px_1fr] gap-1\">\r\n                        <span className=\"text-xs text-gray-500\">Email</span>\r\n                        <span className=\"text-xs text-left\">\r\n                          {currentUser.email}\r\n                        </span>\r\n                      </div>\r\n                    )}\r\n\r\n                  {/* Thông báo về quyền riêng tư số điện thoại */}\r\n                  {isOwnProfile && currentUser?.phoneNumber && (\r\n                    <>\r\n                      <div className=\"h-1\"></div>\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        Chỉ bạn bè có lưu số điện thoại của bạn trong danh bạ\r\n                        của họ mới có thể xem số điện thoại này\r\n                      </p>\r\n                    </>\r\n                  )}\r\n\r\n                  {/* Thông báo về quyền riêng tư khi xem profile người khác không phải bạn bè */}\r\n                  {!isOwnProfile && relationship !== \"FRIEND\" && (\r\n                    <>\r\n                      <div className=\"h-1\"></div>\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        Kết bạn để xem thông tin liên hệ của người dùng này\r\n                      </p>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Photos/Videos - Only shown for other users' profiles */}\r\n              {!isOwnProfile &&\r\n                currentUser?.posts &&\r\n                currentUser.posts.length > 0 && (\r\n                  <div className=\"px-6 py-4 border-t-4 border-gray-200 bg-white mt-4 overflow-auto no-scrollbar\">\r\n                    <div className=\"flex justify-between items-center mb-2\">\r\n                      <h4 className=\"font-medium text-base\">Hình ảnh/Video</h4>\r\n                      <Button variant=\"link\" className=\"text-sm p-0 h-auto\">\r\n                        Xem tất cả\r\n                      </Button>\r\n                    </div>\r\n                    <div className=\"grid grid-cols-4 gap-1\">\r\n                      {Array(8)\r\n                        .fill(0)\r\n                        .map((_, i) => (\r\n                          <div\r\n                            key={i}\r\n                            className=\"aspect-square bg-gray-200 rounded-md overflow-hidden\"\r\n                          >\r\n                            <div className=\"w-full h-full bg-gray-300\"></div>\r\n                          </div>\r\n                        ))}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n              {/* Additional Options for Other User's Profile */}\r\n              {!isOwnProfile && (\r\n                <div className=\"px-2 py-4 border-t-4 border-gray-200 bg-white mt-4 space-y-2 overflow-auto no-scrollbar\">\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"justify-start w-full h-10 px-3\"\r\n                  >\r\n                    <Share2 className=\"h-5 w-5 mr-2 text-gray-500 flex-shrink-0\" />\r\n                    <span className=\"text-sm\">Chia sẻ liên hệ</span>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"justify-start w-full h-10 px-3\"\r\n                    onClick={() => setShowBlockDialog(true)}\r\n                  >\r\n                    <Ban className=\"h-5 w-5 mr-2 text-gray-500 flex-shrink-0\" />\r\n                    <span className=\"text-sm\">Chặn tin nhắn và cuộc gọi</span>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"justify-start w-full h-10 px-3\"\r\n                  >\r\n                    <AlertTriangle className=\"h-5 w-5 mr-2 text-gray-500 flex-shrink-0\" />\r\n                    <span className=\"text-sm\">Báo cáo</span>\r\n                  </Button>\r\n                  {relationship === \"FRIEND\" && (\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      className=\"justify-start w-full h-10 px-3 text-red-500\"\r\n                      onClick={() => setShowRemoveFriendDialog(true)}\r\n                    >\r\n                      <UserMinus className=\"h-5 w-5 mr-2 flex-shrink-0\" />\r\n                      <span className=\"text-sm\">Xóa bạn bè</span>\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Update Button (for own profile) */}\r\n              {isOwnProfile && (\r\n                <div className=\"px-6 py-0.5 border-t border-gray-200 bg-white flex justify-center\">\r\n                  <Button\r\n                    className=\"w-full  hover:bg-gray-100 text-black border-0 shadow-none\"\r\n                    variant=\"ghost\"\r\n                    onClick={handleStartEditing}\r\n                  >\r\n                    <span className=\"flex items-center font-semibold\">\r\n                      <Pencil className=\"h-4 w-4 mr-2\" />\r\n                      Cập nhật\r\n                    </span>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </DialogContent>\r\n\r\n      {/* Image Viewer Dialog */}\r\n      <ImageViewerDialog\r\n        isOpen={isImageViewerOpen}\r\n        onClose={() => setIsImageViewerOpen(false)}\r\n        imageUrl={viewerImageUrl}\r\n        alt={viewerImageAlt}\r\n      />\r\n\r\n      {/* Block User Confirmation Dialog */}\r\n      <AlertDialog\r\n        open={showBlockDialog}\r\n        onOpenChange={(open) => {\r\n          setShowBlockDialog(open);\r\n\r\n          // If dialog is closing, ensure cleanup\r\n          if (!open) {\r\n            // Force cleanup of any potential overlay issues\r\n            document.body.style.pointerEvents = \"auto\";\r\n          }\r\n        }}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Chặn người dùng</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn chặn{\" \"}\r\n              {user?.userInfo?.fullName || \"người dùng này\"}? Người này sẽ không\r\n              thể gửi tin nhắn hoặc gọi điện cho bạn nữa.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isBlocking}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleBlockUser}\r\n              disabled={isBlocking}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isBlocking ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang chặn...\r\n                </>\r\n              ) : (\r\n                \"Chặn\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Remove Friend Confirmation Dialog */}\r\n      <AlertDialog\r\n        open={showRemoveFriendDialog}\r\n        onOpenChange={(open) => {\r\n          setShowRemoveFriendDialog(open);\r\n\r\n          // If dialog is closing, ensure cleanup\r\n          if (!open) {\r\n            // Force cleanup of any potential overlay issues\r\n            document.body.style.pointerEvents = \"auto\";\r\n          }\r\n        }}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xóa bạn bè</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn xóa kết bạn với{\" \"}\r\n              {user?.userInfo?.fullName || \"người dùng này\"}? Hành động này sẽ\r\n              xóa tất cả các cuộc trò chuyện chung và không thể hoàn tác.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isRemovingFriend}>\r\n              Hủy\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleRemoveFriend}\r\n              disabled={isRemovingFriend}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isRemovingFriend ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xóa...\r\n                </>\r\n              ) : (\r\n                \"Xóa bạn bè\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAOA;AAUA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAEA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD;AACvD;AACA;;;;;;;;;;;;;;;;;;;;AAYe,SAAS,cAAc,EACpC,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,eAAe,KAAK,EACpB,MAAM,EACN,MAAM,EACN,+BAA+B,KAAK,EACjB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/D;IAEF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAExD,yBAAyB;IACzB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,wBAAwB;IACxB,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IACtD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,gCAAgC;IAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAEhC,gDAAgD;IAChD,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IAEpD,4DAA4D;IAC5D,MAAM,cAAc,gBAAgB,YAAY,YAAY;IAE5D,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qEAAqE;QACrE,IAAI,gBAAgB,CAAC,MAAM,MAAM,CAAC,QAAQ;QAE1C,QAAQ,GAAG,CACT,mCACA,KAAK,EAAE,EACP,KAAK,QAAQ,EAAE;QAGjB,MAAM,oBAAoB;YACxB,IAAI;gBACF,yBAAyB;gBACzB,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW,IAAI;gBAC3D,QAAQ,GAAG,CAAC,wCAAwC,KAAK,EAAE;gBAC3D,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE,EAAE;gBAC9C,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,iCAAiC;oBACjC,mHAAmH;oBACnH,QAAQ,GAAG,CAAC,4BAA4B,OAAO,IAAI,CAAC,MAAM;oBAC1D,gBAAgB,OAAO,IAAI,CAAC,MAAM,IAAI;oBAEtC,gCAAgC;oBAChC,IACE,OAAO,IAAI,CAAC,MAAM,KAAK,sBACvB,OAAO,IAAI,CAAC,YAAY,EACxB;wBACA,QAAQ,GAAG,CACT,kDACA,OAAO,IAAI,CAAC,YAAY;wBAE1B,oDAAoD;wBACpD,MAAM,mBAAmB,OAAO,IAAI,CAAC,YAAY;wBACjD,IAAI,iBAAiB,EAAE,EAAE;4BACvB,QAAQ,GAAG,CACT,yCACA,iBAAiB,EAAE;4BAErB,aAAa,iBAAiB,EAAE;wBAClC,OAAO;4BACL,QAAQ,KAAK,CAAC;wBAChB;oBACF;gBACF,OAAO;oBACL,gBAAgB;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,gBAAgB;YAClB,SAAU;gBACR,yBAAyB;YAC3B;QACF;QAEA;IACF,GAAG;QAAC;QAAc,MAAM;QAAI,MAAM,UAAU;QAAU;KAAO;IAE7D,wBAAwB;IACxB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,KAAK,eAAe,EAAE;IAE3D,yCAAyC;IACzC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,MAAM,MAAM,aAAa,UAAU,cAC/B,IAAI,KAAK,YAAY,QAAQ,CAAC,WAAW,IACzC;QAEJ,OAAO;YACL,UAAU,aAAa,UAAU,YAAY;YAC7C,QAAQ,aAAa,UAAU,UAAU;YACzC,KAAK,aAAa,UAAU,OAAO;YACnC,KAAK,IAAI,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;YAC1C,OAAO,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;YACnD,MAAM,IAAI,WAAW,GAAG,QAAQ;QAClC;IACF,GAAG;QACD,aAAa,UAAU;QACvB,aAAa,UAAU;QACvB,aAAa,UAAU;QACvB,aAAa,UAAU;QACvB;KACD;IAED,wDAAwD;IACxD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAC9B,IAAM,wBACN;QAAC;KAAqB;IAGxB,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,qFAAqF;YACrF,iBAAiB,YAAY,QAAQ,EAAE,eAAe;QACxD;IACF,GAAG;QAAC;KAAY;IAEhB,6BAA6B;IAC7B,MAAM,0BAA0B;QAC9B,IAAI,CAAC,MAAM,IAAI;QAEf,IAAI;YACF,oBAAoB;YACpB,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW,IAAI;YAC3D,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,oBAAiB,AAAD,EACnC,KAAK,EAAE,EACP,gBACA;YAEF,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,gBAAgB;gBAChB,yBAAyB;YAC3B,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,+BAA+B,EAAE,OAAO,KAAK,EAAE;YAC9D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B;QAC9B,yBAAyB,CAAC;QAC1B,IAAI,uBAAuB;YACzB,kBAAkB;QACpB;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM,IAAI;QAEf,oBAAoB;QACpB,IAAI;YACF,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW,IAAI;YAC3D,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,eAAY,AAAD,EAAE,KAAK,EAAE,EAAE;YAC3C,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,gBAAgB;gBAEhB,gDAAgD;gBAChD,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;gBAEpC,mCAAmC;gBACnC,WAAW;oBACT,0BAA0B;gBAC5B,GAAG;YACL,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO,KAAK,EAAE;YACtD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,IAAI;QAEf,cAAc;QACd,IAAI;YACF,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW,IAAI;YAC3D,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,YAAS,AAAD,EAAE,KAAK,EAAE,EAAE;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE,YAAY,kBAAkB;gBAEtE,gDAAgD;gBAChD,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;gBAEpC,oCAAoC;gBACpC,WAAW;oBACT,mBAAmB;oBACnB,aAAa,QAAQ,2BAA2B;gBAClD,GAAG;gBAEH,6BAA6B;gBAC7B,gBAAgB;YAClB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,2BAA2B,EAAE,OAAO,KAAK,EAAE;YAC1D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,+BAA+B;IAC/B,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM,MAAM,CAAC,WAAW;QAE7B,sBAAsB;QACtB,IAAI;YACF,MAAM,UAAU,MAAM,cAAc;YACpC,IAAI,SAAS;gBACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CACX,CAAC,gCAAgC,EAAE,KAAK,QAAQ,EAAE,YAAY,cAAc;gBAE9E,gBAAgB;YAClB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT;YAEJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,+BAA+B;IAC/B,MAAM,sBAAsB;QAC1B,QAAQ,GAAG,CAAC,8CAA8C;QAC1D,IAAI,CAAC,MAAM,MAAM,CAAC,WAAW;YAC3B,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,sBAAsB;QACtB,IAAI;YACF,QAAQ,GAAG,CAAC,yCAAyC;YACrD,MAAM,UAAU,MAAM,cAAc;YACpC,QAAQ,GAAG,CAAC,yBAAyB;YACrC,IAAI,SAAS;gBACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CACX,CAAC,8BAA8B,EAAE,KAAK,QAAQ,EAAE,YAAY,cAAc;gBAE5E,gBAAgB;YAClB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,4CAA4C;IAC5C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzE,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,uBAAuB,GAAG;YAC5B,6CAA6C;YAC7C,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;YAChD,IAAI,eAAe,cAAc;gBAC/B,iDAAiD;gBACjD,QAAQ,GAAG,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAsB;KAAa;IAEvC,MAAM,6BAA6B,OACjC;QAEA,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;YAClD;QACF;QAEA,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,eAAe;QAEf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;YAC1C,IAAI,OAAO,OAAO,IAAI,OAAO,GAAG,EAAE;gBAChC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,IAAI;gBAEhC,wBAAwB;gBACxB,wBAAwB,CAAC,OAAS,OAAO;gBAEzC,sCAAsC;gBACtC,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;gBAChD,IAAI,eAAe,YAAY,QAAQ,EAAE;oBACvC,kDAAkD;oBAClD,MAAM,cAAc;wBAClB,GAAG,WAAW;wBACd,UAAU;4BACR,GAAG,YAAY,QAAQ;4BACvB,mBAAmB,GAAG,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI;wBAC9D;oBACF;oBACA,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;gBACrC;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,wEAAwE;IACxE,MAAM,CAAC,wBAAwB,0BAA0B,GACvD,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnB,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,yBAAyB,GAAG;YAC9B,6CAA6C;YAC7C,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;YAChD,IAAI,eAAe,cAAc;gBAC/B,iDAAiD;gBACjD,QAAQ,GAAG,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAwB;KAAa;IAEzC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,oCAAoC;QACpC,MAAM,cAAc,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;QAErE,qCAAqC;QACrC,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE;YACvC,UAAU,KAAK,QAAQ;YACvB,QAAQ,KAAK,MAAM;YACnB,aAAa;YACb,KAAK,KAAK,GAAG;QACf;QAEA,IAAI,OAAO,OAAO,EAAE;YAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;YAEb,wBAAwB;YACxB,0BAA0B,CAAC,OAAS,OAAO;YAE3C,sCAAsC;YACtC,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;YAChD,IAAI,eAAe,YAAY,QAAQ,EAAE;gBACvC,4CAA4C;gBAC5C,MAAM,cAAc;oBAClB,GAAG,WAAW;oBACd,UAAU;wBACR,GAAG,YAAY,QAAQ;wBACvB,UAAU,KAAK,QAAQ,IAAI,YAAY,QAAQ,CAAC,QAAQ;wBACxD,QAAQ,AAAC,KAAK,MAAM,IAAY,YAAY,QAAQ,CAAC,MAAM;wBAC3D,aAAa,eAAe,YAAY,QAAQ,CAAC,WAAW;wBAC5D,KAAK,KAAK,GAAG,IAAI,YAAY,QAAQ,CAAC,GAAG;oBAC3C;gBACF;gBACA,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;YACrC;QACF,OAAO;YACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;QAC9B;IACF,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,aAAa;IACf,GAAG,EAAE;IAEL,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;;0BAClC,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAW,CAAC,iFAAiF,CAAC;0BAG7F,aAAa,6BACZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,8OAAC,kIAAA,CAAA,eAAY;8CACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;wCAAC,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAMnD,8OAAC,gJAAA,CAAA,UAAe;4BACd,eAAe;4BACf,UAAU;4BACV,UAAU;;;;;;;;;;;yCAId,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;8CAAiD;;;;;;8CAIxE,8OAAC;oCAAI,WAAU;8CACZ,8BACC,8OAAC,sJAAA,CAAA,UAAqB;wCACpB,MAAK;wCACL,SAAQ;wCACR,WAAU;;;;;;;;;;;8CAKhB,8OAAC,kIAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAAU;;;;;;;;;;;;sCAIzC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,SAAS;oDACP,MAAM,WACJ,iBACA,CAAC,aAAa,UAAU,cACpB,GAAG,YAAY,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI,GAC/D,4CAA4C;oDAClD,kBAAkB;oDAClB,kBAAkB;oDAClB,qBAAqB;gDACvB;0DAEA,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KACE,iBACA,CAAC,aAAa,UAAU,cACpB,GAAG,YAAY,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI,GAC/D,4CAA4C;oDAElD,KAAI;oDACJ,IAAI;oDACJ,WAAU;oDACV,UAAU;oDAIV,aAAa;mDAFX,aAAa,UAAU,eAAe;;;;;;;;;;4CAK3C,8BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,WAAU;wDACV,QAAO;wDACP,UAAU,OAAO;4DACf,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,GAC/C;4DACF,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;4DAC9B,IAAI;gEACF,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;gEACtC,IAAI,OAAO,OAAO,IAAI,OAAO,GAAG,EAAE;oEAChC,wIAAA,CAAA,QAAK,CAAC,OAAO,CACX,OAAO,OAAO,IAAI;oEAEpB,oDAAoD;oEACpD,+DAA+D;oEAC/D,oEAAoE;oEACpE,iBACE,OAAO,GAAG,GAAG,QAAQ,IAAI,OAAO,OAAO;gEAE3C,OAAO;oEACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,OAAO,KAAK,IAAI;gEAEpB;4DACF,EAAE,OAAO,OAAO;gEACd,QAAQ,KAAK,CAAC,6BAA6B;gEAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4DACd;wDACF;;;;;;kEAEF,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS,IACP,SAAS,cAAc,CAAC,uBAAuB;kEAGjD,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,6BACC,8OAAC;oDACC,WAAU;oDACV,SAAS;wDACP,IAAI,aAAa,UAAU,mBAAmB;4DAC5C,kBACE,GAAG,YAAY,QAAQ,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI;4DAEvE,kBACE,YAAY,QAAQ,CAAC,QAAQ,IAAI;4DAEnC,qBAAqB;wDACvB;oDACF;8DAEA,cAAA,8OAAC,2IAAA,CAAA,UAAU;wDAAC,MAAM;wDAAa,WAAU;;;;;;;;;;;gDAG5C,8BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,uBAAuB,OAAO,EAAE;4DAC/C,UAAU;;8EAEV,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,8OAAC;4DACC,MAAK;4DACL,KAAK;4DACL,WAAU;4DACV,QAAO;4DACP,UAAU;;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DACX,aAAa,UAAU,YAAY;;;;;;;;;;;;;;;;;gCAMzC,CAAC,8BACA,8OAAC;oCAAI,WAAU;8CACZ,sCACC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,QAAQ;wCAAC,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;;;;;+CAGzG,iBAAiB,yBACnB,8OAAC;wCAAI,WAAU;;4CACZ,sBACC,8OAAC,wIAAA,CAAA,UAAU;gDACT,QAAQ;gDACR,YAAW;gDACX,eAAe;;;;;;0DAGnB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;oDACP,IAAI,MAAM,IAAI;wDACZ,mBAAmB;wDACnB,aAAa;wDAEb,+BAA+B;wDAC/B,MAAM,SAAS,KAAK,EAAE,EAAE;wDAExB,6CAA6C;wDAC7C,OAAO,IAAI,CAAC;wDAEZ,uCAAuC;wDACvC,IAAI,QAAQ;oDACd;gDACF;gDACA,WAAU;0DACX;;;;;;;;;;;+CAID,iBAAiB,+BACnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,QAAQ;4CACR,WAAU;sDACX;;;;;;;;;;+CAID,iBAAiB,mCACnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,SAAS;oDACT,UAAU;8DAET,mCACC;;0EACE,8OAAC;gEAAI,WAAU;;;;;;4DAA0F;;uEAI3G;;;;;;8DAGJ,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;oDACT,UAAU;8DAET,qBAAqB,oBAAoB;;;;;;;;;;;;;;;;+CAI9C,sCACF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDACC,SAAQ;wDACR,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,IAAG;wDACH,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;wDACV,WAAW;;;;;;kEAEb,8OAAC;wDAAI,WAAU;;4DACZ,eAAe,MAAM;4DAAC;;;;;;;;;;;;;0DAI3B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,SAAS;wDACT,WAAU;kEACX;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,WAAU;wDACV,UAAU;kEAET,iCACC;;8EACE,8OAAC;oEAAI,WAAU;;;;;;gEAA8F;;2EAI/G;;;;;;;;;;;;;;;;;6DAMR,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;8CAS/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAG3C,8OAAC;4CAAI,WAAU;;gDAEZ,aAAa,UAAU,qBACtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEACb,YAAY,QAAQ,CAAC,GAAG;;;;;;;;;;;;gDAM9B,aAAa,UAAU,wBACtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEACb,YAAY,QAAQ,CAAC,MAAM,KAAK,WAC7B,OACA,YAAY,QAAQ,CAAC,MAAM,KAAK,SAC9B,QACA;;;;;;;;;;;;gDAMX,aAAa,UAAU,6BACtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEACb,IAAI,KACH,YAAY,QAAQ,CAAC,WAAW,EAChC,kBAAkB,CAAC,SAAS;gEAC5B,KAAK;gEACL,OAAO;gEACP,MAAM;4DACR;;;;;;;;;;;;gDAML,aAAa,eACZ,CAAC,gBAAgB,iBAAiB,QAAQ,mBACxC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEAGxC,8OAAC;4DAAK,WAAU;sEACb,YAAY,WAAW;;;;;;;;;;;;gDAM/B,aAAa,SACZ,CAAC,gBAAgB,iBAAiB,QAAQ,mBACxC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEACb,YAAY,KAAK;;;;;;;;;;;;gDAMzB,gBAAgB,aAAa,6BAC5B;;sEACE,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;gDAQxC,CAAC,gBAAgB,iBAAiB,0BACjC;;sEACE,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;gCAS5C,CAAC,gBACA,aAAa,SACb,YAAY,KAAK,CAAC,MAAM,GAAG,mBACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwB;;;;;;8DACtC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAO,WAAU;8DAAqB;;;;;;;;;;;;sDAIxD,8OAAC;4CAAI,WAAU;sDACZ,MAAM,GACJ,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC;oDAEC,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;;;;;;mDAHV;;;;;;;;;;;;;;;;gCAWlB,CAAC,8BACA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,mBAAmB;;8DAElC,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;;8DAEV,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;wCAE3B,iBAAiB,0BAChB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,0BAA0B;;8DAEzC,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;gCAOjC,8BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAQ;wCACR,SAAS;kDAET,cAAA,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYnD,8OAAC,kJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,UAAU;gBACV,KAAK;;;;;;0BAIP,8OAAC,2IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc,CAAC;oBACb,mBAAmB;oBAEnB,uCAAuC;oBACvC,IAAI,CAAC,MAAM;wBACT,gDAAgD;wBAChD,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;oBACtC;gBACF;0BAEA,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCACK;wCAC1B,MAAM,UAAU,YAAY;wCAAiB;;;;;;;;;;;;;sCAIlD,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAY;;;;;;8CACzC,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,2BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;0BAQV,8OAAC,2IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc,CAAC;oBACb,0BAA0B;oBAE1B,uCAAuC;oBACvC,IAAI,CAAC,MAAM;wBACT,gDAAgD;wBAChD,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;oBACtC;gBACF;0BAEA,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCACgB;wCACrC,MAAM,UAAU,YAAY;wCAAiB;;;;;;;;;;;;;sCAIlD,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAkB;;;;;;8CAG/C,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,iCACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 6047, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/SidebarMain.tsx"], "sourcesContent": ["import { useAuthStore } from \"@/stores/authStore\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuPortal,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Button } from \"@/components/ui/button\";\r\n// Sử dụng react-icons thay vì lucide-react\r\nimport { BsChatDotsFill, BsGear, BsDoorOpenFill } from \"react-icons/bs\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport Link from \"next/link\";\r\nimport SettingsDialog from \"./SettingDialog\";\r\n\r\nimport { useState, useCallback, useMemo, memo } from \"react\";\r\nimport ProfileDialog from \"./profile/ProfileDialog\";\r\nimport { LoadingWithMessage } from \"./Loading\";\r\nimport { IconType } from \"react-icons\";\r\nimport { ClockIcon, LucideBookUser } from \"lucide-react\";\r\n\r\n// Định nghĩa các mục điều hướng bên ngoài component để tránh tạo lại mỗi lần render\r\nconst NAV_ITEMS: { path: string; icon: IconType; label: string }[] = [\r\n  { path: \"/dashboard/chat\", icon: BsChatDotsFill, label: \"Chat\" },\r\n  { path: \"/dashboard/contact\", icon: LucideBookUser, label: \"Contacts\" },\r\n  { path: \"/dashboard/post\", icon: ClockIcon, label: \"Posts\" },\r\n];\r\n\r\nfunction Sidebar() {\r\n  const { logout: logoutFromStore, user } = useAuthStore();\r\n  const { unreadReceivedRequests } = useFriendStore();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\r\n  const [isSettingsOpen, setIsSettingsOpen] = useState(false);\r\n  const [isSettingsMenuOpen, setIsSettingsMenuOpen] = useState(false);\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n\r\n  // Sử dụng useCallback để tránh tạo lại hàm mỗi lần render\r\n  const handleLogout = useCallback(async () => {\r\n    setIsLoggingOut(true);\r\n    try {\r\n      const result = await logoutFromStore();\r\n      if (result) {\r\n        router.push(\"/login\");\r\n      } else {\r\n        setIsLoggingOut(false);\r\n      }\r\n    } catch {\r\n      setIsLoggingOut(false);\r\n    }\r\n  }, [logoutFromStore, router]);\r\n\r\n  // Sử dụng useCallback cho các hàm xử lý sự kiện\r\n  const handleProfileOpen = useCallback(() => setIsProfileOpen(true), []);\r\n  const handleSettingsOpen = useCallback(() => setIsSettingsOpen(true), []);\r\n\r\n  // Sử dụng useMemo để tránh tính toán lại mỗi lần render\r\n  const bottomNavItems = useMemo(\r\n    () => [\r\n      {\r\n        icon: BsGear,\r\n        label: \"Settings\",\r\n        isActive: isSettingsMenuOpen,\r\n        isDropdown: true,\r\n      },\r\n      {\r\n        icon: BsDoorOpenFill,\r\n        label: \"Logout\",\r\n        action: handleLogout,\r\n      },\r\n    ],\r\n    [isSettingsMenuOpen, handleLogout],\r\n  );\r\n\r\n  // Hàm kiểm tra đường dẫn hiện tại để xác định mục đang được chọn\r\n  const isActive = useCallback(\r\n    (path: string) => {\r\n      return pathname === path || pathname.startsWith(`${path}/`);\r\n    },\r\n    [pathname],\r\n  );\r\n\r\n  // Tạo avatar fallback text một lần duy nhất khi user thay đổi\r\n  const avatarFallback = useMemo(() => {\r\n    if (!user?.userInfo?.fullName) return \"\";\r\n    return user.userInfo.fullName\r\n      .split(\" \")\r\n      .map((w) => w[0])\r\n      .join(\"\");\r\n  }, [user?.userInfo?.fullName]);\r\n\r\n  // Tạo display name một lần duy nhất khi user thay đổi\r\n  const displayName = useMemo(() => {\r\n    return user?.userInfo?.fullName || \"Guest\";\r\n  }, [user?.userInfo?.fullName]);\r\n\r\n  return (\r\n    <>\r\n      {isLoggingOut && <LoadingWithMessage message=\"Đang đăng xuất...\" />}\r\n      <div className=\"w-16 bg-[#005ae0] text-white flex flex-col items-center py-0 space-y-0 shadow-lg\">\r\n        <div className=\"mt-8 mb-6\">\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Avatar className=\"cursor-pointer h-12 w-12 border-2 border-white hover:border-blue-300 transition-all\">\r\n                {user?.userInfo?.profilePictureUrl &&\r\n                  user.userInfo.profilePictureUrl !== \"\" && (\r\n                    <AvatarImage\r\n                      className=\"object-cover\"\r\n                      src={user.userInfo.profilePictureUrl}\r\n                    />\r\n                  )}\r\n                <AvatarFallback className=\"text-gray\">\r\n                  {avatarFallback}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent\r\n              className=\"w-56\"\r\n              side=\"right\"\r\n              align=\"start\"\r\n              sideOffset={5}\r\n              alignOffset={5}\r\n            >\r\n              <DropdownMenuLabel>{displayName}</DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem>\r\n                  Nâng cấp tài khoản\r\n                  <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem onClick={handleProfileOpen}>\r\n                  Hồ sơ của bạn\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem onClick={handleSettingsOpen}>\r\n                  Cài đặt\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem onClick={handleLogout}>\r\n                  Đăng xuất\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n\r\n        {/* Main navigation buttons */}\r\n        <div className=\"flex flex-col items-center w-full space-y-2\">\r\n          {NAV_ITEMS.map((item) => (\r\n            <div key={item.path} className=\"relative group\">\r\n              <Link href={item.path} scroll={false}>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  className={`flex items-center justify-center text-white p-3 hover:bg-[#0045b8] hover:text-white active:bg-[#0045b8] active:text-white ${isActive(item.path) ? \"bg-[#0045b8]\" : \"\"} [&_svg]:!size-7 !h-12 !w-12 !rounded-2sm`}\r\n                  title={item.label}\r\n                >\r\n                  <div className=\"relative\">\r\n                    <item.icon size={40} />\r\n                    {item.label === \"Contacts\" &&\r\n                      unreadReceivedRequests > 0 && (\r\n                        <div className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center\">\r\n                          {unreadReceivedRequests > 9\r\n                            ? \"9+\"\r\n                            : unreadReceivedRequests}\r\n                        </div>\r\n                      )}\r\n                  </div>\r\n                </Button>\r\n              </Link>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        <div className=\"flex-1\" />\r\n\r\n        <div className=\"flex flex-col items-center w-full space-y-2 pb-8 px-2\">\r\n          {bottomNavItems.map((item, index) => (\r\n            <div key={index} className=\"relative group\">\r\n              {item.isDropdown ? (\r\n                <DropdownMenu\r\n                  open={isSettingsMenuOpen}\r\n                  onOpenChange={setIsSettingsMenuOpen}\r\n                >\r\n                  <DropdownMenuTrigger asChild>\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      className={`flex items-center justify-center text-white hover:bg-[#0045b8] hover:text-white active:bg-[#0045b8] active:text-white ${item.isActive ? \"bg-[#0045b8]\" : \"\"} [&_svg]:!size-7 !h-12 !w-12 !rounded-2sm !p-0`}\r\n                      title={item.label}\r\n                    >\r\n                      <item.icon size={40} className=\"!w-8 !h-8\" />\r\n                    </Button>\r\n                  </DropdownMenuTrigger>\r\n                  <DropdownMenuContent\r\n                    className=\"w-56 ml-8\"\r\n                    side=\"top\"\r\n                    align=\"center\"\r\n                    sideOffset={5}\r\n                    alignOffset={0}\r\n                  >\r\n                    <DropdownMenuItem onClick={handleProfileOpen}>\r\n                      Thông tin tài khoản\r\n                    </DropdownMenuItem>\r\n                    <DropdownMenuItem onClick={handleSettingsOpen}>\r\n                      Cài đặt\r\n                    </DropdownMenuItem>\r\n                    <DropdownMenuItem>Dữ liệu</DropdownMenuItem>\r\n                    <DropdownMenuSub>\r\n                      <DropdownMenuSubTrigger>Ngôn ngữ</DropdownMenuSubTrigger>\r\n                      <DropdownMenuPortal>\r\n                        <DropdownMenuSubContent>\r\n                          <DropdownMenuItem>Tiếng Việt</DropdownMenuItem>\r\n                          <DropdownMenuItem>Tiếng Anh</DropdownMenuItem>\r\n                        </DropdownMenuSubContent>\r\n                      </DropdownMenuPortal>\r\n                    </DropdownMenuSub>\r\n                    <DropdownMenuItem>Hỗ trợ</DropdownMenuItem>\r\n                    <DropdownMenuSeparator />\r\n                    <DropdownMenuItem\r\n                      onClick={handleLogout}\r\n                      className=\"text-red-500\"\r\n                    >\r\n                      Đăng xuất\r\n                    </DropdownMenuItem>\r\n                  </DropdownMenuContent>\r\n                </DropdownMenu>\r\n              ) : (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  className=\"flex items-center justify-center text-white hover:bg-[#0045b8] hover:text-white active:bg-[#0045b8] active:text-white [&_svg]:!size-7 !h-12 !w-12 !rounded-2sm !p-0\"\r\n                  onClick={item.action}\r\n                  title={item.label}\r\n                >\r\n                  <item.icon size={40} className=\"!w-8 !h-8\" />\r\n                </Button>\r\n              )}\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Sử dụng lazy loading cho các dialog để giảm tải ban đầu */}\r\n        {isProfileOpen && (\r\n          <ProfileDialog\r\n            user={user}\r\n            isOpen={isProfileOpen}\r\n            onOpenChange={setIsProfileOpen}\r\n            isOwnProfile={true}\r\n          />\r\n        )}\r\n\r\n        {isSettingsOpen && (\r\n          <SettingsDialog\r\n            isOpen={isSettingsOpen}\r\n            onOpenChange={setIsSettingsOpen}\r\n          />\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\n// Sử dụng memo để tránh re-render không cần thiết\r\nexport default memo(Sidebar);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAcA;AACA;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAAA;;;;;;;;;;;;;;;AAEA,oFAAoF;AACpF,MAAM,YAA+D;IACnE;QAAE,MAAM;QAAmB,MAAM,8IAAA,CAAA,iBAAc;QAAE,OAAO;IAAO;IAC/D;QAAE,MAAM;QAAsB,MAAM,oNAAA,CAAA,iBAAc;QAAE,OAAO;IAAW;IACtE;QAAE,MAAM;QAAmB,MAAM,wMAAA,CAAA,YAAS;QAAE,OAAO;IAAQ;CAC5D;AAED,SAAS;IACP,MAAM,EAAE,QAAQ,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IACrD,MAAM,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IAChD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,0DAA0D;IAC1D,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,IAAI,QAAQ;gBACV,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,gBAAgB;YAClB;QACF,EAAE,OAAM;YACN,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,gDAAgD;IAChD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,iBAAiB,OAAO,EAAE;IACtE,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,kBAAkB,OAAO,EAAE;IAExE,wDAAwD;IACxD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAC3B,IAAM;YACJ;gBACE,MAAM,8IAAA,CAAA,SAAM;gBACZ,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;YACA;gBACE,MAAM,8IAAA,CAAA,iBAAc;gBACpB,OAAO;gBACP,QAAQ;YACV;SACD,EACD;QAAC;QAAoB;KAAa;IAGpC,iEAAiE;IACjE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACzB,CAAC;QACC,OAAO,aAAa,QAAQ,SAAS,UAAU,CAAC,GAAG,KAAK,CAAC,CAAC;IAC5D,GACA;QAAC;KAAS;IAGZ,8DAA8D;IAC9D,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,IAAI,CAAC,MAAM,UAAU,UAAU,OAAO;QACtC,OAAO,KAAK,QAAQ,CAAC,QAAQ,CAC1B,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EACf,IAAI,CAAC;IACV,GAAG;QAAC,MAAM,UAAU;KAAS;IAE7B,sDAAsD;IACtD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,OAAO,MAAM,UAAU,YAAY;IACrC,GAAG;QAAC,MAAM,UAAU;KAAS;IAE7B,qBACE;;YACG,8BAAgB,8OAAC,6HAAA,CAAA,qBAAkB;gBAAC,SAAQ;;;;;;0BAC7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;4CACf,MAAM,UAAU,qBACf,KAAK,QAAQ,CAAC,iBAAiB,KAAK,oBAClC,8OAAC,kIAAA,CAAA,cAAW;gDACV,WAAU;gDACV,KAAK,KAAK,QAAQ,CAAC,iBAAiB;;;;;;0DAG1C,8OAAC,kIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB;;;;;;;;;;;;;;;;;8CAIP,8OAAC,4IAAA,CAAA,sBAAmB;oCAClB,WAAU;oCACV,MAAK;oCACL,OAAM;oCACN,YAAY;oCACZ,aAAa;;sDAEb,8OAAC,4IAAA,CAAA,oBAAiB;sDAAE;;;;;;sDACpB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,oBAAiB;;8DAChB,8OAAC,4IAAA,CAAA,mBAAgB;;wDAAC;sEAEhB,8OAAC,4IAAA,CAAA,uBAAoB;sEAAC;;;;;;;;;;;;8DAExB,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,SAAS;8DAAmB;;;;;;8DAG9C,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,SAAS;8DAAoB;;;;;;;;;;;;sDAIjD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,oBAAiB;sDAChB,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;gDAAC,SAAS;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjD,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;gCAAoB,WAAU;0CAC7B,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,KAAK,IAAI;oCAAE,QAAQ;8CAC7B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAW,CAAC,0HAA0H,EAAE,SAAS,KAAK,IAAI,IAAI,iBAAiB,GAAG,yCAAyC,CAAC;wCAC5N,OAAO,KAAK,KAAK;kDAEjB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,KAAK,IAAI;oDAAC,MAAM;;;;;;gDAChB,KAAK,KAAK,KAAK,cACd,yBAAyB,mBACvB,8OAAC;oDAAI,WAAU;8DACZ,yBAAyB,IACtB,OACA;;;;;;;;;;;;;;;;;;;;;;+BAdR,KAAK,IAAI;;;;;;;;;;kCAwBvB,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;gCAAgB,WAAU;0CACxB,KAAK,UAAU,iBACd,8OAAC,4IAAA,CAAA,eAAY;oCACX,MAAM;oCACN,cAAc;;sDAEd,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,OAAO;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAW,CAAC,sHAAsH,EAAE,KAAK,QAAQ,GAAG,iBAAiB,GAAG,8CAA8C,CAAC;gDACvN,OAAO,KAAK,KAAK;0DAEjB,cAAA,8OAAC,KAAK,IAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;;;;;sDAGnC,8OAAC,4IAAA,CAAA,sBAAmB;4CAClB,WAAU;4CACV,MAAK;4CACL,OAAM;4CACN,YAAY;4CACZ,aAAa;;8DAEb,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,SAAS;8DAAmB;;;;;;8DAG9C,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,SAAS;8DAAoB;;;;;;8DAG/C,8OAAC,4IAAA,CAAA,mBAAgB;8DAAC;;;;;;8DAClB,8OAAC,4IAAA,CAAA,kBAAe;;sEACd,8OAAC,4IAAA,CAAA,yBAAsB;sEAAC;;;;;;sEACxB,8OAAC,4IAAA,CAAA,qBAAkB;sEACjB,cAAA,8OAAC,4IAAA,CAAA,yBAAsB;;kFACrB,8OAAC,4IAAA,CAAA,mBAAgB;kFAAC;;;;;;kFAClB,8OAAC,4IAAA,CAAA,mBAAgB;kFAAC;;;;;;;;;;;;;;;;;;;;;;;8DAIxB,8OAAC,4IAAA,CAAA,mBAAgB;8DAAC;;;;;;8DAClB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDACf,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;yDAML,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,KAAK,MAAM;oCACpB,OAAO,KAAK,KAAK;8CAEjB,cAAA,8OAAC,KAAK,IAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;+BAvD3B;;;;;;;;;;oBA+Db,+BACC,8OAAC,8IAAA,CAAA,UAAa;wBACZ,MAAM;wBACN,QAAQ;wBACR,cAAc;wBACd,cAAc;;;;;;oBAIjB,gCACC,8OAAC,mIAAA,CAAA,UAAc;wBACb,QAAQ;wBACR,cAAc;;;;;;;;;;;;;;AAM1B;qDAGe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 6567, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/app/%28protected%29/dashboard/layout.tsx"], "sourcesContent": ["\"use client\";\r\nimport Chat<PERSON>ocketHandler from \"@/components/chat/ChatSocketHandler\";\r\nimport { useEffect } from \"react\";\r\nimport Sidebar from \"@/components/SidebarMain\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\n\r\nexport default function ProtectedLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const { accessToken } = useAuthStore();\r\n  const { fetchFriends } = useFriendStore();\r\n\r\n  // Initialize friend data when dashboard loads\r\n  useEffect(() => {\r\n    if (accessToken) {\r\n      fetchFriends();\r\n    }\r\n  }, [accessToken, fetchFriends]);\r\n\r\n  return (\r\n    <div className=\"protected-layout h-screen w-full flex overflow-hidden\">\r\n      <div className=\"flex h-full bg-gray-100\">\r\n        {/* Sidebar trái - Tabs */}\r\n        <Sidebar />\r\n        <ChatSocketHandler />\r\n      </div>\r\n      <div className=\"flex flex-col flex-1\">{children}</div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;;AAOe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IACnC,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IAEtC,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf;QACF;IACF,GAAG;QAAC;QAAa;KAAa;IAE9B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,iIAAA,CAAA,UAAO;;;;;kCACR,8OAAC,+IAAA,CAAA,UAAiB;;;;;;;;;;;0BAEpB,8OAAC;gBAAI,WAAU;0BAAwB;;;;;;;;;;;;AAG7C", "debugId": null}}]}