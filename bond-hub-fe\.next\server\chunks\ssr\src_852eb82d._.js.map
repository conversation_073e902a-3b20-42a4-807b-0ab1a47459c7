{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/QrLogin/QrLogin.socket.ts"], "sourcesContent": ["import { Socket } from \"socket.io-client\";\r\nimport { QrStatusData } from \"@/types/qrCode\";\r\n\r\n// Define a type for socket data that can be in various formats\r\ntype SocketData = QrStatusData | QrStatusData[] | string | unknown;\r\n\r\n/**\r\n * Subscribes to QR code status events on the socket\r\n * @param socket The socket.io client instance\r\n * @param qrToken The QR token to subscribe to\r\n * @param handleQrStatus Callback function to handle QR status updates\r\n */\r\nexport const subscribeToQrEvents = (\r\n  socket: Socket,\r\n  qrToken: string,\r\n  handleQrStatus: (data: QrStatusData) => void,\r\n) => {\r\n  if (!socket || !qrToken) return;\r\n\r\n  // Format 1: qr-status-{token}\r\n  console.log(`Subscribing to qr-status-${qrToken} events`);\r\n  socket.on(`qr-status-${qrToken}`, (data: SocketData) => {\r\n    console.log(`Received qr-status-${qrToken} event:`, data);\r\n    try {\r\n      // Nếu data là mảng, l<PERSON><PERSON> phần tử đầu tiên\r\n      const processedData = Array.isArray(data) ? data[0] : data;\r\n      console.log(`Processed data:`, processedData);\r\n\r\n      // Nếu data là string, thử parse JSON\r\n      if (typeof processedData === \"string\") {\r\n        try {\r\n          const parsedData = JSON.parse(processedData);\r\n          console.log(`Parsed JSON data:`, parsedData);\r\n          handleQrStatus(parsedData);\r\n          return;\r\n        } catch (e) {\r\n          console.warn(`Failed to parse string data as JSON:`, e);\r\n        }\r\n      }\r\n\r\n      handleQrStatus(processedData);\r\n    } catch (error) {\r\n      console.error(`Error processing qr-status-${qrToken} event:`, error);\r\n    }\r\n  });\r\n\r\n  // Format 2: status-{token} (without qr- prefix)\r\n  console.log(`Subscribing to status-${qrToken} events`);\r\n  socket.on(`status-${qrToken}`, (data: SocketData) => {\r\n    console.log(`Received status-${qrToken} event:`, data);\r\n    try {\r\n      // Nếu data là mảng, lấy phần tử đầu tiên\r\n      const processedData = Array.isArray(data) ? data[0] : data;\r\n      console.log(`Processed data:`, processedData);\r\n\r\n      // Nếu data là string, thử parse JSON\r\n      if (typeof processedData === \"string\") {\r\n        try {\r\n          const parsedData = JSON.parse(processedData);\r\n          console.log(`Parsed JSON data:`, parsedData);\r\n          handleQrStatus(parsedData);\r\n          return;\r\n        } catch (e) {\r\n          console.warn(`Failed to parse string data as JSON:`, e);\r\n        }\r\n      }\r\n\r\n      handleQrStatus(processedData);\r\n    } catch (error) {\r\n      console.error(`Error processing status-${qrToken} event:`, error);\r\n    }\r\n  });\r\n\r\n  // Format 3: just the token as event name\r\n  console.log(`Subscribing to ${qrToken} events`);\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  socket.on(`${qrToken}`, (data: any) => {\r\n    console.log(`Received ${qrToken} event:`, data);\r\n    try {\r\n      // Nếu data là mảng, lấy phần tử đầu tiên\r\n      const processedData = Array.isArray(data) ? data[0] : data;\r\n      console.log(`Processed data:`, processedData);\r\n\r\n      // Nếu data là string, thử parse JSON\r\n      if (typeof processedData === \"string\") {\r\n        try {\r\n          const parsedData = JSON.parse(processedData);\r\n          console.log(`Parsed JSON data:`, parsedData);\r\n          handleQrStatus(parsedData);\r\n          return;\r\n        } catch (e) {\r\n          console.warn(`Failed to parse string data as JSON:`, e);\r\n        }\r\n      }\r\n\r\n      handleQrStatus(processedData);\r\n    } catch (error) {\r\n      console.error(`Error processing ${qrToken} event:`, error);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Unsubscribes from QR code status events on the socket\r\n * @param socket The socket.io client instance\r\n * @param qrToken The QR token to unsubscribe from\r\n */\r\nexport const unsubscribeFromQrEvents = (socket: Socket, qrToken: string) => {\r\n  if (!socket || !qrToken) return;\r\n\r\n  console.log(`Unsubscribing from all qrToken events: ${qrToken}`);\r\n  socket.off(`qr-status-${qrToken}`);\r\n  socket.off(`status-${qrToken}`);\r\n  socket.off(`${qrToken}`);\r\n};\r\n\r\n/**\r\n * Initializes a socket connection with debug logging\r\n * @param socket The socket.io client instance\r\n */\r\nexport const initializeSocketDebugListeners = (socket: Socket) => {\r\n  // Listen for all events (debug)\r\n  socket.onAny((event, ...args) => {\r\n    console.log(`Received event: ${event}`, args);\r\n  });\r\n\r\n  // Log socket connection events\r\n  socket.on(\"connect\", () => {\r\n    console.log(\"Socket connected with ID:\", socket.id);\r\n  });\r\n\r\n  socket.on(\"disconnect\", (reason) => {\r\n    console.log(\"Socket disconnected:\", reason);\r\n  });\r\n\r\n  socket.on(\"connect_error\", (error) => {\r\n    console.error(\"Socket connection error:\", error);\r\n  });\r\n\r\n  socket.on(\"error\", (error) => {\r\n    console.error(\"Socket error:\", error);\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;AAYO,MAAM,sBAAsB,CACjC,QACA,SACA;IAEA,IAAI,CAAC,UAAU,CAAC,SAAS;IAEzB,8BAA8B;IAC9B,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,QAAQ,OAAO,CAAC;IACxD,OAAO,EAAE,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,CAAC;QACjC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ,OAAO,CAAC,EAAE;QACpD,IAAI;YACF,yCAAyC;YACzC,MAAM,gBAAgB,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;YACtD,QAAQ,GAAG,CAAC,CAAC,eAAe,CAAC,EAAE;YAE/B,qCAAqC;YACrC,IAAI,OAAO,kBAAkB,UAAU;gBACrC,IAAI;oBACF,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,QAAQ,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;oBACjC,eAAe;oBACf;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC,CAAC,oCAAoC,CAAC,EAAE;gBACvD;YACF;YAEA,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,QAAQ,OAAO,CAAC,EAAE;QAChE;IACF;IAEA,gDAAgD;IAChD,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,QAAQ,OAAO,CAAC;IACrD,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC;QAC9B,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ,OAAO,CAAC,EAAE;QACjD,IAAI;YACF,yCAAyC;YACzC,MAAM,gBAAgB,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;YACtD,QAAQ,GAAG,CAAC,CAAC,eAAe,CAAC,EAAE;YAE/B,qCAAqC;YACrC,IAAI,OAAO,kBAAkB,UAAU;gBACrC,IAAI;oBACF,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,QAAQ,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;oBACjC,eAAe;oBACf;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC,CAAC,oCAAoC,CAAC,EAAE;gBACvD;YACF;YAEA,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,QAAQ,OAAO,CAAC,EAAE;QAC7D;IACF;IAEA,yCAAyC;IACzC,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,QAAQ,OAAO,CAAC;IAC9C,8DAA8D;IAC9D,OAAO,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC;QACvB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,OAAO,CAAC,EAAE;QAC1C,IAAI;YACF,yCAAyC;YACzC,MAAM,gBAAgB,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;YACtD,QAAQ,GAAG,CAAC,CAAC,eAAe,CAAC,EAAE;YAE/B,qCAAqC;YACrC,IAAI,OAAO,kBAAkB,UAAU;gBACrC,IAAI;oBACF,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,QAAQ,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;oBACjC,eAAe;oBACf;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC,CAAC,oCAAoC,CAAC,EAAE;gBACvD;YACF;YAEA,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,QAAQ,OAAO,CAAC,EAAE;QACtD;IACF;AACF;AAOO,MAAM,0BAA0B,CAAC,QAAgB;IACtD,IAAI,CAAC,UAAU,CAAC,SAAS;IAEzB,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,SAAS;IAC/D,OAAO,GAAG,CAAC,CAAC,UAAU,EAAE,SAAS;IACjC,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,SAAS;IAC9B,OAAO,GAAG,CAAC,GAAG,SAAS;AACzB;AAMO,MAAM,iCAAiC,CAAC;IAC7C,gCAAgC;IAChC,OAAO,KAAK,CAAC,CAAC,OAAO,GAAG;QACtB,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,EAAE;IAC1C;IAEA,+BAA+B;IAC/B,OAAO,EAAE,CAAC,WAAW;QACnB,QAAQ,GAAG,CAAC,6BAA6B,OAAO,EAAE;IACpD;IAEA,OAAO,EAAE,CAAC,cAAc,CAAC;QACvB,QAAQ,GAAG,CAAC,wBAAwB;IACtC;IAEA,OAAO,EAAE,CAAC,iBAAiB,CAAC;QAC1B,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;IAEA,OAAO,EAAE,CAAC,SAAS,CAAC;QAClB,QAAQ,KAAK,CAAC,iBAAiB;IACjC;AACF", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  },\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/qrAuth.action.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport api from \"@/lib/axios\";\r\n\r\nexport interface QrCodeResponse {\r\n  qrToken: string;\r\n  expires_in: number;\r\n}\r\n\r\n/**\r\n * Generates a new QR code for authentication\r\n * @returns Promise with QR code data including token and expiration time\r\n */\r\nexport const generateQrCode = async (): Promise<QrCodeResponse> => {\r\n  try {\r\n    const response = await api.post(\"/qrcode/generate\");\r\n    const { qrToken, expires_in } = response.data;\r\n\r\n    // Log information about the generated QR code\r\n    console.log(\"QR code generated with token:\", qrToken);\r\n    console.log(\"QR expires_in:\", expires_in, \"seconds\");\r\n    console.log(\"Current time:\", new Date().toISOString());\r\n    console.log(\r\n      \"Expiry time:\",\r\n      new Date(Date.now() + expires_in * 1000).toISOString(),\r\n    );\r\n\r\n    return {\r\n      qrToken,\r\n      expires_in,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error generating QR code:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Verifies a QR code token\r\n * @param token The QR code token to verify\r\n * @returns Promise with verification result\r\n */\r\nexport const verifyQrCode = async (token: string) => {\r\n  try {\r\n    const response = await api.post(\"/qrcode/verify\", { token });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error verifying QR code:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Cancels a QR code token\r\n * @param token The QR code token to cancel\r\n * @returns Promise with cancellation result\r\n */\r\nexport const cancelQrCode = async (token: string) => {\r\n  try {\r\n    const response = await api.post(\"/qrcode/cancel\", { token });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error cancelling QR code:\", error);\r\n    throw error;\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAaa,iBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/utils/helpers.ts"], "sourcesContent": ["import { DeviceType } from \"@/types/base\";\r\nimport * as U<PERSON>ars<PERSON> from \"ua-parser-js\";\r\n\r\n/**\r\n * <PERSON><PERSON><PERSON> định thông tin thiết bị dựa trên userAgent\r\n * @returns Thông tin về loại thiết bị và tên thiết bị\r\n */\r\nexport const getDeviceInfo = () => {\r\n  if (typeof window === \"undefined\") {\r\n    return { deviceType: DeviceType.OTHER, deviceName: \"Dell Latitude 5290\" };\r\n  }\r\n\r\n  const parser = new UAParser.UAParser();\r\n  const result = parser.getResult();\r\n\r\n  // Xác định deviceType\r\n  let deviceType: DeviceType;\r\n  const device = result.device.type?.toLowerCase();\r\n  const os = result.os.name?.toLowerCase();\r\n\r\n  if (device === \"mobile\" || /iphone|android/.test(result.ua.toLowerCase())) {\r\n    deviceType = DeviceType.MOBILE;\r\n  } else if (device === \"tablet\" || /ipad/.test(result.ua.toLowerCase())) {\r\n    deviceType = DeviceType.TABLET;\r\n  } else if (os && /mac|win|linux/.test(os)) {\r\n    deviceType = DeviceType.DESKTOP;\r\n  } else {\r\n    deviceType = DeviceType.OTHER;\r\n  }\r\n\r\n  // Lấy deviceName\r\n  const deviceName =\r\n    result.device.model || result.os.name || \"Dell Latitude 5290\";\r\n\r\n  return { deviceType, deviceName };\r\n};\r\n\r\n/**\r\n * Kiểm tra xem một chuỗi có phải là email hợp lệ hay không\r\n * @param input Chuỗi cần kiểm tra\r\n * @returns true nếu là email hợp lệ, false nếu không phải\r\n */\r\nexport const isEmail = (input: string): boolean => {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(input);\r\n};\r\n\r\n/**\r\n * Kiểm tra xem một chuỗi có phải là số điện thoại hợp lệ hay không\r\n * @param input Chuỗi cần kiểm tra\r\n * @returns true nếu là số điện thoại hợp lệ, false nếu không phải\r\n */\r\nexport const isPhoneNumber = (input: string): boolean => {\r\n  const phoneRegex = /^\\d{10,11}$/; // Giả sử số điện thoại Việt Nam có 10-11 chữ số\r\n  return phoneRegex.test(input);\r\n};\r\n\r\n/**\r\n * Định dạng số điện thoại theo định dạng Việt Nam\r\n * @param phone Số điện thoại cần định dạng\r\n * @returns Số điện thoại đã được định dạng\r\n */\r\nexport const formatPhoneNumber = (phone: string): string => {\r\n  if (!phone) return \"\";\r\n\r\n  // Loại bỏ tất cả các ký tự không phải số\r\n  const cleaned = phone.replace(/\\D/g, \"\");\r\n\r\n  // Kiểm tra độ dài và định dạng theo quy tắc Việt Nam\r\n  if (cleaned.length === 10) {\r\n    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;\r\n  } else if (cleaned.length === 11) {\r\n    return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;\r\n  }\r\n\r\n  return cleaned;\r\n};\r\n\r\n/**\r\n * Định dạng ngày tháng theo định dạng dd/mm/yyyy\r\n * @param date Đối tượng Date cần định dạng\r\n * @returns Chuỗi ngày tháng đã được định dạng\r\n */\r\nexport const formatDate = (date: Date): string => {\r\n  const day = date.getDate().toString().padStart(2, \"0\");\r\n  const month = (date.getMonth() + 1).toString().padStart(2, \"0\");\r\n  const year = date.getFullYear();\r\n\r\n  return `${day}/${month}/${year}`;\r\n};\r\n\r\n/**\r\n * Chuyển đổi giới tính từ tiếng Anh sang tiếng Việt\r\n * @param gender Giới tính bằng tiếng Anh (\"male\" hoặc \"female\")\r\n * @returns Giới tính bằng tiếng Việt\r\n */\r\nexport const translateGender = (gender: string): string => {\r\n  if (gender.toLowerCase() === \"male\") return \"Nam\";\r\n  if (gender.toLowerCase() === \"female\") return \"Nữ\";\r\n  return gender;\r\n};\r\n\r\n/**\r\n * Kiểm tra xem một chuỗi có phải là họ tên tiếng Việt hợp lệ hay không\r\n * @param input Chuỗi cần kiểm tra\r\n * @returns true nếu là họ tên tiếng Việt hợp lệ, false nếu không phải\r\n */\r\nexport const isVietnameseName = (input: string): boolean => {\r\n  // Regex cho tên tiếng Việt có dấu hoặc không dấu\r\n  // Cho phép chữ cái, dấu cách và dấu tiếng Việt\r\n  // Yêu cầu ít nhất 2 từ (họ và tên)\r\n  const vietnameseNameRegex =\r\n    /^[A-Za-zÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ]+(\\s[A-Za-zÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ]+)+$/;\r\n  return vietnameseNameRegex.test(input);\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAMO,MAAM,gBAAgB;IAC3B,wCAAmC;QACjC,OAAO;YAAE,YAAY,oHAAA,CAAA,aAAU,CAAC,KAAK;YAAE,YAAY;QAAqB;IAC1E;;IAEA,MAAM;IACN,MAAM;IAEN,sBAAsB;IACtB,IAAI;IACJ,MAAM;IACN,MAAM;IAYN,iBAAiB;IACjB,MAAM;AAIR;AAOO,MAAM,UAAU,CAAC;IACtB,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAOO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,aAAa,eAAe,gDAAgD;IAClF,OAAO,WAAW,IAAI,CAAC;AACzB;AAOO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,OAAO,OAAO;IAEnB,yCAAyC;IACzC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,qDAAqD;IACrD,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;IAC5E,OAAO,IAAI,QAAQ,MAAM,KAAK,IAAI;QAChC,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;IAC5E;IAEA,OAAO;AACT;AAOO,MAAM,aAAa,CAAC;IACzB,MAAM,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAClD,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAC3D,MAAM,OAAO,KAAK,WAAW;IAE7B,OAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM;AAClC;AAOO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,OAAO,WAAW,OAAO,QAAQ,OAAO;IAC5C,IAAI,OAAO,WAAW,OAAO,UAAU,OAAO;IAC9C,OAAO;AACT;AAOO,MAAM,mBAAmB,CAAC;IAC/B,iDAAiD;IACjD,+CAA+C;IAC/C,mCAAmC;IACnC,MAAM,sBACJ;IACF,OAAO,oBAAoB,IAAI,CAAC;AAClC", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/QrLogin/QrLogin.tsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport { QRCodeCanvas } from \"qrcode.react\";\r\nimport { useAuthStore } from \"../../stores/authStore\";\r\nimport {\r\n  subscribeToQrEvents,\r\n  unsubscribeFromQrEvents,\r\n  initializeSocketDebugListeners,\r\n} from \"@/components/QrLogin/QrLogin.socket\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Button } from \"../ui/button\";\r\nimport {\r\n  Loader2,\r\n  RefreshCw,\r\n  Clock,\r\n  AlertCircle,\r\n  Check,\r\n  Smartphone,\r\n} from \"lucide-react\";\r\nimport { io } from \"socket.io-client\";\r\nimport { QrStatusData, UserData } from \"@/types/qrCode\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"../ui/avatar\";\r\nimport { generateQrCode } from \"@/actions/qrAuth.action\";\r\nimport { User } from \"@/types/base\";\r\nimport { getDeviceInfo } from \"@/utils/helpers\";\r\nimport { getSocketInstance } from \"@/hooks/useSocketConnection\";\r\nimport { getUserDataById } from \"@/actions/user.action\";\r\n\r\nexport default function QrLogin() {\r\n  const [qrToken, setQrToken] = useState(\"\");\r\n  const [expiresAt, setExpiresAt] = useState<number>(Date.now() + 300 * 1000); // Mặc định 5 phút từ thời điểm hiện tại\r\n  const [isQrExpired, setIsQrExpired] = useState<boolean>(false);\r\n  const [qrCodeSize, setQrCodeSize] = useState<number>(200);\r\n\r\n  const refreshQrCode = async (\r\n    setIsQrExpired: React.Dispatch<React.SetStateAction<boolean>>,\r\n    setStatus: React.Dispatch<\r\n      React.SetStateAction<\"pending\" | \"scanned\" | \"confirmed\">\r\n    >,\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    setScannedUser: React.Dispatch<React.SetStateAction<any>>,\r\n    setQrToken: React.Dispatch<React.SetStateAction<string>>,\r\n    setExpiresAt: React.Dispatch<React.SetStateAction<number>>,\r\n  ): Promise<void> => {\r\n    console.log(\"refreshQrCode: Setting isQrExpired to false\");\r\n    setIsQrExpired(false);\r\n    setStatus(\"pending\");\r\n    setScannedUser(null);\r\n\r\n    try {\r\n      const { qrToken: newQrToken, expires_in: expiresInSeconds } =\r\n        await generateQrCode();\r\n      const newExpiresAt = Date.now() + expiresInSeconds * 1000;\r\n      console.log(\r\n        `Setting new QR token: ${newQrToken}, expires at: ${new Date(newExpiresAt).toISOString()}`,\r\n      );\r\n      setQrToken(newQrToken);\r\n      setExpiresAt(newExpiresAt);\r\n    } catch (error) {\r\n      console.error(\"Error refreshing QR code:\", error);\r\n      throw error;\r\n    }\r\n  };\r\n  // Log khi isQrExpired thay đổi\r\n  useEffect(() => {\r\n    console.log(`isQrExpired changed to: ${isQrExpired}`);\r\n  }, [isQrExpired]);\r\n  const [scannedUser, setScannedUser] = useState<UserData | null>(null);\r\n  const [status, setStatus] = useState<\"pending\" | \"scanned\" | \"confirmed\">(\r\n    \"pending\",\r\n  );\r\n  const { setAuth, setTokens } = useAuthStore();\r\n  const router = useRouter();\r\n\r\n  // Handle responsive QR code size\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setQrCodeSize(window.innerWidth < 640 ? 160 : 200);\r\n    };\r\n\r\n    // Set initial size\r\n    handleResize();\r\n\r\n    // Add event listener\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Initialize socket and fetch QR code when component mounts\r\n  useEffect(() => {\r\n    console.log(\"Component mounted, initializing socket and fetching QR code\");\r\n    // Khởi tạo socket khi component mount với namespace /qr-code\r\n    const socket = io(\r\n      `${process.env.NEXT_PUBLIC_WS_URL || \"http://localhost:3000\"}/qr-code`,\r\n      {\r\n        transports: [\"websocket\"],\r\n        autoConnect: true,\r\n      },\r\n    );\r\n\r\n    // Initialize socket debug listeners\r\n    console.log(\"Socket initialized with namespace /qr-code\");\r\n    initializeSocketDebugListeners(socket);\r\n\r\n    // Generate QR code khi component mount\r\n    const fetchQrCode = async () => {\r\n      try {\r\n        await refreshQrCode(\r\n          setIsQrExpired,\r\n          setStatus,\r\n          setScannedUser,\r\n          setQrToken,\r\n          setExpiresAt,\r\n        );\r\n      } catch (error) {\r\n        console.error(\"Error generating QR code:\", error);\r\n      }\r\n    };\r\n\r\n    fetchQrCode();\r\n\r\n    // Cleanup\r\n    return () => {\r\n      console.log(\"Closing socket connection\");\r\n      socket.close();\r\n    };\r\n  }, []);\r\n\r\n  // Handle QR token changes and subscribe to events\r\n  useEffect(() => {\r\n    console.log(`QR token changed: ${qrToken}, isExpired: ${isQrExpired}`);\r\n    if (!qrToken) return;\r\n\r\n    // Create a new socket connection for this token\r\n    const socket = io(\r\n      `${process.env.NEXT_PUBLIC_WS_URL || \"http://localhost:3000\"}/qr-code`,\r\n      {\r\n        transports: [\"websocket\"],\r\n        autoConnect: true,\r\n      },\r\n    );\r\n\r\n    if (!socket) {\r\n      console.error(\"Could not create socket\");\r\n      return;\r\n    }\r\n\r\n    // Xử lý sự kiện QR status\r\n    const handleQrStatus = (data: QrStatusData) => {\r\n      console.log(\"QR status update:\", data);\r\n\r\n      // Ensure data is properly formatted\r\n      if (!data || typeof data !== \"object\") {\r\n        console.error(\"Invalid data received:\", data);\r\n        return;\r\n      }\r\n\r\n      switch (data.status) {\r\n        case \"SCANNED\":\r\n          console.log(\r\n            \"Processing SCANNED status with userData:\",\r\n            data.userData,\r\n          );\r\n          setStatus(\"scanned\");\r\n\r\n          // Handle userData\r\n          if (data.userData) {\r\n            try {\r\n              console.log(\"Setting scanned user:\", data.userData);\r\n              // Ensure userData has all required fields\r\n              const safeUserData: UserData = {\r\n                id: data.userData.id || \"\",\r\n                email: data.userData.email || null,\r\n                phoneNumber: data.userData.phoneNumber || null,\r\n                fullName: data.userData.fullName || \"Người dùng\",\r\n                profilePictureUrl: data.userData.profilePictureUrl || null,\r\n              };\r\n              setScannedUser(safeUserData);\r\n            } catch (error) {\r\n              console.error(\"Error setting scanned user:\", error);\r\n            }\r\n          } else {\r\n            console.warn(\"SCANNED event received but no userData found\");\r\n          }\r\n          break;\r\n\r\n        case \"CONFIRMED\":\r\n          setStatus(\"confirmed\");\r\n          if (data.loginData) {\r\n            const { user, accessToken, refreshToken } = data.loginData;\r\n\r\n            // Create a User object from UserData\r\n            const userForAuth: User = {\r\n              id: user.id,\r\n              email: user.email,\r\n              phoneNumber: user.phoneNumber,\r\n              passwordHash: \"\", // Required by User type but not needed for auth\r\n              createdAt: new Date(),\r\n              updatedAt: new Date(),\r\n              userInfo: {\r\n                id: user.id,\r\n                fullName: user.fullName,\r\n                profilePictureUrl: user.profilePictureUrl,\r\n                coverImgUrl: user.coverImgUrl || null,\r\n                blockStrangers: false,\r\n                createdAt: new Date(),\r\n                updatedAt: new Date(),\r\n                userAuth: {} as User, // Circular reference, not needed for auth\r\n              },\r\n              refreshTokens: [],\r\n              qrCodes: [],\r\n              posts: [],\r\n              stories: [],\r\n              groupMembers: [],\r\n              cloudFiles: [],\r\n              pinnedItems: [],\r\n              sentFriends: [],\r\n              receivedFriends: [],\r\n              contacts: [],\r\n              contactOf: [],\r\n              settings: [],\r\n              postReactions: [],\r\n              hiddenPosts: [],\r\n              addedBy: [],\r\n              notifications: [],\r\n              sentMessages: [],\r\n              receivedMessages: [],\r\n              comments: [],\r\n            };\r\n\r\n            // Get device info for login\r\n            getDeviceInfo(); // Just calling for consistency with regular login, not using the values\r\n\r\n            // Initial auth state with basic information\r\n            setAuth(userForAuth, accessToken);\r\n\r\n            // Save refresh token\r\n            setTokens(accessToken, refreshToken);\r\n\r\n            // Get the main socket instance from the hook (will be established by SocketProvider)\r\n            // This ensures we have the same socket connection as regular login\r\n            const mainSocket = getSocketInstance();\r\n\r\n            // Check if mainSocket exists; if not, it will be created by the SocketProvider\r\n            if (!mainSocket) {\r\n              console.log(\r\n                \"Main socket will be established by SocketProvider after auth is set\",\r\n              );\r\n            } else {\r\n              console.log(\r\n                \"Main socket already exists, will reconnect with new auth token\",\r\n              );\r\n            }\r\n\r\n            // After login, fetch complete user data to get any missing fields\r\n            setTimeout(async () => {\r\n              try {\r\n                // Get full user profile data with all fields\r\n                const userData = await getUserDataById(user.id);\r\n                if (userData.success && userData.user) {\r\n                  // Update user with complete data including cover image and other fields\r\n                  useAuthStore.getState().updateUser(userData.user);\r\n                  console.log(\r\n                    \"User data updated with complete profile including cover image\",\r\n                  );\r\n                }\r\n              } catch (error) {\r\n                console.error(\"Error fetching complete user data:\", error);\r\n              } finally {\r\n                // Redirect after auth is set and user data is fetched, even if fetch fails\r\n                router.push(\"/dashboard\");\r\n              }\r\n            }, 1000);\r\n          }\r\n          break;\r\n\r\n        case \"CANCELLED\":\r\n          console.log(\"Received CANCELLED event from server\");\r\n          setIsQrExpired(true);\r\n          break;\r\n\r\n        case \"EXPIRED\":\r\n          console.log(\"Received EXPIRED event from server\");\r\n          setIsQrExpired(true);\r\n          break;\r\n      }\r\n    };\r\n\r\n    // Subscribe to events for this token\r\n    console.log(`Subscribing to events for token: ${qrToken}`);\r\n    subscribeToQrEvents(socket, qrToken, handleQrStatus);\r\n\r\n    // Cleanup when token changes\r\n    return () => {\r\n      unsubscribeFromQrEvents(socket, qrToken);\r\n    };\r\n  }, [qrToken, router, setAuth, setTokens, isQrExpired]);\r\n\r\n  // Tính và format thời gian còn lại\r\n  const [timeLeft, setTimeLeft] = useState<number>(300); // Mặc định 5 phút (300 giây)\r\n\r\n  // Cập nhật thời gian còn lại mỗi giây\r\n  useEffect(() => {\r\n    console.log(\r\n      `Timer effect triggered - qrToken: ${qrToken}, isExpired: ${isQrExpired}, expiresAt: ${expiresAt}`,\r\n    );\r\n    // Chỉ bắt đầu đếm ngược khi có mã QR và chưa hết hạn\r\n    if (!qrToken || isQrExpired || expiresAt === 0) {\r\n      console.log(\"Timer not started - conditions not met\");\r\n      return;\r\n    }\r\n\r\n    // Cập nhật ngay lần đầu\r\n    const updateTimeLeft = () => {\r\n      const currentTime = Date.now();\r\n      const timeRemaining = expiresAt - currentTime;\r\n      const newTimeLeft = Math.max(0, Math.floor(timeRemaining / 1000));\r\n\r\n      // Log thời gian còn lại mỗi 5 giây để tránh quá nhiều log\r\n      if (newTimeLeft % 5 === 0 || newTimeLeft <= 10) {\r\n        console.log(\r\n          `Time left: ${newTimeLeft}s, Current: ${new Date(currentTime).toISOString()}, Expires: ${new Date(expiresAt).toISOString()}`,\r\n        );\r\n      }\r\n\r\n      setTimeLeft(newTimeLeft);\r\n\r\n      // Kiểm tra nếu hết hạn\r\n      if (newTimeLeft === 0 && !isQrExpired) {\r\n        console.log(\"QR code expired naturally (timer reached zero)\");\r\n        setIsQrExpired(true);\r\n      }\r\n    };\r\n\r\n    // Cập nhật ngay lập tức\r\n    updateTimeLeft();\r\n\r\n    // Thiết lập interval để cập nhật mỗi giây\r\n    const intervalId = setInterval(updateTimeLeft, 1000);\r\n\r\n    // Cleanup khi component unmount\r\n    return () => clearInterval(intervalId);\r\n  }, [expiresAt, isQrExpired, qrToken]);\r\n\r\n  const formatTime = (seconds: number) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs < 10 ? \"0\" : \"\"}${secs}`;\r\n  };\r\n\r\n  // Log component state for debugging\r\n  useEffect(() => {\r\n    console.log(\r\n      \"Component state updated - status:\",\r\n      status,\r\n      \"scannedUser:\",\r\n      scannedUser,\r\n    );\r\n  }, [status, scannedUser]);\r\n\r\n  const renderContent = () => {\r\n    if (!qrToken) {\r\n      console.log(\"Rendering loading UI\");\r\n      return (\r\n        <div className=\"flex flex-col items-center justify-center h-full w-full\">\r\n          <div className=\"mb-4\">\r\n            <div className=\"relative\">\r\n              <div className=\"w-16 h-16 sm:w-20 sm:h-20 rounded-lg flex items-center justify-center\"></div>\r\n              <Loader2 className=\"h-8 w-8 sm:h-10 sm:w-10 text-blue-500 animate-spin absolute inset-0 m-auto\" />\r\n            </div>\r\n          </div>\r\n          <p className=\"text-gray-600 font-medium text-sm sm:text-base\">\r\n            Đang tạo mã QR...\r\n          </p>\r\n          <p className=\"text-xs text-gray-400 mt-2\">\r\n            Vui lòng đợi trong giây lát\r\n          </p>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (isQrExpired) {\r\n      console.log(\"Rendering expired QR code UI\");\r\n      return (\r\n        <div className=\"flex flex-col items-center justify-center h-full w-full\">\r\n          <div className=\"mb-4 bg-orange-100 p-3 sm:p-4 rounded-full\">\r\n            <AlertCircle className=\"h-12 w-12 sm:h-16 sm:w-16 text-orange-500\" />\r\n          </div>\r\n          <p className=\"font-bold text-base sm:text-lg mb-2 sm:mb-3 text-gray-800 text-center\">\r\n            Mã QR đã hết hạn hoặc huỷ bỏ\r\n          </p>\r\n          <p className=\"text-xs sm:text-sm text-gray-500 mb-4 sm:mb-6 text-center px-2 sm:px-4\">\r\n            Mã QR chỉ có hiệu lực trong 5 phút. Vui lòng tạo mã mới để tiếp tục.\r\n          </p>\r\n          <Button\r\n            onClick={async () => {\r\n              try {\r\n                await refreshQrCode(\r\n                  setIsQrExpired,\r\n                  setStatus,\r\n                  setScannedUser,\r\n                  setQrToken,\r\n                  setExpiresAt,\r\n                );\r\n              } catch (error) {\r\n                console.error(\"Error refreshing QR code:\", error);\r\n              }\r\n            }}\r\n            className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 sm:px-6 py-2 sm:py-2.5 rounded-full flex items-center gap-2 transition-all duration-200 shadow-md hover:shadow-lg text-sm sm:text-base\"\r\n          >\r\n            <RefreshCw className=\"h-3 w-3 sm:h-4 sm:w-4\" />\r\n            Tạo mã QR mới\r\n          </Button>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (status === \"scanned\") {\r\n      console.log(\"Status is 'scanned', scannedUser:\", scannedUser);\r\n      if (!scannedUser) {\r\n        console.warn(\r\n          \"scannedUser is null or undefined despite status being 'scanned'\",\r\n        );\r\n        return (\r\n          <div className=\"flex flex-col items-center justify-center h-full w-full\">\r\n            <div className=\"mb-4 bg-blue-100 p-4 rounded-full\">\r\n              <Smartphone className=\"h-16 w-16 text-blue-600\" />\r\n            </div>\r\n            <p className=\"font-bold text-lg text-gray-800\">\r\n              QR code đã được quét\r\n            </p>\r\n            <p className=\"text-sm text-gray-500 mt-2 text-center px-4\">\r\n              Đang chờ thông tin người dùng...\r\n            </p>\r\n          </div>\r\n        );\r\n      }\r\n      console.log(\"Rendering scanned user UI:\", scannedUser);\r\n      return (\r\n        <div className=\"flex flex-col items-center justify-center h-full w-full\">\r\n          <div className=\"mb-4 relative\">\r\n            <Avatar className=\"h-24 w-24\">\r\n              <AvatarImage\r\n                src={scannedUser.profilePictureUrl || undefined}\r\n                alt=\"Profile\"\r\n                className=\"object-cover\"\r\n              />\r\n              <AvatarFallback className=\"text-3xl\">\r\n                {scannedUser.fullName\r\n                  ?.split(\" \")\r\n                  .map((word) => word[0]?.toUpperCase())\r\n                  .join(\"\")\r\n                  .slice(0, 2)}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div className=\"absolute -bottom-2 -right-2 bg-green-500 rounded-full p-1\">\r\n              <Check className=\"h-5 w-5 text-white\" />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"text-center\">\r\n            <p className=\"font-bold text-lg text-gray-800\">\r\n              {scannedUser.fullName || \"Người dùng\"}\r\n            </p>\r\n            <p className=\"text-sm text-gray-500 mt-1\">\r\n              {scannedUser.email || \"\"}\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"mt-6 text-center\">\r\n            <div className=\"flex items-center justify-center mb-2\">\r\n              <div className=\"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"></div>\r\n              <p className=\"text-sm font-medium text-green-600\">\r\n                Đã quét mã QR\r\n              </p>\r\n            </div>\r\n            <p className=\"text-sm text-blue-500 mt-2 px-4\">\r\n              Vui lòng xác nhận đăng nhập trên thiết bị di động của bạn\r\n            </p>\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    if (status === \"confirmed\") {\r\n      console.log(\"Rendering confirmed UI\");\r\n      return (\r\n        <div className=\"flex flex-col items-center justify-center h-full w-full\">\r\n          <div className=\"mb-6 bg-green-100 p-4 rounded-full\">\r\n            <Check className=\"h-16 w-16 text-green-600\" />\r\n          </div>\r\n          <p className=\"font-bold text-xl text-gray-800\">\r\n            Đăng nhập thành công!\r\n          </p>\r\n          <p className=\"text-sm text-gray-500 mt-2\">Đang chuyển hướng...</p>\r\n          <div className=\"mt-4 w-12 h-1\">\r\n            <div className=\"animate-pulse bg-blue-500 h-1 w-full rounded-full\"></div>\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    // Trạng thái mặc định: hiển thị QR code để quét\r\n    console.log(\"Rendering QR code UI for scanning\");\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-between h-full w-full\">\r\n        <div className=\"relative p-3 bg-white rounded-xl mb-4 shadow-sm border border-gray-100\">\r\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 to-white rounded-xl opacity-50\"></div>\r\n          <QRCodeCanvas\r\n            value={qrToken}\r\n            size={qrCodeSize}\r\n            className=\"rounded-lg relative z-10\"\r\n            bgColor=\"#ffffff\"\r\n            fgColor=\"#000000\"\r\n            level=\"H\"\r\n          />\r\n        </div>\r\n        <div className=\"w-fit bg-blue-500 text-white text-xs px-2 py-1 rounded-full mb-2\">\r\n          <div className=\"flex items-center gap-1\">\r\n            <Clock className=\"h-3 w-3\" />\r\n            <span>{formatTime(timeLeft)}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"text-center space-y-3 w-full\">\r\n          <p className=\"text-blue-600 font-semibold text-base\">\r\n            Quét mã để đăng nhập\r\n          </p>\r\n          <p className=\"text-sm text-gray-500 px-4\">\r\n            Mở ứng dụng Vodka trên điện thoại và quét mã này để đăng nhập nhanh\r\n            chóng\r\n          </p>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center min-h-[400px] w-full p-4 sm:p-6\">\r\n      <div className=\"flex flex-col items-center gap-6 max-w-md w-full\">\r\n        <div className=\"w-full sm:w-fit flex flex-col items-center  p-4 sm:p-6 rounded-2xl  bg-white transition-all duration-300\">\r\n          <div className=\"flex flex-col items-center justify-center min-h-[350px] w-full\">\r\n            {renderContent()}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAKA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAAA;AAEA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;AAEe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,KAAK,GAAG,KAAK,MAAM,OAAO,wCAAwC;IACrH,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,MAAM,gBAAgB,OACpB,gBACA,WAGA,8DAA8D;IAC9D,gBACA,YACA;QAEA,QAAQ,GAAG,CAAC;QACZ,eAAe;QACf,UAAU;QACV,eAAe;QAEf,IAAI;YACF,MAAM,EAAE,SAAS,UAAU,EAAE,YAAY,gBAAgB,EAAE,GACzD,MAAM,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD;YACrB,MAAM,eAAe,KAAK,GAAG,KAAK,mBAAmB;YACrD,QAAQ,GAAG,CACT,CAAC,sBAAsB,EAAE,WAAW,cAAc,EAAE,IAAI,KAAK,cAAc,WAAW,IAAI;YAE5F,WAAW;YACX,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IACA,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,aAAa;IACtD,GAAG;QAAC;KAAY;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACjC;IAEF,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAC1C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,UAAU,GAAG,MAAM,MAAM;QAChD;QAEA,mBAAmB;QACnB;QAEA,qBAAqB;QACrB,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;QACZ,6DAA6D;QAC7D,MAAM,SAAS,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EACd,GAAG,iEAAkC,wBAAwB,QAAQ,CAAC,EACtE;YACE,YAAY;gBAAC;aAAY;YACzB,aAAa;QACf;QAGF,oCAAoC;QACpC,QAAQ,GAAG,CAAC;QACZ,CAAA,GAAA,iJAAA,CAAA,iCAA8B,AAAD,EAAE;QAE/B,uCAAuC;QACvC,MAAM,cAAc;YAClB,IAAI;gBACF,MAAM,cACJ,gBACA,WACA,gBACA,YACA;YAEJ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;QACF;QAEA;QAEA,UAAU;QACV,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,OAAO,KAAK;QACd;IACF,GAAG,EAAE;IAEL,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ,aAAa,EAAE,aAAa;QACrE,IAAI,CAAC,SAAS;QAEd,gDAAgD;QAChD,MAAM,SAAS,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EACd,GAAG,iEAAkC,wBAAwB,QAAQ,CAAC,EACtE;YACE,YAAY;gBAAC;aAAY;YACzB,aAAa;QACf;QAGF,IAAI,CAAC,QAAQ;YACX,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,0BAA0B;QAC1B,MAAM,iBAAiB,CAAC;YACtB,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,oCAAoC;YACpC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,KAAK,CAAC,0BAA0B;gBACxC;YACF;YAEA,OAAQ,KAAK,MAAM;gBACjB,KAAK;oBACH,QAAQ,GAAG,CACT,4CACA,KAAK,QAAQ;oBAEf,UAAU;oBAEV,kBAAkB;oBAClB,IAAI,KAAK,QAAQ,EAAE;wBACjB,IAAI;4BACF,QAAQ,GAAG,CAAC,yBAAyB,KAAK,QAAQ;4BAClD,0CAA0C;4BAC1C,MAAM,eAAyB;gCAC7B,IAAI,KAAK,QAAQ,CAAC,EAAE,IAAI;gCACxB,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;gCAC9B,aAAa,KAAK,QAAQ,CAAC,WAAW,IAAI;gCAC1C,UAAU,KAAK,QAAQ,CAAC,QAAQ,IAAI;gCACpC,mBAAmB,KAAK,QAAQ,CAAC,iBAAiB,IAAI;4BACxD;4BACA,eAAe;wBACjB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,+BAA+B;wBAC/C;oBACF,OAAO;wBACL,QAAQ,IAAI,CAAC;oBACf;oBACA;gBAEF,KAAK;oBACH,UAAU;oBACV,IAAI,KAAK,SAAS,EAAE;wBAClB,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,KAAK,SAAS;wBAE1D,qCAAqC;wBACrC,MAAM,cAAoB;4BACxB,IAAI,KAAK,EAAE;4BACX,OAAO,KAAK,KAAK;4BACjB,aAAa,KAAK,WAAW;4BAC7B,cAAc;4BACd,WAAW,IAAI;4BACf,WAAW,IAAI;4BACf,UAAU;gCACR,IAAI,KAAK,EAAE;gCACX,UAAU,KAAK,QAAQ;gCACvB,mBAAmB,KAAK,iBAAiB;gCACzC,aAAa,KAAK,WAAW,IAAI;gCACjC,gBAAgB;gCAChB,WAAW,IAAI;gCACf,WAAW,IAAI;gCACf,UAAU,CAAC;4BACb;4BACA,eAAe,EAAE;4BACjB,SAAS,EAAE;4BACX,OAAO,EAAE;4BACT,SAAS,EAAE;4BACX,cAAc,EAAE;4BAChB,YAAY,EAAE;4BACd,aAAa,EAAE;4BACf,aAAa,EAAE;4BACf,iBAAiB,EAAE;4BACnB,UAAU,EAAE;4BACZ,WAAW,EAAE;4BACb,UAAU,EAAE;4BACZ,eAAe,EAAE;4BACjB,aAAa,EAAE;4BACf,SAAS,EAAE;4BACX,eAAe,EAAE;4BACjB,cAAc,EAAE;4BAChB,kBAAkB,EAAE;4BACpB,UAAU,EAAE;wBACd;wBAEA,4BAA4B;wBAC5B,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,KAAK,wEAAwE;wBAEzF,4CAA4C;wBAC5C,QAAQ,aAAa;wBAErB,qBAAqB;wBACrB,UAAU,aAAa;wBAEvB,qFAAqF;wBACrF,mEAAmE;wBACnE,MAAM,aAAa,CAAA,GAAA,mIAAA,CAAA,oBAAiB,AAAD;wBAEnC,+EAA+E;wBAC/E,IAAI,CAAC,YAAY;4BACf,QAAQ,GAAG,CACT;wBAEJ,OAAO;4BACL,QAAQ,GAAG,CACT;wBAEJ;wBAEA,kEAAkE;wBAClE,WAAW;4BACT,IAAI;gCACF,6CAA6C;gCAC7C,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;gCAC9C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oCACrC,wEAAwE;oCACxE,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,IAAI;oCAChD,QAAQ,GAAG,CACT;gCAEJ;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,sCAAsC;4BACtD,SAAU;gCACR,2EAA2E;gCAC3E,OAAO,IAAI,CAAC;4BACd;wBACF,GAAG;oBACL;oBACA;gBAEF,KAAK;oBACH,QAAQ,GAAG,CAAC;oBACZ,eAAe;oBACf;gBAEF,KAAK;oBACH,QAAQ,GAAG,CAAC;oBACZ,eAAe;oBACf;YACJ;QACF;QAEA,qCAAqC;QACrC,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS;QACzD,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,SAAS;QAErC,6BAA6B;QAC7B,OAAO;YACL,CAAA,GAAA,iJAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ;QAClC;IACF,GAAG;QAAC;QAAS;QAAQ;QAAS;QAAW;KAAY;IAErD,mCAAmC;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,MAAM,6BAA6B;IAEpF,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CACT,CAAC,kCAAkC,EAAE,QAAQ,aAAa,EAAE,YAAY,aAAa,EAAE,WAAW;QAEpG,qDAAqD;QACrD,IAAI,CAAC,WAAW,eAAe,cAAc,GAAG;YAC9C,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,wBAAwB;QACxB,MAAM,iBAAiB;YACrB,MAAM,cAAc,KAAK,GAAG;YAC5B,MAAM,gBAAgB,YAAY;YAClC,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,gBAAgB;YAE3D,0DAA0D;YAC1D,IAAI,cAAc,MAAM,KAAK,eAAe,IAAI;gBAC9C,QAAQ,GAAG,CACT,CAAC,WAAW,EAAE,YAAY,YAAY,EAAE,IAAI,KAAK,aAAa,WAAW,GAAG,WAAW,EAAE,IAAI,KAAK,WAAW,WAAW,IAAI;YAEhI;YAEA,YAAY;YAEZ,uBAAuB;YACvB,IAAI,gBAAgB,KAAK,CAAC,aAAa;gBACrC,QAAQ,GAAG,CAAC;gBACZ,eAAe;YACjB;QACF;QAEA,wBAAwB;QACxB;QAEA,0CAA0C;QAC1C,MAAM,aAAa,YAAY,gBAAgB;QAE/C,gCAAgC;QAChC,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAW;QAAa;KAAQ;IAEpC,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM,KAAK,MAAM;IACjD;IAEA,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CACT,qCACA,QACA,gBACA;IAEJ,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,gBAAgB;QACpB,IAAI,CAAC,SAAS;YACZ,QAAQ,GAAG,CAAC;YACZ,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGvB,8OAAC;wBAAE,WAAU;kCAAiD;;;;;;kCAG9D,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;QAKhD;QAEA,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC;YACZ,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEzB,8OAAC;wBAAE,WAAU;kCAAwE;;;;;;kCAGrF,8OAAC;wBAAE,WAAU;kCAAyE;;;;;;kCAGtF,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;4BACP,IAAI;gCACF,MAAM,cACJ,gBACA,WACA,gBACA,YACA;4BAEJ,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,6BAA6B;4BAC7C;wBACF;wBACA,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAA0B;;;;;;;;;;;;;QAKvD;QAEA,IAAI,WAAW,WAAW;YACxB,QAAQ,GAAG,CAAC,qCAAqC;YACjD,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;sCAExB,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;sCAG/C,8OAAC;4BAAE,WAAU;sCAA8C;;;;;;;;;;;;YAKjE;YACA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,8OAAC,kIAAA,CAAA,cAAW;wCACV,KAAK,YAAY,iBAAiB,IAAI;wCACtC,KAAI;wCACJ,WAAU;;;;;;kDAEZ,8OAAC,kIAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,YAAY,QAAQ,EACjB,MAAM,KACP,IAAI,CAAC,OAAS,IAAI,CAAC,EAAE,EAAE,eACvB,KAAK,IACL,MAAM,GAAG;;;;;;;;;;;;0CAGhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CACV,YAAY,QAAQ,IAAI;;;;;;0CAE3B,8OAAC;gCAAE,WAAU;0CACV,YAAY,KAAK,IAAI;;;;;;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAIpD,8OAAC;gCAAE,WAAU;0CAAkC;;;;;;;;;;;;;;;;;;QAMvD;QAEA,IAAI,WAAW,aAAa;YAC1B,QAAQ,GAAG,CAAC;YACZ,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,8OAAC;wBAAE,WAAU;kCAAkC;;;;;;kCAG/C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;QAIvB;QAEA,gDAAgD;QAChD,QAAQ,GAAG,CAAC;QACZ,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC,sJAAA,CAAA,eAAY;4BACX,OAAO;4BACP,MAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,OAAM;;;;;;;;;;;;8BAGV,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;0CAAM,WAAW;;;;;;;;;;;;;;;;;8BAItB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className,\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  },\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1150, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\r\n);\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n));\r\nLabel.displayName = LabelPrimitive.Root.displayName;\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAwNsB,yBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA6OsB,0BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1208, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA8PsB,gBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/password/ForgotPasswordFlow.tsx"], "sourcesContent": ["import { useState } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  initiateForgotPassword,\r\n  verifyForgotPasswordOtp,\r\n  resetPassword,\r\n} from \"@/actions/auth.action\";\r\nimport { toast } from \"sonner\";\r\nimport { Eye, EyeOff, ArrowLeft } from \"lucide-react\";\r\n\r\nenum ForgotPasswordStep {\r\n  ENTER_EMAIL,\r\n  ENTER_OTP,\r\n  ENTER_NEW_PASSWORD,\r\n  COMPLETE,\r\n}\r\n\r\nexport default function ForgotPasswordFlow({\r\n  onComplete,\r\n}: {\r\n  onComplete?: () => void;\r\n}) {\r\n  const [step, setStep] = useState<ForgotPasswordStep>(\r\n    ForgotPasswordStep.ENTER_EMAIL,\r\n  );\r\n  const [identifier, setIdentifier] = useState(\"\");\r\n  const [otp, setOtp] = useState(\"\");\r\n  const [resetId, setResetId] = useState(\"\");\r\n  const [newPassword, setNewPassword] = useState(\"\");\r\n  const [confirmPassword, setConfirmPassword] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [showNewPassword, setShowNewPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n\r\n  const handleSendOtp = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const result = await initiateForgotPassword(identifier);\r\n\r\n      if (result.success) {\r\n        setResetId(result.resetId);\r\n        setStep(ForgotPasswordStep.ENTER_OTP);\r\n        toast.success(\r\n          `OTP đã được gửi đến ${identifier.includes(\"@\") ? \"email\" : \"số điện thoại\"} của bạn`,\r\n        );\r\n      } else {\r\n        // Xử lý các loại lỗi khác nhau\r\n        if (result.error && result.error.includes(\"400\")) {\r\n          toast.error(\"Vui lòng kiểm tra lại email/số điện thoại của bạn\");\r\n        } else if (result.error && result.error.includes(\"404\")) {\r\n          toast.error(\"Email/số điện thoại chưa được đăng ký trong hệ thống\");\r\n        } else {\r\n          toast.error(\r\n            result.error || \"Vui lòng kiểm tra lại email/số điện thoại của bạn\",\r\n          );\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(error);\r\n\r\n      // Hiển thị thông báo lỗi thân thiện thay vì lỗi kỹ thuật\r\n      toast.error(\"Vui lòng kiểm tra lại email/số điện thoại của bạn\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleVerifyOtp = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const result = await verifyForgotPasswordOtp(resetId, otp);\r\n\r\n      if (result.success) {\r\n        setStep(ForgotPasswordStep.ENTER_NEW_PASSWORD);\r\n        toast.success(\"OTP xác nhận thành công\");\r\n      } else {\r\n        // Xử lý các loại lỗi khác nhau\r\n        if (result.error && result.error.includes(\"400\")) {\r\n          toast.error(\"Mã OTP không hợp lệ\");\r\n        } else if (result.error && result.error.includes(\"expired\")) {\r\n          toast.error(\"Mã OTP đã hết hạn\");\r\n        } else {\r\n          toast.error(result.error || \"Mã OTP không hợp lệ\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(error);\r\n      // Hiển thị thông báo lỗi thân thiện\r\n      toast.error(\"Mã OTP không hợp lệ\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleResetPassword = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Validate passwords\r\n    if (newPassword !== confirmPassword) {\r\n      toast.error(\"Mật khẩu không khớp\");\r\n      return;\r\n    }\r\n\r\n    if (newPassword.length < 8) {\r\n      toast.error(\"Mật khẩu phải có ít nhất 8 ký tự\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const result = await resetPassword(resetId, newPassword);\r\n\r\n      if (result.success) {\r\n        setStep(ForgotPasswordStep.COMPLETE);\r\n        toast.success(\"Đặt lại mật khẩu thành công\");\r\n      } else {\r\n        // Xử lý các loại lỗi khác nhau\r\n        if (result.error && result.error.includes(\"400\")) {\r\n          toast.error(\"Không thể đặt lại mật khẩu. Vui lòng thử lại sau\");\r\n        } else if (result.error && result.error.includes(\"expired\")) {\r\n          toast.error(\"Phiên đặt lại mật khẩu đã hết hạn\");\r\n        } else {\r\n          toast.error(result.error || \"Không thể đặt lại mật khẩu\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(error);\r\n\r\n      // Hiển thị thông báo lỗi thân thiện\r\n      toast.error(\"Không thể đặt lại mật khẩu. Vui lòng thử lại sau\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const renderStepContent = () => {\r\n    switch (step) {\r\n      case ForgotPasswordStep.ENTER_EMAIL:\r\n        return (\r\n          <form onSubmit={handleSendOtp} className=\"space-y-6\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"identifier\">Email hoặc Số điện thoại</Label>\r\n              <Input\r\n                id=\"identifier\"\r\n                type=\"text\"\r\n                value={identifier}\r\n                onChange={(e) => setIdentifier(e.target.value)}\r\n                placeholder=\"Nhập email hoặc số điện thoại của bạn\"\r\n                className=\"focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n                required\r\n              />\r\n            </div>\r\n\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"w-full bg-[#80c7f9] hover:bg-[#0068ff] text-white\"\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? \"Đang gửi OTP...\" : \"Gửi OTP\"}\r\n            </Button>\r\n          </form>\r\n        );\r\n\r\n      case ForgotPasswordStep.ENTER_OTP:\r\n        return (\r\n          <form onSubmit={handleVerifyOtp} className=\"space-y-6\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"otp\">Nhập mã OTP</Label>\r\n              <Input\r\n                id=\"otp\"\r\n                type=\"text\"\r\n                value={otp}\r\n                onChange={(e) => setOtp(e.target.value)}\r\n                placeholder=\"Nhập mã OTP đã gửi đến email hoặc số điện thoại của bạn\"\r\n                className=\"focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n                required\r\n              />\r\n            </div>\r\n\r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                onClick={() => setStep(ForgotPasswordStep.ENTER_EMAIL)}\r\n                disabled={isLoading}\r\n              >\r\n                <ArrowLeft className=\"mr-2 h-4 w-4\" />\r\n                Quay lại\r\n              </Button>\r\n              <Button\r\n                type=\"submit\"\r\n                className=\"flex-1 bg-[#80c7f9] hover:bg-[#0068ff] text-white\"\r\n                disabled={isLoading}\r\n              >\r\n                {isLoading ? \"Đang xác nhận...\" : \"Xác nhận OTP\"}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        );\r\n\r\n      case ForgotPasswordStep.ENTER_NEW_PASSWORD:\r\n        return (\r\n          <form onSubmit={handleResetPassword} className=\"space-y-6\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"newPassword\">Mật khẩu mới</Label>\r\n              <div className=\"relative\">\r\n                <Input\r\n                  id=\"newPassword\"\r\n                  type={showNewPassword ? \"text\" : \"password\"}\r\n                  value={newPassword}\r\n                  onChange={(e) => setNewPassword(e.target.value)}\r\n                  className=\"pr-10 focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n                  required\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500\"\r\n                  onClick={() => setShowNewPassword(!showNewPassword)}\r\n                >\r\n                  {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"confirmPassword\">Xác nhận mật khẩu mới</Label>\r\n              <div className=\"relative\">\r\n                <Input\r\n                  id=\"confirmPassword\"\r\n                  type={showConfirmPassword ? \"text\" : \"password\"}\r\n                  value={confirmPassword}\r\n                  onChange={(e) => setConfirmPassword(e.target.value)}\r\n                  className=\"pr-10 focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n                  required\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500\"\r\n                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                >\r\n                  {showConfirmPassword ? (\r\n                    <EyeOff size={16} />\r\n                  ) : (\r\n                    <Eye size={16} />\r\n                  )}\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                onClick={() => setStep(ForgotPasswordStep.ENTER_OTP)}\r\n                disabled={isLoading}\r\n              >\r\n                <ArrowLeft className=\"mr-2 h-4 w-4\" />\r\n                Quay lại\r\n              </Button>\r\n              <Button\r\n                type=\"submit\"\r\n                className=\"flex-1 bg-[#80c7f9] hover:bg-[#0068ff] text-white\"\r\n                disabled={isLoading}\r\n              >\r\n                {isLoading ? \"Đang đặt lại mật khẩu...\" : \"Đặt lại mật khẩu\"}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        );\r\n\r\n      case ForgotPasswordStep.COMPLETE:\r\n        return (\r\n          <div className=\"space-y-6 text-center\">\r\n            <div className=\"py-8\">\r\n              <div className=\"mx-auto w-12 h-12 rounded-full bg-green-100 flex items-center justify-center\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  className=\"h-6 w-6 text-green-600\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  stroke=\"currentColor\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    strokeWidth={2}\r\n                    d=\"M5 13l4 4L19 7\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"mt-4 text-lg font-medium\">\r\n                Đặt lại mật khẩu thành công\r\n              </h3>\r\n              <p className=\"mt-2 text-sm text-gray-500\">\r\n                Mật khẩu của bạn đã được đặt lại thành công. Bây giờ bạn có thể\r\n                đăng nhập với mật khẩu mới.\r\n              </p>\r\n            </div>\r\n\r\n            <Button\r\n              type=\"button\"\r\n              className=\"w-full bg-[#80c7f9] hover:bg-[#0068ff] text-white\"\r\n              onClick={onComplete}\r\n            >\r\n              Quay lại đăng nhập\r\n            </Button>\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full max-w-md mx-auto\">\r\n      {/* <div className=\"text-center mb-6\">\r\n        <h2 className=\"text-2xl font-bold\">Reset Password</h2>\r\n        {step !== ForgotPasswordStep.COMPLETE && (\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            {step === ForgotPasswordStep.ENTER_EMAIL &&\r\n              \"Enter your email or phone number to receive a verification code\"}\r\n            {step === ForgotPasswordStep.ENTER_OTP &&\r\n              \"Enter the verification code sent to your email or phone number\"}\r\n            {step === ForgotPasswordStep.ENTER_NEW_PASSWORD &&\r\n              \"Create a new password for your account\"}\r\n          </p>\r\n        )}\r\n      </div> */}\r\n\r\n      {renderStepContent()}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAKA;AACA;AAAA;AAAA;;;;;;;;;AAEA,IAAA,AAAK,4CAAA;;;;;WAAA;EAAA;AAOU,SAAS,mBAAmB,EACzC,UAAU,EAGX;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAG/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,gBAAgB,OAAO;QAC3B,EAAE,cAAc;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,yBAAsB,AAAD,EAAE;YAE5C,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW,OAAO,OAAO;gBACzB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CACX,CAAC,oBAAoB,EAAE,WAAW,QAAQ,CAAC,OAAO,UAAU,gBAAgB,QAAQ,CAAC;YAEzF,OAAO;gBACL,+BAA+B;gBAC/B,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ;oBAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ;oBACvD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,OAAO,KAAK,IAAI;gBAEpB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YAEZ,yDAAyD;YACzD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,EAAE,cAAc;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS;YAEtD,IAAI,OAAO,OAAO,EAAE;gBAClB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,+BAA+B;gBAC/B,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ;oBAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,YAAY;oBAC3D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;gBAC9B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YACZ,oCAAoC;YACpC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAEhB,qBAAqB;QACrB,IAAI,gBAAgB,iBAAiB;YACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;YAE5C,IAAI,OAAO,OAAO,EAAE;gBAClB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,+BAA+B;gBAC/B,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ;oBAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,YAAY;oBAC3D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;gBAC9B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YAEZ,oCAAoC;YACpC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN;gBACE,qBACE,8OAAC;oBAAK,UAAU;oBAAe,WAAU;;sCACvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAa;;;;;;8CAC5B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,aAAY;oCACZ,WAAU;oCACV,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;sCAET,YAAY,oBAAoB;;;;;;;;;;;;YAKzC;gBACE,qBACE,8OAAC;oBAAK,UAAU;oBAAiB,WAAU;;sCACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAM;;;;;;8CACrB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;oCACtC,aAAY;oCACZ,WAAU;oCACV,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM;oCACf,UAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU;8CAET,YAAY,qBAAqB;;;;;;;;;;;;;;;;;;YAM5C;gBACE,qBACE,8OAAC;oBAAK,UAAU;oBAAqB,WAAU;;sCAC7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAM,kBAAkB,SAAS;4CACjC,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;4CACV,QAAQ;;;;;;sDAEV,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,mBAAmB,CAAC;sDAElC,gCAAkB,8OAAC,0MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;qEAAS,8OAAC,gMAAA,CAAA,MAAG;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAK3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAkB;;;;;;8CACjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAM,sBAAsB,SAAS;4CACrC,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAClD,WAAU;4CACV,QAAQ;;;;;;sDAEV,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,uBAAuB,CAAC;sDAEtC,oCACC,8OAAC,0MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;qEAEd,8OAAC,gMAAA,CAAA,MAAG;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAMnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM;oCACf,UAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU;8CAET,YAAY,6BAA6B;;;;;;;;;;;;;;;;;;YAMpD;gBACE,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAM;wCACN,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,QAAO;kDAEP,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;8CAIR,8OAAC;oCAAG,WAAU;8CAA2B;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,SAAS;sCACV;;;;;;;;;;;;QAKT;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBAeZ;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1742, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/LoginForm.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\n// import { useRouter } from \"next/navigation\";\r\nimport { Input } from \"./ui/input\";\r\nimport { Button } from \"./ui/button\";\r\nimport { Smartphone, Lock } from \"lucide-react\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { toast } from \"sonner\";\r\nimport ForgotPasswordFlow from \"./password/ForgotPasswordFlow\";\r\nimport { getDeviceInfo } from \"@/utils/helpers\";\r\nimport Loading from \"./Loading\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} from \"@/components/ui/dialog\";\r\n\r\nexport default function LoginForm() {\r\n  const [identifier, setIdentifier] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [showForgotPasswordDialog, setShowForgotPasswordDialog] =\r\n    useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const { login } = useAuthStore();\r\n  // const router = useRouter();\r\n\r\n  // Handle hydration\r\n  useEffect(() => {\r\n    useAuthStore.persist.rehydrate();\r\n  }, []);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n    try {\r\n      const { deviceType, deviceName } = getDeviceInfo();\r\n      console.log(\"Attempting login with:\", {\r\n        identifier,\r\n        deviceName,\r\n        deviceType,\r\n      });\r\n\r\n      const isSuccess = await login(\r\n        identifier,\r\n        password,\r\n        deviceName,\r\n        deviceType,\r\n      );\r\n\r\n      console.log(\"Login result:\", isSuccess);\r\n\r\n      if (isSuccess) {\r\n        toast.success(\"Đăng nhập thành công!\");\r\n        // Không cần chuyển hướng tại đây, AuthProvider sẽ tự động chuyển hướng\r\n        // router.push(\"/dashboard\");\r\n      } else {\r\n        setIsLoading(false);\r\n        toast.error(\"Đăng nhập thất bại! Vui lòng kiểm tra lại thông tin!\");\r\n        console.error(\"Login failed: No response data\");\r\n      }\r\n    } catch (error) {\r\n      setIsLoading(false);\r\n      toast.error(\"Đăng nhập thất bại! \");\r\n      console.error(\"Login error:\", error);\r\n    }\r\n  };\r\n\r\n  const handleForgotPassword = () => {\r\n    setShowForgotPasswordDialog(true);\r\n  };\r\n\r\n  const handleForgotPasswordComplete = () => {\r\n    setShowForgotPasswordDialog(false);\r\n    toast.success(\r\n      \"Password has been reset. You can now log in with your new password.\",\r\n    );\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {isLoading && <Loading />}\r\n      <form onSubmit={handleSubmit} className=\"w-full\">\r\n        <div className=\"flex flex-col gap-2 justify-center items-center\">\r\n          <div className=\"flex items-center gap-2 border-b border-gray-200 mb-3 w-full max-w-[350px] mx-auto\">\r\n            <Smartphone className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n            <Input\r\n              className=\"w-full h-[40px] pl-8 sm:h-[50px] border-none shadow-none focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n              type=\"text\"\r\n              value={identifier}\r\n              onChange={(e) => setIdentifier(e.target.value)}\r\n              placeholder=\"Số điện thoại hoặc Email\"\r\n              required\r\n            />\r\n          </div>\r\n          <div className=\"flex items-center gap-2 border-b border-gray-200 mb-7 w-full max-w-[350px] mx-auto\">\r\n            <Lock className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n            <Input\r\n              className=\"w-full h-[40px] pl-8 sm:h-[50px] border-none shadow-none focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n              type=\"password\"\r\n              name=\"password\"\r\n              value={password}\r\n              onChange={(e) => setPassword(e.target.value)}\r\n              placeholder=\"Mật khẩu\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <Button\r\n            className=\"w-full max-w-[373px] h-[40px] sm:h-[50px] bg-[#80c7f9] hover:bg-[#0068ff] text-white font-semibold rounded-md mb-3\"\r\n            type=\"submit\"\r\n          >\r\n            Đăng nhập với mật khẩu\r\n          </Button>\r\n\r\n          <a\r\n            className=\"cursor-pointer text-sm sm:text-base hover:underline\"\r\n            onClick={handleForgotPassword}\r\n          >\r\n            Quên mật khẩu\r\n          </a>\r\n        </div>\r\n      </form>\r\n\r\n      <Dialog\r\n        open={showForgotPasswordDialog}\r\n        onOpenChange={setShowForgotPasswordDialog}\r\n      >\r\n        <DialogContent className=\"sm:max-w-[500px] p-6\">\r\n          <DialogHeader>\r\n            <DialogTitle className=\"text-center text-xl font-semibold mb-2\">\r\n              Quên mật khẩu\r\n            </DialogTitle>\r\n            <DialogDescription className=\"text-center\">\r\n              Làm theo các bước để đặt lại mật khẩu của bạn\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <ForgotPasswordFlow onComplete={handleForgotPasswordComplete} />\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA,+CAA+C;AAC/C;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAC7B,8BAA8B;IAE9B,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0HAAA,CAAA,eAAY,CAAC,OAAO,CAAC,SAAS;IAChC,GAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,IAAI;YACF,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD;YAC/C,QAAQ,GAAG,CAAC,0BAA0B;gBACpC;gBACA;gBACA;YACF;YAEA,MAAM,YAAY,MAAM,MACtB,YACA,UACA,YACA;YAGF,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,IAAI,WAAW;gBACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,uEAAuE;YACvE,6BAA6B;YAC/B,OAAO;gBACL,aAAa;gBACb,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,aAAa;YACb,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,gBAAgB;QAChC;IACF;IAEA,MAAM,uBAAuB;QAC3B,4BAA4B;IAC9B;IAEA,MAAM,+BAA+B;QACnC,4BAA4B;QAC5B,wIAAA,CAAA,QAAK,CAAC,OAAO,CACX;IAEJ;IAEA,qBACE;;YACG,2BAAa,8OAAC,6HAAA,CAAA,UAAO;;;;;0BACtB,8OAAC;gBAAK,UAAU;gBAAc,WAAU;0BACtC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,WAAU;oCACV,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAGZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,WAAU;oCACV,MAAK;oCACL,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC,kIAAA,CAAA,SAAM;4BACL,WAAU;4BACV,MAAK;sCACN;;;;;;sCAID,8OAAC;4BACC,WAAU;4BACV,SAAS;sCACV;;;;;;;;;;;;;;;;;0BAML,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc;0BAEd,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;8CAAyC;;;;;;8CAGhE,8OAAC,kIAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAAc;;;;;;;;;;;;sCAI7C,8OAAC,oJAAA,CAAA,UAAkB;4BAAC,YAAY;;;;;;;;;;;;;;;;;;;AAK1C", "debugId": null}}, {"offset": {"line": 2102, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root;\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n));\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName;\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName;\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n));\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n));\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName;\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n));\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sLACA,4YACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2300, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className,\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\",\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n));\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2492, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/profile/DateOfBirthPicker.tsx"], "sourcesContent": ["import React, { useEffect, useMemo } from \"react\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface DateOfBirthPickerProps {\r\n  day: string;\r\n  month: string;\r\n  year: string;\r\n  onDayChange: (day: string) => void;\r\n  onMonthChange: (month: string) => void;\r\n  onYearChange: (year: string) => void;\r\n  className?: string;\r\n  showFutureWarning?: boolean;\r\n}\r\n\r\n// Danh sách tháng từ 1-12 với tên tháng - định nghĩa bên ngoài component để tránh tạo lại\r\nconst MONTH_NAMES = [\r\n  \"Tháng 1\",\r\n  \"Tháng 2\",\r\n  \"Tháng 3\",\r\n  \"Tháng 4\",\r\n  \"Tháng 5\",\r\n  \"Tháng 6\",\r\n  \"Tháng 7\",\r\n  \"Tháng 8\",\r\n  \"Tháng 9\",\r\n  \"Tháng 10\",\r\n  \"Tháng 11\",\r\n  \"Tháng 12\",\r\n];\r\n\r\n// H<PERSON>m kiểm tra năm nhuận - định nghĩa bên ngoài component\r\nconst isLeapYear = (year: number): boolean => {\r\n  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;\r\n};\r\n\r\n// Hàm tính số ngày trong tháng - định nghĩa bên ngoài component\r\nconst getDaysInMonth = (month: number, year: number): number => {\r\n  // Tháng 2\r\n  if (month === 2) {\r\n    return isLeapYear(year) ? 29 : 28;\r\n  }\r\n  // Tháng 4, 6, 9, 11 có 30 ngày\r\n  else if ([4, 6, 9, 11].includes(month)) {\r\n    return 30;\r\n  }\r\n  // Các tháng còn lại có 31 ngày\r\n  else {\r\n    return 31;\r\n  }\r\n};\r\n\r\n// Tạo mảng tháng từ 1-12 - định nghĩa bên ngoài component\r\nconst MONTHS = Array.from({ length: 12 }, (_, i) => String(i + 1));\r\n\r\nconst DateOfBirthPicker: React.FC<DateOfBirthPickerProps> = ({\r\n  day,\r\n  month,\r\n  year,\r\n  onDayChange,\r\n  onMonthChange,\r\n  onYearChange,\r\n  className,\r\n  showFutureWarning = true,\r\n}) => {\r\n  // Lấy năm hiện tại một lần duy nhất\r\n  const currentYear = useMemo(() => new Date().getFullYear(), []);\r\n\r\n  // Memoize danh sách năm để tránh tạo lại mỗi lần render\r\n  const years = useMemo(\r\n    () => Array.from({ length: 100 }, (_, i) => String(currentYear - i)),\r\n    [currentYear],\r\n  );\r\n\r\n  // Tính toán số ngày trong tháng và tạo mảng ngày\r\n  const days = useMemo(() => {\r\n    const monthNum = parseInt(month, 10) || 1;\r\n    const yearNum = parseInt(year, 10) || currentYear;\r\n    const daysInMonth = getDaysInMonth(monthNum, yearNum);\r\n    return Array.from({ length: daysInMonth }, (_, i) => String(i + 1));\r\n  }, [month, year, currentYear]);\r\n\r\n  // Xử lý khi ngày vượt quá số ngày trong tháng\r\n  useEffect(() => {\r\n    const monthNum = parseInt(month, 10) || 1;\r\n    const yearNum = parseInt(year, 10) || currentYear;\r\n    const daysInMonth = getDaysInMonth(monthNum, yearNum);\r\n\r\n    if (parseInt(day, 10) > daysInMonth) {\r\n      onDayChange(String(daysInMonth));\r\n    }\r\n  }, [month, year, day, onDayChange, currentYear]);\r\n\r\n  // Kiểm tra ngày tương lai - chỉ khi cả ba giá trị đều có\r\n  useEffect(() => {\r\n    // Chỉ kiểm tra khi cả ba giá trị đều hợp lệ\r\n    if (!day || !month || !year || !showFutureWarning) return;\r\n\r\n    const selectedDay = parseInt(day, 10);\r\n    const selectedMonth = parseInt(month, 10);\r\n    const selectedYear = parseInt(year, 10);\r\n\r\n    if (isNaN(selectedDay) || isNaN(selectedMonth) || isNaN(selectedYear)) {\r\n      return;\r\n    }\r\n\r\n    const selectedDate = new Date(selectedYear, selectedMonth - 1, selectedDay);\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n\r\n    if (selectedDate > today) {\r\n      // Sử dụng setTimeout để tránh hiển thị cảnh báo quá sớm\r\n      const timer = setTimeout(() => {\r\n        toast.warning(\"Ngày sinh không thể là ngày trong tương lai.\");\r\n      }, 500);\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [day, month, year, showFutureWarning]);\r\n\r\n  return (\r\n    <div className={`flex gap-2 ${className || \"\"}`}>\r\n      <Select value={day} onValueChange={onDayChange} name=\"day\">\r\n        <SelectTrigger className={`w-[80px] ${className || \"\"}`}>\r\n          <SelectValue placeholder=\"Ngày\" />\r\n        </SelectTrigger>\r\n        <SelectContent className=\"max-h-[200px] overflow-y-auto\">\r\n          {days.map((d) => (\r\n            <SelectItem key={d} value={d}>\r\n              {d}\r\n            </SelectItem>\r\n          ))}\r\n        </SelectContent>\r\n      </Select>\r\n\r\n      <Select value={month} onValueChange={onMonthChange} name=\"month\">\r\n        <SelectTrigger className={`w-[120px] ${className || \"\"}`}>\r\n          <SelectValue placeholder=\"Tháng\" />\r\n        </SelectTrigger>\r\n        <SelectContent className=\"max-h-[200px] overflow-y-auto\">\r\n          {MONTHS.map((m, index) => (\r\n            <SelectItem key={m} value={m}>\r\n              {MONTH_NAMES[index]}\r\n            </SelectItem>\r\n          ))}\r\n        </SelectContent>\r\n      </Select>\r\n\r\n      <Select value={year} onValueChange={onYearChange} name=\"year\">\r\n        <SelectTrigger className={`w-[100px] ${className || \"\"}`}>\r\n          <SelectValue placeholder=\"Năm\" />\r\n        </SelectTrigger>\r\n        <SelectContent className=\"max-h-[200px] overflow-y-auto\">\r\n          {years.map((y) => (\r\n            <SelectItem key={y} value={y}>\r\n              {y}\r\n            </SelectItem>\r\n          ))}\r\n        </SelectContent>\r\n      </Select>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default React.memo(DateOfBirthPicker);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAOA;;;;;AAaA,0FAA0F;AAC1F,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,0DAA0D;AAC1D,MAAM,aAAa,CAAC;IAClB,OAAO,AAAC,OAAO,MAAM,KAAK,OAAO,QAAQ,KAAM,OAAO,QAAQ;AAChE;AAEA,gEAAgE;AAChE,MAAM,iBAAiB,CAAC,OAAe;IACrC,UAAU;IACV,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,QAAQ,KAAK;IACjC,OAEK,IAAI;QAAC;QAAG;QAAG;QAAG;KAAG,CAAC,QAAQ,CAAC,QAAQ;QACtC,OAAO;IACT,OAEK;QACH,OAAO;IACT;AACF;AAEA,0DAA0D;AAC1D,MAAM,SAAS,MAAM,IAAI,CAAC;IAAE,QAAQ;AAAG,GAAG,CAAC,GAAG,IAAM,OAAO,IAAI;AAE/D,MAAM,oBAAsD,CAAC,EAC3D,GAAG,EACH,KAAK,EACL,IAAI,EACJ,WAAW,EACX,aAAa,EACb,YAAY,EACZ,SAAS,EACT,oBAAoB,IAAI,EACzB;IACC,oCAAoC;IACpC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,OAAO,WAAW,IAAI,EAAE;IAE9D,wDAAwD;IACxD,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAClB,IAAM,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAI,GAAG,CAAC,GAAG,IAAM,OAAO,cAAc,KACjE;QAAC;KAAY;IAGf,iDAAiD;IACjD,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnB,MAAM,WAAW,SAAS,OAAO,OAAO;QACxC,MAAM,UAAU,SAAS,MAAM,OAAO;QACtC,MAAM,cAAc,eAAe,UAAU;QAC7C,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAY,GAAG,CAAC,GAAG,IAAM,OAAO,IAAI;IAClE,GAAG;QAAC;QAAO;QAAM;KAAY;IAE7B,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,SAAS,OAAO,OAAO;QACxC,MAAM,UAAU,SAAS,MAAM,OAAO;QACtC,MAAM,cAAc,eAAe,UAAU;QAE7C,IAAI,SAAS,KAAK,MAAM,aAAa;YACnC,YAAY,OAAO;QACrB;IACF,GAAG;QAAC;QAAO;QAAM;QAAK;QAAa;KAAY;IAE/C,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4CAA4C;QAC5C,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAmB;QAEnD,MAAM,cAAc,SAAS,KAAK;QAClC,MAAM,gBAAgB,SAAS,OAAO;QACtC,MAAM,eAAe,SAAS,MAAM;QAEpC,IAAI,MAAM,gBAAgB,MAAM,kBAAkB,MAAM,eAAe;YACrE;QACF;QAEA,MAAM,eAAe,IAAI,KAAK,cAAc,gBAAgB,GAAG;QAC/D,MAAM,QAAQ,IAAI;QAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;QAExB,IAAI,eAAe,OAAO;YACxB,wDAAwD;YACxD,MAAM,QAAQ,WAAW;gBACvB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAK;QAAO;QAAM;KAAkB;IAExC,qBACE,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;;0BAC7C,8OAAC,kIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAK,eAAe;gBAAa,MAAK;;kCACnD,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAW,CAAC,SAAS,EAAE,aAAa,IAAI;kCACrD,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,aAAY;;;;;;;;;;;kCAE3B,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;kCACtB,KAAK,GAAG,CAAC,CAAC,kBACT,8OAAC,kIAAA,CAAA,aAAU;gCAAS,OAAO;0CACxB;+BADc;;;;;;;;;;;;;;;;0BAOvB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAO,eAAe;gBAAe,MAAK;;kCACvD,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAW,CAAC,UAAU,EAAE,aAAa,IAAI;kCACtD,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,aAAY;;;;;;;;;;;kCAE3B,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;kCACtB,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,8OAAC,kIAAA,CAAA,aAAU;gCAAS,OAAO;0CACxB,WAAW,CAAC,MAAM;+BADJ;;;;;;;;;;;;;;;;0BAOvB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAM,eAAe;gBAAc,MAAK;;kCACrD,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAW,CAAC,UAAU,EAAE,aAAa,IAAI;kCACtD,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,aAAY;;;;;;;;;;;kCAE3B,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;kCACtB,MAAM,GAAG,CAAC,CAAC,kBACV,8OAAC,kIAAA,CAAA,aAAU;gCAAS,OAAO;0CACxB;+BADc;;;;;;;;;;;;;;;;;;;;;;AAQ7B;qDAEe,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 2742, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/input-otp.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { OTPInput, OTPInputContext } from \"input-otp\";\r\nimport { Minus } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst InputOTP = React.forwardRef<\r\n  React.ElementRef<typeof OTPInput>,\r\n  React.ComponentPropsWithoutRef<typeof OTPInput>\r\n>(({ className, containerClassName, ...props }, ref) => (\r\n  <OTPInput\r\n    ref={ref}\r\n    containerClassName={cn(\r\n      \"flex items-center gap-2 has-[:disabled]:opacity-50\",\r\n      containerClassName,\r\n    )}\r\n    className={cn(\"disabled:cursor-not-allowed\", className)}\r\n    {...props}\r\n  />\r\n));\r\nInputOTP.displayName = \"InputOTP\";\r\n\r\nconst InputOTPGroup = React.forwardRef<\r\n  React.ElementRef<\"div\">,\r\n  React.ComponentPropsWithoutRef<\"div\">\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"flex items-center\", className)} {...props} />\r\n));\r\nInputOTPGroup.displayName = \"InputOTPGroup\";\r\n\r\nconst InputOTPSlot = React.forwardRef<\r\n  React.ElementRef<\"div\">,\r\n  React.ComponentPropsWithoutRef<\"div\"> & { index: number }\r\n>(({ index, className, ...props }, ref) => {\r\n  const inputOTPContext = React.useContext(OTPInputContext);\r\n  const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index];\r\n\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative flex h-9 w-9 items-center justify-center border-y border-r border-input text-sm shadow-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md\",\r\n        isActive && \"z-10 ring-1 ring-ring\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {char}\r\n      {hasFakeCaret && (\r\n        <div className=\"pointer-events-none absolute inset-0 flex items-center justify-center\">\r\n          <div className=\"h-4 w-px animate-caret-blink bg-foreground duration-1000\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\nInputOTPSlot.displayName = \"InputOTPSlot\";\r\n\r\nconst InputOTPSeparator = React.forwardRef<\r\n  React.ElementRef<\"div\">,\r\n  React.ComponentPropsWithoutRef<\"div\">\r\n>(({ ...props }, ref) => (\r\n  <div ref={ref} role=\"separator\" {...props}>\r\n    <Minus />\r\n  </div>\r\n));\r\nInputOTPSeparator.displayName = \"InputOTPSeparator\";\r\n\r\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAE,GAAG,OAAO,EAAE,oBAC9C,8OAAC,8IAAA,CAAA,WAAQ;QACP,KAAK;QACL,oBAAoB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACnB,sDACA;QAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;QAAa,GAAG,KAAK;;;;;;AAEzE,cAAc,WAAW,GAAG;AAE5B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACjC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,8IAAA,CAAA,kBAAe;IACxD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,gBAAgB,KAAK,CAAC,MAAM;IAErE,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yKACA,YAAY,yBACZ;QAED,GAAG,KAAK;;YAER;YACA,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,GAAG,OAAO,EAAE,oBACf,8OAAC;QAAI,KAAK;QAAK,MAAK;QAAa,GAAG,KAAK;kBACvC,cAAA,8OAAC,oMAAA,CAAA,QAAK;;;;;;;;;;AAGV,kBAAkB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2833, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgDsB,uBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2846, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAUsB,uBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2859, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA+BsB,YAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2872, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/app/signup/RegisterFrom.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  UsersRound,\r\n  Lock,\r\n  CalendarIcon,\r\n  UserRound,\r\n  Venus,\r\n  Mars,\r\n  VenusAndMars,\r\n} from \"lucide-react\";\r\nimport DateOfBirthPicker from \"@/components/profile/DateOfBirthPicker\";\r\nimport {\r\n  InputOTP,\r\n  InputOTPGroup,\r\n  InputOTPSlot,\r\n} from \"@/components/ui/input-otp\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\n\r\nimport {\r\n  completeRegistration,\r\n  initiateRegistration,\r\n  verifyOtp,\r\n} from \"@/actions/auth.action\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { toast } from \"sonner\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport {\r\n  getDeviceInfo,\r\n  isEmail,\r\n  isPhoneNumber,\r\n  isVietnameseName,\r\n} from \"@/utils/helpers\";\r\n\r\nexport default function RegisterForm() {\r\n  // Đặt ngày mặc định là ngày hiện tại\r\n  const [date, setDate] = useState<Date>(new Date());\r\n  const [day, setDay] = useState<string>(String(new Date().getDate()));\r\n  const [month, setMonth] = useState<string>(String(new Date().getMonth() + 1));\r\n  const [year, setYear] = useState<string>(String(new Date().getFullYear()));\r\n  const [step, setStep] = useState(1);\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [otp, setOtp] = useState(\"\");\r\n  const [fullName, setFullName] = useState(\"\");\r\n  const [gender, setGender] = useState(\"MALE\");\r\n  const [registrationId, setRegistrationId] = useState(\"\"); // Lưu registrationId từ bước 1\r\n  // Sử dụng state error để lưu trữ thông báo lỗi (không hiển thị trên UI)\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const router = useRouter();\r\n  const { login } = useAuthStore();\r\n\r\n  // Đồng bộ giữa date và day, month, year\r\n  useEffect(() => {\r\n    const newDate = new Date(\r\n      parseInt(year),\r\n      parseInt(month) - 1,\r\n      parseInt(day),\r\n    );\r\n    // Kiểm tra xem ngày có hợp lệ không\r\n    if (!isNaN(newDate.getTime())) {\r\n      setDate(newDate);\r\n    }\r\n  }, [day, month, year]);\r\n\r\n  // Bước 1: Gửi yêu cầu OTP\r\n  const handleRequestOTP = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    if (!inputValue) {\r\n      toast.warning(\"Vui lòng nhập số điện thoại hoặc email.\");\r\n      setError(\"Vui lòng nhập số điện thoại hoặc email.\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    const inputIsEmail = isEmail(inputValue);\r\n    const inputIsPhone = isPhoneNumber(inputValue);\r\n\r\n    if (!inputIsEmail && !inputIsPhone) {\r\n      toast.warning(\"Vui lòng nhập email hoặc số điện thoại hợp lệ.\");\r\n      setError(\"Vui lòng nhập email hoặc số điện thoại hợp lệ.\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    // Gửi OTP qua email hoặc số điện thoại\r\n    const result = await initiateRegistration(inputValue);\r\n    setLoading(false);\r\n\r\n    if (result.success && result.registrationId) {\r\n      setRegistrationId(result.registrationId);\r\n      setStep(2);\r\n      toast.success(\r\n        `Đã gửi mã OTP đến ${inputIsEmail ? \"email\" : \"số điện thoại\"} của bạn!`,\r\n      );\r\n    } else {\r\n      toast.error(\"Không thể gửi OTP. Vui lòng thử lại.\");\r\n      setError(result.error || \"Không thể gửi OTP. Vui lòng thử lại.\");\r\n    }\r\n  };\r\n\r\n  // Bước 2: Xác thực OTP\r\n  const handleVerifyOTP = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    const result = await verifyOtp(registrationId, otp);\r\n    setLoading(false);\r\n\r\n    if (result.success) {\r\n      toast.success(\"Xác thực OTP thành công!\");\r\n      setStep(3);\r\n    } else {\r\n      toast.error(\"Xác thực OTP thất bại!\");\r\n      setError(result.error || \"Mã OTP không đúng. Vui lòng thử lại.\");\r\n    }\r\n  };\r\n\r\n  // Bước 3: Hoàn tất đăng ký\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    // Kiểm tra họ tên\r\n    if (!isVietnameseName(fullName)) {\r\n      toast.warning(\"Vui lòng nhập đúng định dạng họ tên tiếng Việt.\");\r\n      setError(\"Vui lòng nhập đúng định dạng họ tên tiếng Việt.\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    // Kiểm tra ngày sinh\r\n    if (!day || !month || !year) {\r\n      toast.warning(\"Vui lòng chọn ngày sinh đầy đủ.\");\r\n      setError(\"Vui lòng chọn ngày sinh đầy đủ.\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    // Kiểm tra ngày sinh không được là ngày tương lai\r\n    const today = new Date();\r\n    if (date > today) {\r\n      toast.warning(\"Ngày sinh không thể là ngày trong tương lai.\");\r\n      setError(\"Ngày sinh không thể là ngày trong tương lai.\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    // Kiểm tra mật khẩu\r\n    if (password.length < 6) {\r\n      toast.warning(\"Mật khẩu phải có ít nhất 6 ký tự.\");\r\n      setError(\"Mật khẩu phải có ít nhất 6 ký tự.\");\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    // Tạo đối tượng Date từ day, month, year\r\n    const birthDate = new Date(\r\n      parseInt(year),\r\n      parseInt(month) - 1,\r\n      parseInt(day),\r\n    );\r\n\r\n    const result = await completeRegistration(\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      birthDate.toISOString(),\r\n      gender,\r\n    );\r\n    setLoading(false);\r\n    const { deviceType, deviceName } = getDeviceInfo();\r\n    if (result.success) {\r\n      const loginResult = await login(\r\n        inputValue,\r\n        password,\r\n        deviceName,\r\n        deviceType,\r\n      );\r\n      if (loginResult) {\r\n        toast.success(\"Đăng ký thành công!\");\r\n        router.push(\"/dashboard\");\r\n      } else {\r\n        toast.error(\"Đăng nhập thất bại!\");\r\n        setError(result.error || \"Đăng ký thất bại. Vui lòng thử lại.\");\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"mb-4 overflow-auto no-scrollbar\">\r\n      {step === 1 && (\r\n        <div className=\"items-center w-full max-w-[373px] mx-auto justify-center overflow-auto no-scrollbar\">\r\n          <div className=\"flex items-center justify-center gap-2 border-b border-gray-200 mb-6\">\r\n            <UsersRound className=\"w-5 h-5\" />\r\n            <Input\r\n              type=\"text\"\r\n              value={inputValue}\r\n              onChange={(e) => setInputValue(e.target.value)}\r\n              placeholder=\"Số điện thoại hoặc Email\"\r\n              className=\"border-none shadow-none focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n              required\r\n            />\r\n          </div>\r\n          <Button\r\n            className=\"w-full h-[50px] bg-[#80c7f9] hover:bg-[#0068ff] text-white font-semibold rounded-md mb-3\"\r\n            onClick={handleRequestOTP}\r\n            disabled={loading || !inputValue}\r\n          >\r\n            {loading ? \"Đang gửi...\" : \"Nhận OTP\"}\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {step === 2 && (\r\n        <div className=\"items-center flex flex-col gap-4 w-full max-w-[373px] mx-auto overflow-auto no-scrollbar\">\r\n          <p className=\"text-center\">Nhập mã OTP</p>\r\n          <InputOTP\r\n            maxLength={6}\r\n            value={otp}\r\n            onChange={setOtp}\r\n            className=\"mb-4 flex gap-x-2 sm:gap-x-4 justify-center\"\r\n          >\r\n            <InputOTPGroup>\r\n              {[...Array(6)].map((_, i) => (\r\n                <InputOTPSlot\r\n                  className=\"w-8 h-10 sm:w-12 sm:h-12 text-lg sm:text-xl text-center border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  key={i}\r\n                  index={i}\r\n                />\r\n              ))}\r\n            </InputOTPGroup>\r\n          </InputOTP>\r\n          {error && process.env.NODE_ENV === \"development\" && (\r\n            <p className=\"hidden\">{error}</p>\r\n          )}\r\n          <Button\r\n            className=\"w-full h-[50px] bg-[#80c7f9] hover:bg-[#0068ff] text-white font-semibold rounded-md mb-3\"\r\n            onClick={handleVerifyOTP}\r\n          >\r\n            Xác nhận\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {step === 3 && (\r\n        <form\r\n          onSubmit={handleSubmit}\r\n          className=\"flex flex-col w-full max-w-[373px] mx-auto overflow-auto no-scrollbar\"\r\n        >\r\n          <div className=\"flex items-center justify-center mb-3 text-muted-foreground\">\r\n            <p className=\"text-center\">Nhập thông tin cá nhân</p>\r\n          </div>\r\n          <div className=\"flex items-center gap-2 border-b border-gray-200 pl-4 mb-6\">\r\n            <UserRound className=\"w-5 h-5 flex-shrink-0\" />\r\n            <Input\r\n              type=\"text\"\r\n              value={fullName}\r\n              onChange={(e) => setFullName(e.target.value)}\r\n              placeholder=\"Họ và tên\"\r\n              className=\"border-none shadow-none focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n              required\r\n            />\r\n          </div>\r\n          <div className=\"flex items-center gap-2 border-b border-gray-200 pl-4 mb-6\">\r\n            <CalendarIcon className=\"w-5 h-5 flex-shrink-0\" />\r\n            <div className=\"w-full\">\r\n              <DateOfBirthPicker\r\n                day={day}\r\n                month={month}\r\n                year={year}\r\n                onDayChange={setDay}\r\n                onMonthChange={setMonth}\r\n                onYearChange={setYear}\r\n                className=\"border-none shadow-none focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-2 border-b border-gray-200 pl-4 mb-6\">\r\n            {gender === \"MALE\" && <Mars className=\"w-5 h-5 flex-shrink-0\" />}\r\n            {gender === \"FEMALE\" && <Venus className=\"w-5 h-5 flex-shrink-0\" />}\r\n            {gender === \"OTHER\" && (\r\n              <VenusAndMars className=\"w-5 h-5 flex-shrink-0\" />\r\n            )}\r\n            <Select value={gender} onValueChange={setGender}>\r\n              <SelectTrigger className=\"border-none shadow-none focus:outline-none focus:ring-0 focus-visible:ring-0 h-[40px] sm:h-[50px] pl-0\">\r\n                <SelectValue placeholder=\"Chọn giới tính\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"MALE\">Nam</SelectItem>\r\n                <SelectItem value=\"FEMALE\">Nữ</SelectItem>\r\n                <SelectItem value=\"OTHER\">Khác</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n          <div className=\"flex items-center gap-2 border-b border-gray-200 pl-4 mb-6\">\r\n            <Lock className=\"w-5 h-5 flex-shrink-0\" />\r\n            <Input\r\n              className=\"border-none shadow-none focus:outline-none focus:ring-0 focus-visible:ring-0\"\r\n              type=\"password\"\r\n              value={password}\r\n              onChange={(e) => setPassword(e.target.value)}\r\n              placeholder=\"Mật khẩu\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          {error && process.env.NODE_ENV === \"development\" && (\r\n            <p className=\"hidden\">{error}</p>\r\n          )}\r\n          <Button\r\n            className=\"w-full h-[50px] bg-[#80c7f9] hover:bg-[#0068ff] text-white font-semibold rounded-md mb-3\"\r\n            type=\"submit\"\r\n            disabled={\r\n              loading || !fullName || !password || !day || !month || !year\r\n            }\r\n          >\r\n            {loading ? \"Đang hoàn tất...\" : \"Hoàn tất đăng ký\"}\r\n          </Button>\r\n        </form>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAKA;AAQA;AAAA;AAAA;AAKA;AACA;AACA;AACA;;;;;;;;;;;;;;AAOe,SAAS;IACtB,qCAAqC;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAC3C,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,OAAO,IAAI,OAAO,OAAO;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,OAAO,IAAI,OAAO,QAAQ,KAAK;IAC1E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,OAAO,IAAI,OAAO,WAAW;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,+BAA+B;IACzF,wEAAwE;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAE7B,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,IAAI,KAClB,SAAS,OACT,SAAS,SAAS,GAClB,SAAS;QAEX,oCAAoC;QACpC,IAAI,CAAC,MAAM,QAAQ,OAAO,KAAK;YAC7B,QAAQ;QACV;IACF,GAAG;QAAC;QAAK;QAAO;KAAK;IAErB,0BAA0B;IAC1B,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QACT,IAAI,CAAC,YAAY;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,SAAS;YACT,WAAW;YACX;QACF;QAEA,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE;QAEnC,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAClC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,SAAS;YACT,WAAW;YACX;QACF;QAEA,uCAAuC;QACvC,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,uBAAoB,AAAD,EAAE;QAC1C,WAAW;QAEX,IAAI,OAAO,OAAO,IAAI,OAAO,cAAc,EAAE;YAC3C,kBAAkB,OAAO,cAAc;YACvC,QAAQ;YACR,wIAAA,CAAA,QAAK,CAAC,OAAO,CACX,CAAC,kBAAkB,EAAE,eAAe,UAAU,gBAAgB,SAAS,CAAC;QAE5E,OAAO;YACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,SAAS,OAAO,KAAK,IAAI;QAC3B;IACF;IAEA,uBAAuB;IACvB,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;QAC/C,WAAW;QAEX,IAAI,OAAO,OAAO,EAAE;YAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,QAAQ;QACV,OAAO;YACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,SAAS,OAAO,KAAK,IAAI;QAC3B;IACF;IAEA,2BAA2B;IAC3B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,kBAAkB;QAClB,IAAI,CAAC,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;YAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,SAAS;YACT,WAAW;YACX;QACF;QAEA,qBAAqB;QACrB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM;YAC3B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,SAAS;YACT,WAAW;YACX;QACF;QAEA,kDAAkD;QAClD,MAAM,QAAQ,IAAI;QAClB,IAAI,OAAO,OAAO;YAChB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,SAAS;YACT,WAAW;YACX;QACF;QAEA,oBAAoB;QACpB,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,SAAS;YACT,WAAW;YACX;QACF;QAEA,yCAAyC;QACzC,MAAM,YAAY,IAAI,KACpB,SAAS,OACT,SAAS,SAAS,GAClB,SAAS;QAGX,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,uBAAoB,AAAD,EACtC,gBACA,UACA,UACA,UAAU,WAAW,IACrB;QAEF,WAAW;QACX,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD;QAC/C,IAAI,OAAO,OAAO,EAAE;YAClB,MAAM,cAAc,MAAM,MACxB,YACA,UACA,YACA;YAEF,IAAI,aAAa;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,SAAS,mBACR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,aAAY;gCACZ,WAAU;gCACV,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC,kIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAS;wBACT,UAAU,WAAW,CAAC;kCAErB,UAAU,gBAAgB;;;;;;;;;;;;YAKhC,SAAS,mBACR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAc;;;;;;kCAC3B,8OAAC,wIAAA,CAAA,WAAQ;wBACP,WAAW;wBACX,OAAO;wBACP,UAAU;wBACV,WAAU;kCAEV,cAAA,8OAAC,wIAAA,CAAA,gBAAa;sCACX;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,wIAAA,CAAA,eAAY;oCACX,WAAU;oCAEV,OAAO;mCADF;;;;;;;;;;;;;;;oBAMZ,SAAS,oDAAyB,+BACjC,8OAAC;wBAAE,WAAU;kCAAU;;;;;;kCAEzB,8OAAC,kIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAS;kCACV;;;;;;;;;;;;YAMJ,SAAS,mBACR,8OAAC;gBACC,UAAU;gBACV,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAc;;;;;;;;;;;kCAE7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,aAAY;gCACZ,WAAU;gCACV,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kJAAA,CAAA,UAAiB;oCAChB,KAAK;oCACL,OAAO;oCACP,MAAM;oCACN,aAAa;oCACb,eAAe;oCACf,cAAc;oCACd,WAAU;;;;;;;;;;;;;;;;;kCAKhB,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,wBAAU,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACrC,WAAW,0BAAY,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BACxC,WAAW,yBACV,8OAAC,0NAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CAE1B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAQ,eAAe;;kDACpC,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAO;;;;;;0DACzB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;0DAC3B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;;;;;;;kCAIhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,WAAU;gCACV,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,aAAY;gCACZ,QAAQ;;;;;;;;;;;;oBAIX,SAAS,oDAAyB,+BACjC,8OAAC;wBAAE,WAAU;kCAAU;;;;;;kCAEzB,8OAAC,kIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,MAAK;wBACL,UACE,WAAW,CAAC,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC;kCAGzD,UAAU,qBAAqB;;;;;;;;;;;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 3377, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/app/login/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport Image from \"next/image\";\r\nimport QrLogin from \"@/components/QrLogin/QrLogin\";\r\nimport { useSearchParams } from \"next/navigation\";\r\n\r\nimport LoginForm from \"@/components/LoginForm\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Check, AlignJustify } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport RegisterForm from \"../signup/RegisterFrom\";\r\n\r\nexport default function LoginPage() {\r\n  const [showLoginForm, setShowLoginForm] = useState(false);\r\n  const [showRegisterForm, setShowRegisterForm] = useState(false);\r\n  const [open, setOpen] = React.useState(false);\r\n  const searchParams = useSearchParams();\r\n\r\n  // <PERSON><PERSON><PERSON> tra xem có tham số showRegister trong URL không\r\n  useEffect(() => {\r\n    const showRegister = searchParams.get(\"showRegister\");\r\n    if (showRegister === \"true\") {\r\n      setShowLoginForm(true);\r\n      setShowRegisterForm(true);\r\n    }\r\n  }, [searchParams]);\r\n\r\n  const handleSelect = (currentValue: string) => {\r\n    if (currentValue === \"password-login\") {\r\n      setShowLoginForm(true);\r\n      setShowRegisterForm(false);\r\n    } else if (currentValue === \"register\") {\r\n      setShowLoginForm(true);\r\n      setShowRegisterForm(true);\r\n    } else {\r\n      setShowLoginForm(false);\r\n      setShowRegisterForm(false);\r\n    }\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex justify-center items-start min-h-screen p-4 sm:p-6 md:p-10 bg-[#e8f3ff]\">\r\n      <div className=\"flex flex-col items-center justify-center gap-5 w-full max-w-[541px]\">\r\n        <div className=\"flex flex-col items-center justify-center\">\r\n          <Image\r\n            src=\"/logo.png\"\r\n            width={300}\r\n            height={100}\r\n            alt=\"Vodka Logo\"\r\n            className=\"w-[200px] sm:w-[250px] md:w-[300px] h-auto\"\r\n            priority\r\n            loading=\"eager\"\r\n          />\r\n          <h2 className=\"text-center text-sm sm:text-base md:text-lg text-gray-600 whitespace-normal mt-2 max-w-[300px] mx-auto\">\r\n            Đăng nhập tài khoản Vodka để kết nối với ứng dụng Vodka Web\r\n          </h2>\r\n        </div>\r\n        <div className=\"bg-white shadow-lg rounded-[30px] flex flex-col items-center gap-4 relative w-full h-auto min-h-[400px] sm:min-h-[523px] overflow-auto no-scrollbar\">\r\n          <div className=\"flex flex-row border-b border-gray-200 w-full h-[60px] justify-center items-center font-semibold text-sm sm:text-base\">\r\n            <p>\r\n              {showLoginForm\r\n                ? showRegisterForm\r\n                  ? \"Đăng ký tài khoản\"\r\n                  : \"Đăng nhập bằng mật khẩu\"\r\n                : \"Đăng nhập bằng quét mã QR\"}\r\n            </p>\r\n            {!showLoginForm ? (\r\n              <DropdownMenu open={open} onOpenChange={setOpen}>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    className=\"absolute top-2 right-2 border mr-2 w-[35px] h-[35px] sm:w-[45px] sm:h-[35px]\"\r\n                  >\r\n                    <AlignJustify className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent className=\"w-48 sm:w-56\">\r\n                  <DropdownMenuCheckboxItem\r\n                    className=\"flex justify-center text-sm sm:text-base\"\r\n                    onSelect={() => handleSelect(\"password-login\")}\r\n                  >\r\n                    Đăng nhập bằng mật khẩu\r\n                    <Check\r\n                      className={cn(\r\n                        \"ml-auto w-4 h-4\",\r\n                        showLoginForm ? \"opacity-100\" : \"opacity-0\",\r\n                      )}\r\n                    />\r\n                  </DropdownMenuCheckboxItem>\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            ) : null}\r\n          </div>\r\n          <div className=\"w-full px-4 sm:px-6\">\r\n            {showLoginForm ? (\r\n              showRegisterForm ? (\r\n                <RegisterForm />\r\n              ) : (\r\n                <LoginForm />\r\n              )\r\n            ) : (\r\n              <QrLogin />\r\n            )}\r\n          </div>\r\n          {showLoginForm && !showRegisterForm ? (\r\n            <div className=\"flex flex-col gap-4 items-center pb-4 text-sm sm:text-base\">\r\n              <a\r\n                className=\"text-[#39a8f5] font-semibold cursor-pointer hover:underline\"\r\n                onClick={() => handleSelect(\"qr-login\")}\r\n              >\r\n                Đăng nhập bằng mã QR\r\n              </a>\r\n              <a\r\n                className=\"text-[#39a8f5] font-semibold cursor-pointer hover:underline\"\r\n                onClick={() => handleSelect(\"register\")}\r\n              >\r\n                Đăng ký tài khoản\r\n              </a>\r\n            </div>\r\n          ) : showRegisterForm ? (\r\n            <div className=\"pb-4 text-sm sm:text-base\">\r\n              <a\r\n                className=\"text-[#39a8f5] font-semibold cursor-pointer hover:underline\"\r\n                onClick={() => handleSelect(\"qr-login\")}\r\n              >\r\n                Quay lại đăng nhập\r\n              </a>\r\n            </div>\r\n          ) : null}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAMA;AAjBA;;;;;;;;;;;;AAmBe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACvC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,GAAG,CAAC;QACtC,IAAI,iBAAiB,QAAQ;YAC3B,iBAAiB;YACjB,oBAAoB;QACtB;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,eAAe,CAAC;QACpB,IAAI,iBAAiB,kBAAkB;YACrC,iBAAiB;YACjB,oBAAoB;QACtB,OAAO,IAAI,iBAAiB,YAAY;YACtC,iBAAiB;YACjB,oBAAoB;QACtB,OAAO;YACL,iBAAiB;YACjB,oBAAoB;QACtB;QACA,QAAQ;IACV;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,KAAI;4BACJ,WAAU;4BACV,QAAQ;4BACR,SAAQ;;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAyG;;;;;;;;;;;;8BAIzH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CACE,gBACG,mBACE,sBACA,4BACF;;;;;;gCAEL,CAAC,8BACA,8OAAC,4IAAA,CAAA,eAAY;oCAAC,MAAM;oCAAM,cAAc;;sDACtC,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,OAAO;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG5B,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,WAAU;sDAC7B,cAAA,8OAAC,4IAAA,CAAA,2BAAwB;gDACvB,WAAU;gDACV,UAAU,IAAM,aAAa;;oDAC9B;kEAEC,8OAAC,oMAAA,CAAA,QAAK;wDACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mBACA,gBAAgB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;2CAMxC;;;;;;;sCAEN,8OAAC;4BAAI,WAAU;sCACZ,gBACC,iCACE,8OAAC,qIAAA,CAAA,UAAY;;;;qDAEb,8OAAC,+HAAA,CAAA,UAAS;;;;qDAGZ,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;wBAGX,iBAAiB,CAAC,iCACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,aAAa;8CAC7B;;;;;;8CAGD,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,aAAa;8CAC7B;;;;;;;;;;;mCAID,iCACF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,aAAa;0CAC7B;;;;;;;;;;mCAID;;;;;;;;;;;;;;;;;;AAKd", "debugId": null}}]}