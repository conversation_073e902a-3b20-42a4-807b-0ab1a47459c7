{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/providers/AuthProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/providers/AuthProvider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/AuthProvider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/providers/AuthProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/providers/AuthProvider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/AuthProvider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/providers/SocketChatProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SocketChatProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SocketChatProvider() from the server but SocketChatProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/SocketChatProvider.tsx <module evaluation>\",\n    \"SocketChatProvider\",\n);\nexport const useSocket = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSocket() from the server but useSocket is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/SocketChatProvider.tsx <module evaluation>\",\n    \"useSocket\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,sEACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,sEACA", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/providers/SocketChatProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SocketChatProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SocketChatProvider() from the server but SocketChatProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/SocketChatProvider.tsx\",\n    \"SocketChatProvider\",\n);\nexport const useSocket = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSocket() from the server but useSocket is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/SocketChatProvider.tsx\",\n    \"useSocket\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,kDACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,kDACA", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/common/DocumentTitle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/common/DocumentTitle.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/DocumentTitle.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/common/DocumentTitle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/common/DocumentTitle.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/DocumentTitle.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_a34fcf.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_a34fcf-module__eoPVeG__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_a34fcf.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,mJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,mJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,mJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/app/layout.tsx"], "sourcesContent": ["import { Toaster } from \"sonner\";\r\nimport \"./globals.css\";\r\nimport AuthProvider from \"@/providers/AuthProvider\";\r\nimport { SocketChatProvider } from \"@/providers/SocketChatProvider\";\r\nimport DocumentTitle from \"@/components/common/DocumentTitle\";\r\nimport { Inter } from \"next/font/google\"; // Import Inter font\r\n\r\nconst inter = Inter({\r\n  subsets: [\"latin\"],\r\n  weight: [\"400\", \"500\", \"600\", \"700\"],\r\n});\r\n\r\nexport const metadata = {\r\n  title: \"Vodka\",\r\n  description:\r\n    \"Vodka is a community for sharing and connecting. Chat, share, and connect with others.\",\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <head>\r\n        <meta charSet=\"UTF-8\" />\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n      </head>\r\n      <body className={`${inter.className} force-pointer-events`}>\r\n        <DocumentTitle title=\"Vodka\" />\r\n        <Toaster position=\"top-center\" richColors />\r\n        <AuthProvider>\r\n          <SocketChatProvider>\r\n            <main className=\"min-h-screen\">{children}</main>\r\n          </SocketChatProvider>\r\n        </AuthProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;AACA;;;;;;;;;AAQO,MAAM,WAAW;IACtB,OAAO;IACP,aACE;AACJ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;wBAAK,SAAQ;;;;;;kCACd,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAEhC,8OAAC;gBAAK,WAAW,GAAG,uIAAA,CAAA,UAAK,CAAC,SAAS,CAAC,qBAAqB,CAAC;;kCACxD,8OAAC,6IAAA,CAAA,UAAa;wBAAC,OAAM;;;;;;kCACrB,8OAAC,wIAAA,CAAA,UAAO;wBAAC,UAAS;wBAAa,UAAU;;;;;;kCACzC,8OAAC,iIAAA,CAAA,UAAY;kCACX,cAAA,8OAAC,uIAAA,CAAA,qBAAkB;sCACjB,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/sonner/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"toast\",\n);\nexport const useSonner = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"useSonner\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,oEACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,oEACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/node_modules/sonner/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"toast\",\n);\nexport const useSonner = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"useSonner\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,gDACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,gDACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}