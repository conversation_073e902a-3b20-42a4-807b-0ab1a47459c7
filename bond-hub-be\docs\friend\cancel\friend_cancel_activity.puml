@startuml Hủy lời mời kết bạn - Activity Diagram
title Hủy lời mời kết bạn - Activity Diagram

|User|
start
:<PERSON>em danh sách lời mời đã gửi;
:Chọn lời mời muốn hủy;
:<PERSON><PERSON><PERSON> yêu cầu hủy lời mời;

|System|
:Lấy ID người dùng từ token xác thực;
:Kiểm tra định dạng ID người dùng và ID lời mời;

if (ID hợp lệ?) then (Có)
  :Tìm kiếm lời mời kết bạn;
  
  if (Lời mời tồn tại?) then (Có)
    :Kiểm tra người dùng có phải người gửi lời mời không;
    
    if (<PERSON><PERSON> người gửi?) then (Có)
      :X<PERSON><PERSON> lời mời kết bạn khỏi cơ sở dữ liệu;
      :<PERSON><PERSON><PERSON> thông báo qua WebSocket;
      :Tr<PERSON> về thông báo hủy thành công;
    else (<PERSON>hông)
      :<PERSON><PERSON><PERSON> về lỗi "Không có quyền hủy lời mời này";
    endif
  else (Không)
    :Trả về lỗi "Lời mời kết bạn không tồn tại";
  endif
else (Không)
  :Trả về lỗi "ID không hợp lệ";
endif

|User|
if (Hủy lời mời thành công?) then (Có)
  :Xem thông báo hủy lời mời thành công;
else (Không)
  :Hiển thị thông báo lỗi;
endif

stop
@enduml
