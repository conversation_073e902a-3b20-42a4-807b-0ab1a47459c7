"use client";

import { useEffect, useState } from "react";
import { useChatStore } from "@/stores/chatStore";
import { useConversationsStore } from "@/stores/conversationsStore";
import { useAuthStore } from "@/stores/authStore";

export default function RealTimeMessageDebug() {
  const { messages, selectedContact, selectedGroup, currentChatType, _lastMessageTime } = useChatStore();
  const { conversations } = useConversationsStore();
  const { user: currentUser } = useAuthStore();
  const [debugInfo, setDebugInfo] = useState<any[]>([]);

  // Track messages changes
  useEffect(() => {
    const timestamp = new Date().toISOString();
    const conversationId = currentChatType === "USER" ? selectedContact?.id : selectedGroup?.id;

    const newDebugInfo = {
      timestamp,
      event: "MESSAGES_CHANGED",
      conversationId,
      currentChatType,
      messagesCount: messages.length,
      lastMessageId: messages.length > 0 ? messages[messages.length - 1].id : null,
      lastMessageContent: messages.length > 0 ? messages[messages.length - 1].content?.text : null,
      lastMessageSender: messages.length > 0 ? messages[messages.length - 1].senderId : null,
    };

    setDebugInfo(prev => [newDebugInfo, ...prev.slice(0, 9)]); // Keep last 10 entries
  }, [messages, currentChatType, selectedContact?.id, selectedGroup?.id]);

  // Track socket events
  useEffect(() => {
    if (typeof window !== "undefined" && window.messageSocket) {
      const socket = window.messageSocket;

      const handleNewMessage = (data: any) => {
        const timestamp = new Date().toISOString();
        const newDebugInfo = {
          timestamp,
          event: "SOCKET_NEW_MESSAGE",
          messageId: data.message?.id,
          messageContent: data.message?.content?.text,
          messageSender: data.message?.senderId,
          messageGroupId: data.message?.groupId,
          messageReceiverId: data.message?.receiverId,
        };
        setDebugInfo(prev => [newDebugInfo, ...prev.slice(0, 9)]);
      };

      socket.on("newMessage", handleNewMessage);

      return () => {
        socket.off("newMessage", handleNewMessage);
      };
    }
  }, []);

  if (!currentUser) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-96 bg-white border border-gray-300 rounded-lg shadow-lg p-4 overflow-y-auto z-50">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-sm">Real-time Debug</h3>
        <button
          onClick={() => setDebugInfo([])}
          className="text-xs bg-red-500 text-white px-2 py-1 rounded"
        >
          Clear
        </button>
      </div>

      <div className="text-xs space-y-2">
        <div className="bg-gray-100 p-2 rounded">
          <div><strong>Current Chat:</strong> {currentChatType}</div>
          <div><strong>Contact/Group ID:</strong> {selectedContact?.id || selectedGroup?.id}</div>
          <div><strong>Messages Count:</strong> {messages.length}</div>
          <div><strong>Last Message Time:</strong> {_lastMessageTime ? new Date(_lastMessageTime).toLocaleTimeString() : "None"}</div>
          <div><strong>Socket Connected:</strong> {typeof window !== "undefined" && window.messageSocket?.connected ? "Yes" : "No"}</div>
        </div>

        <div className="space-y-1">
          <div className="font-semibold">Recent Events:</div>
          {debugInfo.map((info, index) => (
            <div key={index} className="bg-gray-50 p-2 rounded text-xs">
              <div className="font-semibold text-blue-600">{info.event}</div>
              <div className="text-gray-500">{info.timestamp.split('T')[1].split('.')[0]}</div>
              {info.event === "MESSAGES_CHANGED" && (
                <div>
                  <div>Count: {info.messagesCount}</div>
                  <div>Last ID: {info.lastMessageId}</div>
                  <div>Content: {info.lastMessageContent?.substring(0, 30)}...</div>
                </div>
              )}
              {info.event === "SOCKET_NEW_MESSAGE" && (
                <div>
                  <div>ID: {info.messageId}</div>
                  <div>From: {info.messageSender}</div>
                  <div>Content: {info.messageContent?.substring(0, 30)}...</div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
