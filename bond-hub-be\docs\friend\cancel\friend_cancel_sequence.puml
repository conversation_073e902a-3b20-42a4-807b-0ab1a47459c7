@startuml Hủy lời mời kết bạn - Sequence Diagram
title Hủy lời mời kết bạn - Sequence Diagram

skinparam sequenceParticipant {
    BorderColor grey
    BackgroundColor white
    FontColor black
}

skinparam sequenceLifeLine {
    BorderColor grey
    BackgroundColor white
}

skinparam sequenceArrow {
    Color black
}

actor User
boundary "Frontend" as Frontend
control "FriendController" as Controller
entity "FriendService" as Service
entity "PrismaService" as Prisma
entity "FriendGateway" as Gateway
database "Database" as DB
queue "WebSocket" as WS

User -> Frontend: Chọn hủy lời mời kết bạn
Frontend -> Controller: DELETE /friends/request/{requestId}

Controller -> Service: cancelFriendRequest(userId, requestId)

Service -> Service: isValidUUID(userId, requestId)
alt ID không hợp lệ
    Service --> Controller: throw BadRequestException
    Controller --> Frontend: 400 Bad Request
    Frontend --> User: <PERSON><PERSON><PERSON> thị lỗi "ID không hợp lệ"
else ID hợp lệ
    Service -> Prisma: friend.findUnique()
    Prisma -> DB: SELECT * FROM friends\nWHERE id = ?
    DB --> Prisma: Friend request data
    Prisma --> Service: Friend request object
    
    alt Lời mời không tồn tại
        Service --> Controller: throw NotFoundException
        Controller --> Frontend: 404 Not Found
        Frontend --> User: Hiển thị lỗi "Lời mời kết bạn không tồn tại"
    else Lời mời tồn tại
        alt Không phải người gửi lời mời
            Service --> Controller: throw ForbiddenException
            Controller --> Frontend: 403 Forbidden
            Frontend --> User: Hiển thị lỗi "Không có quyền hủy lời mời này"
        else Là người gửi lời mời
            Service -> Prisma: friend.delete()
            Prisma -> DB: DELETE FROM friends\nWHERE id = ?
            DB --> Prisma: Deleted result
            Prisma --> Service: Deleted result
            
            Service -> Gateway: emitReloadEvent(senderId, receiverId)
            Gateway -> WS: emit('reload')
            
            Service --> Controller: {message: "Đã hủy lời mời kết bạn"}
            Controller --> Frontend: 200 OK {message}
            Frontend --> User: Hiển thị thông báo hủy lời mời thành công
        end
    end
end

@enduml
