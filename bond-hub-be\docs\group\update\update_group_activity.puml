@startuml Cập nhật thông tin nhóm - Activity Diagram
title Cập nhật thông tin nhóm - Activity Diagram

|User|
start
:<PERSON><PERSON><PERSON> nhóm;
:<PERSON><PERSON><PERSON> chức năng cập nhật thông tin nhóm;
:<PERSON><PERSON><PERSON><PERSON> thông tin mới (tên nhóm);
:<PERSON><PERSON><PERSON> yêu cầu cập nhật thông tin nhóm;

|System|
:Ki<PERSON><PERSON> tra quyền của người dùng;

if (<PERSON><PERSON> quyền cập nhật?) then (Có)
  :Cậ<PERSON> nhật thông tin nhóm trong cơ sở dữ liệu;
  :<PERSON><PERSON><PERSON> thông báo cho tất cả thành viên qua WebSocket;
  :<PERSON><PERSON><PERSON> về thông tin nhóm đã cập nhật;
else (Không)
  :<PERSON><PERSON><PERSON> về lỗi "Không có quyền cập nhật thông tin nhóm";
endif

|User|
if (<PERSON>ậ<PERSON> nhật thành công?) then (Có)
  :Xem thông tin nhóm đã cập nhật;
else (Không)
  :<PERSON><PERSON><PERSON> thị thông báo lỗi;
endif

stop
@enduml
