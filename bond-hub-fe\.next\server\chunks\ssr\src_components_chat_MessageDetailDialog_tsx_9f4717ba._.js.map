{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/MessageDetailDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport type React from \"react\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport type { Message, Media, UserInfo } from \"@/types/base\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { formatMessageTime } from \"@/utils/dateUtils\";\r\nimport { getUserInitials } from \"@/utils/userUtils\";\r\nimport Image from \"next/image\";\r\nimport AudioVisualizer from \"./AudioVisualizer\";\r\nimport {\r\n  Download,\r\n  X,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  ZoomIn,\r\n  ZoomOut,\r\n  PanelRightClose,\r\n  PanelRightOpen,\r\n  RefreshCcw,\r\n  Music,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { createPortal } from \"react-dom\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { Popover, PopoverContent } from \"@/components/ui/popover\";\r\nimport { CheckCircle2 } from \"lucide-react\";\r\n\r\ninterface MessageDetailDialogProps {\r\n  message: Message | null;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  userInfo?: UserInfo; // Add userInfo for the sender\r\n}\r\n\r\nexport default function MessageDetailDialog({\r\n  message,\r\n  isOpen,\r\n  onClose,\r\n  userInfo,\r\n}: MessageDetailDialogProps) {\r\n  const [currentMediaIndex, setCurrentMediaIndex] = useState(0);\r\n  const [mounted, setMounted] = useState(false);\r\n  const [isForwardMenuOpen, setIsForwardMenuOpen] = useState(false);\r\n  const [selectedRecipients, setSelectedRecipients] = useState<\r\n    Array<{ type: \"USER\" | \"GROUP\"; id: string; name: string }>\r\n  >([]);\r\n  const [forwardSuccess, setForwardSuccess] = useState(false);\r\n  const [zoomLevel, setZoomLevel] = useState(1);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\r\n  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });\r\n  const [showSidebar, setShowSidebar] = useState(true);\r\n  const imageRef = useRef<HTMLImageElement>(null);\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const [fileContent, setFileContent] = useState<string | null>(null);\r\n\r\n  const { forwardMessageToRecipients } = useChatStore();\r\n  const { conversations } = useConversationsStore();\r\n  const currentUser = useAuthStore((state) => state.user);\r\n\r\n  // Helper functions for file handling\r\n  const isOfficeDocument = (extension?: string) => {\r\n    if (!extension) return false;\r\n    const officeExtensions = [\"doc\", \"docx\", \"xls\", \"xlsx\", \"ppt\", \"pptx\"];\r\n    return officeExtensions.includes(extension.toLowerCase());\r\n  };\r\n\r\n  const isTextFile = (extension?: string) => {\r\n    if (!extension) return false;\r\n    const textExtensions = [\r\n      \"txt\",\r\n      \"md\",\r\n      \"json\",\r\n      \"js\",\r\n      \"ts\",\r\n      \"html\",\r\n      \"css\",\r\n      \"csv\",\r\n    ];\r\n    return textExtensions.includes(extension.toLowerCase());\r\n  };\r\n\r\n  // Check if file is an audio file by extension\r\n  const isAudioFile = (extension?: string) => {\r\n    if (!extension) return false;\r\n    const audioExtensions = [\"mp3\", \"wav\", \"ogg\", \"m4a\", \"aac\", \"flac\"];\r\n    return audioExtensions.includes(extension.toLowerCase());\r\n  };\r\n\r\n  const getFileIcon = (extension?: string, size = 32) => {\r\n    const iconSize = size;\r\n    const strokeWidth = size <= 24 ? 2 : 1.5;\r\n\r\n    if (!extension) {\r\n      return (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          width={iconSize}\r\n          height={iconSize}\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth={strokeWidth}\r\n          strokeLinecap=\"round\"\r\n          strokeLinejoin=\"round\"\r\n          className=\"text-gray-500\"\r\n        >\r\n          <path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\" />\r\n          <polyline points=\"14 2 14 8 20 8\" />\r\n        </svg>\r\n      );\r\n    }\r\n\r\n    switch (extension.toLowerCase()) {\r\n      case \"pdf\":\r\n        return (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width={iconSize}\r\n            height={iconSize}\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth={strokeWidth}\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            className=\"text-red-500\"\r\n          >\r\n            <path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\" />\r\n            <polyline points=\"14 2 14 8 20 8\" />\r\n            <path d=\"M9 15v-1h6v1\" />\r\n            <path d=\"M9 18v-1h6v1\" />\r\n            <path d=\"M9 12v-1h6v1\" />\r\n          </svg>\r\n        );\r\n      case \"doc\":\r\n      case \"docx\":\r\n        return (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width={iconSize}\r\n            height={iconSize}\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth={strokeWidth}\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            className=\"text-blue-600\"\r\n          >\r\n            <path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\" />\r\n            <polyline points=\"14 2 14 8 20 8\" />\r\n            <path d=\"M9 15h6\" />\r\n            <path d=\"M9 18h6\" />\r\n            <path d=\"M9 12h6\" />\r\n          </svg>\r\n        );\r\n      case \"xls\":\r\n      case \"xlsx\":\r\n        return (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width={iconSize}\r\n            height={iconSize}\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth={strokeWidth}\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            className=\"text-green-600\"\r\n          >\r\n            <path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\" />\r\n            <polyline points=\"14 2 14 8 20 8\" />\r\n            <rect x=\"8\" y=\"12\" width=\"8\" height=\"6\" />\r\n            <path d=\"M8 12v-2h8v2\" />\r\n          </svg>\r\n        );\r\n      case \"ppt\":\r\n      case \"pptx\":\r\n        return (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width={iconSize}\r\n            height={iconSize}\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth={strokeWidth}\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            className=\"text-orange-600\"\r\n          >\r\n            <path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\" />\r\n            <polyline points=\"14 2 14 8 20 8\" />\r\n            <circle cx=\"12\" cy=\"14\" r=\"4\" />\r\n          </svg>\r\n        );\r\n      case \"txt\":\r\n      case \"md\":\r\n      case \"json\":\r\n      case \"js\":\r\n      case \"ts\":\r\n      case \"html\":\r\n      case \"css\":\r\n      case \"csv\":\r\n        return (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width={iconSize}\r\n            height={iconSize}\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth={strokeWidth}\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            className=\"text-gray-600\"\r\n          >\r\n            <path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\" />\r\n            <polyline points=\"14 2 14 8 20 8\" />\r\n            <line x1=\"8\" y1=\"13\" x2=\"16\" y2=\"13\" />\r\n            <line x1=\"8\" y1=\"17\" x2=\"16\" y2=\"17\" />\r\n          </svg>\r\n        );\r\n      default:\r\n        // Check if it's an audio file\r\n        if (isAudioFile(extension)) {\r\n          return (\r\n            <Music\r\n              size={iconSize}\r\n              strokeWidth={strokeWidth}\r\n              className=\"text-purple-600\"\r\n            />\r\n          );\r\n        }\r\n        // Default file icon\r\n        return (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width={iconSize}\r\n            height={iconSize}\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth={strokeWidth}\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            className=\"text-gray-500\"\r\n          >\r\n            <path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\" />\r\n            <polyline points=\"14 2 14 8 20 8\" />\r\n          </svg>\r\n        );\r\n    }\r\n  };\r\n\r\n  // Reset states when dialog opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      setSelectedRecipients([]);\r\n      setForwardSuccess(false);\r\n      setIsForwardMenuOpen(false);\r\n      setShowSidebar(true);\r\n    }\r\n  }, [isOpen]);\r\n\r\n  // Fetch text file content when needed\r\n  useEffect(() => {\r\n    let currentMedia: Media | null = null;\r\n    if (message?.content.media && message.content.media.length > 0) {\r\n      currentMedia = message.content.media[currentMediaIndex];\r\n    } else if (message?.content.image) {\r\n      currentMedia = {\r\n        url: message.content.image,\r\n        type: \"IMAGE\",\r\n        fileId: \"legacy-image\",\r\n        fileName: \"image.jpg\",\r\n        metadata: {\r\n          path: \"\",\r\n          size: 0,\r\n          mimeType: \"image/jpeg\",\r\n          extension: \"jpg\",\r\n          bucketName: \"\",\r\n          uploadedAt: new Date().toISOString(),\r\n          sizeFormatted: \"\",\r\n        },\r\n      };\r\n    } else if (message?.content.video) {\r\n      currentMedia = {\r\n        url: message.content.video,\r\n        type: \"VIDEO\",\r\n        fileId: \"legacy-video\",\r\n        fileName: \"video.mp4\",\r\n        metadata: {\r\n          path: \"\",\r\n          size: 0,\r\n          mimeType: \"video/mp4\",\r\n          extension: \"mp4\",\r\n          bucketName: \"\",\r\n          uploadedAt: new Date().toISOString(),\r\n          sizeFormatted: \"\",\r\n        },\r\n      };\r\n    }\r\n    if (currentMedia && isTextFile(currentMedia.metadata.extension)) {\r\n      setFileContent(null); // Reset content while loading\r\n\r\n      fetch(currentMedia.url)\r\n        .then((response) => response.text())\r\n        .then((text) => {\r\n          setFileContent(text);\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Error fetching text file:\", error);\r\n          setFileContent(\"Error loading file content.\");\r\n        });\r\n    }\r\n  }, [message, currentMediaIndex]);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n\r\n    // Khi dialog mở, ngăn cuộn trang\r\n    if (isOpen) {\r\n      document.body.style.overflow = \"hidden\";\r\n    }\r\n\r\n    // Reset zoom when dialog opens or closes\r\n    setZoomLevel(1);\r\n    setImagePosition({ x: 0, y: 0 });\r\n\r\n    return () => {\r\n      // Khi dialog đóng, cho phép cuộn trang trở lại\r\n      document.body.style.overflow = \"\";\r\n    };\r\n  }, [isOpen]);\r\n\r\n  const handleForwardMessage = async () => {\r\n    if (!message || selectedRecipients.length === 0) return;\r\n\r\n    const recipients = selectedRecipients.map((recipient) => ({\r\n      type: recipient.type,\r\n      id: recipient.id,\r\n    }));\r\n\r\n    const success = await forwardMessageToRecipients(message.id, recipients);\r\n\r\n    if (success) {\r\n      setForwardSuccess(true);\r\n      // Auto close the forward menu after success\r\n      setTimeout(() => {\r\n        setIsForwardMenuOpen(false);\r\n        setSelectedRecipients([]);\r\n        setForwardSuccess(false);\r\n      }, 2000);\r\n    }\r\n  };\r\n\r\n  const toggleRecipient = (\r\n    type: \"USER\" | \"GROUP\",\r\n    id: string,\r\n    name: string,\r\n  ) => {\r\n    setSelectedRecipients((prev) => {\r\n      const exists = prev.some((r) => r.id === id && r.type === type);\r\n\r\n      if (exists) {\r\n        return prev.filter((r) => !(r.id === id && r.type === type));\r\n      } else {\r\n        return [...prev, { type, id, name }];\r\n      }\r\n    });\r\n  };\r\n\r\n  if (!message || !mounted || !isOpen) return null;\r\n\r\n  const handleDownload = (media: Media) => {\r\n    // Use fetch to get the file as a blob\r\n    fetch(media.url)\r\n      .then((response) => response.blob())\r\n      .then((blob) => {\r\n        // Create a blob URL for the file\r\n        const blobUrl = URL.createObjectURL(blob);\r\n\r\n        // Create a temporary anchor element\r\n        const link = document.createElement(\"a\");\r\n        link.href = blobUrl;\r\n        link.download = media.fileName;\r\n\r\n        // Append to body, click, and clean up\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n\r\n        // Release the blob URL\r\n        setTimeout(() => URL.revokeObjectURL(blobUrl), 100);\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Download failed:\", error);\r\n      });\r\n  };\r\n\r\n  const handlePrevMedia = () => {\r\n    if (message?.content.media && message.content.media.length > 1) {\r\n      setCurrentMediaIndex((prev) =>\r\n        prev === 0 ? message.content.media!.length - 1 : prev - 1,\r\n      );\r\n      resetZoom();\r\n    }\r\n  };\r\n\r\n  const handleNextMedia = () => {\r\n    if (message?.content.media && message.content.media.length > 1) {\r\n      setCurrentMediaIndex((prev) =>\r\n        prev === message.content.media!.length - 1 ? 0 : prev + 1,\r\n      );\r\n      resetZoom();\r\n    }\r\n  };\r\n\r\n  const handleWheel = (e: React.WheelEvent) => {\r\n    if (!currentMedia || currentMedia.type !== \"IMAGE\") return;\r\n\r\n    e.preventDefault();\r\n\r\n    // Determine zoom direction\r\n    const zoomDirection = e.deltaY < 0 ? 1 : -1;\r\n\r\n    // Calculate new zoom level\r\n    const newZoomLevel = Math.max(\r\n      1,\r\n      Math.min(3, zoomLevel + zoomDirection * 0.1),\r\n    );\r\n\r\n    // Only update if zoom level changed\r\n    if (newZoomLevel !== zoomLevel) {\r\n      setZoomLevel(newZoomLevel);\r\n\r\n      // Reset position when zooming back to 1\r\n      if (newZoomLevel === 1) {\r\n        setImagePosition({ x: 0, y: 0 });\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleMouseDown = (e: React.MouseEvent) => {\r\n    if (zoomLevel > 1) {\r\n      e.preventDefault();\r\n      setIsDragging(true);\r\n      setDragStart({ x: e.clientX, y: e.clientY });\r\n\r\n      // Change cursor style\r\n      if (containerRef.current) {\r\n        containerRef.current.style.cursor = \"grabbing\";\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleMouseMove = (e: React.MouseEvent) => {\r\n    if (isDragging && zoomLevel > 1) {\r\n      e.preventDefault();\r\n\r\n      // Calculate the movement delta\r\n      const dx = e.clientX - dragStart.x;\r\n      const dy = e.clientY - dragStart.y;\r\n\r\n      // Update drag start position for next move\r\n      setDragStart({ x: e.clientX, y: e.clientY });\r\n\r\n      // Update image position with smooth transition\r\n      requestAnimationFrame(() => {\r\n        setImagePosition((prev) => ({\r\n          x: prev.x + dx,\r\n          y: prev.y + dy,\r\n        }));\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleMouseUp = () => {\r\n    if (isDragging) {\r\n      setIsDragging(false);\r\n      // Reset cursor style\r\n      if (containerRef.current) {\r\n        containerRef.current.style.cursor = \"grab\";\r\n      }\r\n    }\r\n  };\r\n\r\n  const resetZoom = () => {\r\n    setZoomLevel(1);\r\n    setImagePosition({ x: 0, y: 0 });\r\n  };\r\n\r\n  // Xác định media hiện tại để hiển thị\r\n  let currentMedia: Media | null = null;\r\n  if (message.content.media && message.content.media.length > 0) {\r\n    currentMedia = message.content.media[currentMediaIndex];\r\n  } else if (message.content.image) {\r\n    currentMedia = {\r\n      url: message.content.image,\r\n      type: \"IMAGE\",\r\n      fileId: \"legacy-image\",\r\n      fileName: \"image.jpg\",\r\n      metadata: {\r\n        path: \"\",\r\n        size: 0,\r\n        mimeType: \"image/jpeg\",\r\n        extension: \"jpg\",\r\n        bucketName: \"\",\r\n        uploadedAt: new Date().toISOString(),\r\n        sizeFormatted: \"\",\r\n      },\r\n    };\r\n  } else if (message.content.video) {\r\n    currentMedia = {\r\n      url: message.content.video,\r\n      type: \"VIDEO\",\r\n      fileId: \"legacy-video\",\r\n      fileName: \"video.mp4\",\r\n      metadata: {\r\n        path: \"\",\r\n        size: 0,\r\n        mimeType: \"video/mp4\",\r\n        extension: \"mp4\",\r\n        bucketName: \"\",\r\n        uploadedAt: new Date().toISOString(),\r\n        sizeFormatted: \"\",\r\n      },\r\n    };\r\n  }\r\n\r\n  const hasMultipleMedia =\r\n    message.content.media && message.content.media.length > 1;\r\n\r\n  // Add zoom control functions\r\n  const zoomIn = () => {\r\n    const newZoomLevel = Math.min(3, zoomLevel + 0.25);\r\n    setZoomLevel(newZoomLevel);\r\n  };\r\n\r\n  const zoomOut = () => {\r\n    const newZoomLevel = Math.max(1, zoomLevel - 0.25);\r\n    setZoomLevel(newZoomLevel);\r\n    if (newZoomLevel === 1) {\r\n      setImagePosition({ x: 0, y: 0 });\r\n    }\r\n  };\r\n\r\n  // Sử dụng createPortal để render dialog trực tiếp vào body\r\n  return createPortal(\r\n    <div className=\"fixed inset-0 z-50 bg-black flex flex-col overflow-hidden\">\r\n      {/* Header */}\r\n      <header className=\"h-12 flex items-center justify-between px-4 bg-black/90 text-white border-b border-gray-800\">\r\n        <div className=\"text-sm font-medium truncate\">\r\n          {currentMedia?.fileName ||\r\n            (message.senderId === currentUser?.id\r\n              ? currentUser?.userInfo?.fullName || \"Bạn\"\r\n              : userInfo?.fullName ||\r\n                message.sender?.userInfo?.fullName ||\r\n                \"Tin nhắn\")}\r\n        </div>\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"text-white hover:bg-white/10 rounded-full h-8 w-8 hover:text-white\"\r\n            onClick={() => setShowSidebar(!showSidebar)}\r\n          >\r\n            {showSidebar ? (\r\n              <PanelRightClose className=\"h-4 w-4\" />\r\n            ) : (\r\n              <PanelRightOpen className=\"h-4 w-4\" />\r\n            )}\r\n          </Button>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"text-white hover:bg-white/10 rounded-full h-8 w-8 hover:text-white\"\r\n            onClick={onClose}\r\n          >\r\n            <X className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Main content area with sidebar */}\r\n      <div className=\"flex flex-1 overflow-hidden\">\r\n        {/* Main content */}\r\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n          <div className=\"flex-1 flex items-center justify-center relative overflow-hidden\">\r\n            {currentMedia && (\r\n              <>\r\n                {currentMedia.type === \"IMAGE\" ? (\r\n                  <div\r\n                    ref={containerRef}\r\n                    className=\"relative max-h-full max-w-full w-auto h-auto \"\r\n                    onWheel={handleWheel}\r\n                    onMouseDown={handleMouseDown}\r\n                    onMouseMove={handleMouseMove}\r\n                    onMouseUp={handleMouseUp}\r\n                    onMouseLeave={handleMouseUp}\r\n                    style={{ cursor: zoomLevel > 1 ? \"grab\" : \"default\" }}\r\n                  >\r\n                    <Image\r\n                      ref={imageRef}\r\n                      src={currentMedia.url || \"/placeholder.svg\"}\r\n                      alt={currentMedia.fileName}\r\n                      className=\"object-contain max-h-[calc(100vh-96px)] w-auto h-auto will-change-transform\"\r\n                      width={1200}\r\n                      height={800}\r\n                      unoptimized\r\n                      style={{\r\n                        maxWidth: \"100%\",\r\n                        transform: `scale(${zoomLevel})`,\r\n                        transformOrigin: \"center\",\r\n                        translate: `${imagePosition.x}px ${imagePosition.y}px`,\r\n                        transition: isDragging\r\n                          ? \"none\"\r\n                          : \"transform 0.1s ease-out, translate 0.1s ease-out\",\r\n                      }}\r\n                    />\r\n                    {zoomLevel > 1 && (\r\n                      <div className=\"absolute top-4 right-4 text-white text-[10px] px-2 py-0.5 rounded drop-shadow-[0_0_2px_rgba(0,0,0,0.8)]\">\r\n                        {Math.round(zoomLevel * 100)}%\r\n                      </div>\r\n                    )}\r\n                    {zoomLevel > 1 && (\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        className=\"absolute bottom-4 left-4 bg-white/20 text-white hover:bg-white/40 rounded-full\"\r\n                        onClick={resetZoom}\r\n                      >\r\n                        <RefreshCcw className=\"h-5 w-5\" />\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                ) : currentMedia.type === \"VIDEO\" ? (\r\n                  <div className=\"relative max-h-full max-w-full w-auto h-auto\">\r\n                    <video\r\n                      src={currentMedia.url}\r\n                      controls\r\n                      className=\"max-h-[calc(100vh-96px)] w-auto h-auto\"\r\n                      style={{ maxWidth: \"100%\" }}\r\n                    />\r\n                  </div>\r\n                ) : currentMedia.type === \"AUDIO\" ? (\r\n                  <div className=\"bg-white rounded-lg max-w-5xl w-full max-h-[calc(100vh-96px)] flex flex-col overflow-hidden\">\r\n                    {/* Audio header with file info */}\r\n                    <div className=\"p-3 border-b flex items-center bg-gray-50\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <div className=\"flex-shrink-0\">\r\n                          <Music className=\"h-6 w-6 text-purple-600\" />\r\n                        </div>\r\n                        <div className=\"truncate\">\r\n                          <h3 className=\"font-medium text-base truncate max-w-[500px]\">\r\n                            {currentMedia.fileName}\r\n                          </h3>\r\n                          <p className=\"text-xs text-gray-500\">\r\n                            {currentMedia.metadata.sizeFormatted}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Audio player with visualizer */}\r\n                    <div className=\"flex-1 flex items-center justify-center p-8 bg-gray-50\">\r\n                      <AudioVisualizer\r\n                        url={currentMedia.url}\r\n                        fileName={currentMedia.fileName}\r\n                        onDownload={() => handleDownload(currentMedia)}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"bg-white rounded-lg max-w-5xl w-full max-h-[calc(100vh-96px)] flex flex-col overflow-hidden\">\r\n                    {/* Clean header with file info only */}\r\n                    <div className=\"p-3 border-b flex items-center bg-gray-50\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <div className=\"flex-shrink-0\">\r\n                          {getFileIcon(currentMedia.metadata.extension, 24)}\r\n                        </div>\r\n                        <div className=\"truncate\">\r\n                          <h3 className=\"font-medium text-base truncate max-w-[500px]\">\r\n                            {currentMedia.fileName}\r\n                          </h3>\r\n                          <p className=\"text-xs text-gray-500\">\r\n                            {currentMedia.metadata.sizeFormatted}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Content area */}\r\n                    <div\r\n                      className={`flex-1 ${\r\n                        currentMedia.metadata.extension === \"pdf\"\r\n                          ? \"overflow-hidden\"\r\n                          : \"overflow-auto\"\r\n                      } min-h-[300px] max-h-[calc(100vh-144px)]`}\r\n                    >\r\n                      {currentMedia.metadata.extension === \"pdf\" ? (\r\n                        <iframe\r\n                          src={`${currentMedia.url}#toolbar=0`}\r\n                          className=\"w-full h-full min-h-[500px] border-0\"\r\n                          title={currentMedia.fileName}\r\n                          style={{ height: \"calc(100vh - 144px)\" }}\r\n                        />\r\n                      ) : isOfficeDocument(currentMedia.metadata.extension) ? (\r\n                        <iframe\r\n                          src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(\r\n                            currentMedia.url,\r\n                          )}`}\r\n                          className=\"w-full h-full min-h-[500px] border-0\"\r\n                          title={currentMedia.fileName}\r\n                        />\r\n                      ) : isTextFile(currentMedia.metadata.extension) ? (\r\n                        <div className=\"p-4 font-mono text-sm whitespace-pre-wrap h-full overflow-auto\">\r\n                          {fileContent || (\r\n                            <div className=\"flex items-center justify-center h-full\">\r\n                              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900\"></div>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"flex flex-col items-center justify-center h-full p-8 text-center\">\r\n                          <div className=\"mb-4 p-6 bg-gray-50 rounded-full\">\r\n                            {getFileIcon(currentMedia.metadata.extension, 64)}\r\n                          </div>\r\n                          <p className=\"text-gray-500 mb-4\">\r\n                            Không thể hiển thị trực tiếp tệp này. Vui lòng tải\r\n                            xuống để xem.\r\n                          </p>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </>\r\n            )}\r\n\r\n            {/* Nút điều hướng giữa các media */}\r\n            {hasMultipleMedia && (\r\n              <>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"absolute left-12 top-1/2 -translate-y-1/2 bg-black/30 text-white hover:bg-black/50 rounded-full h-10 w-10\"\r\n                  onClick={handlePrevMedia}\r\n                >\r\n                  <ChevronLeft className=\"h-6 w-6\" />\r\n                </Button>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"absolute right-12 top-1/2 -translate-y-1/2 bg-black/30 text-white hover:bg-black/50 rounded-full h-10 w-10\"\r\n                  onClick={handleNextMedia}\r\n                >\r\n                  <ChevronRight className=\"h-6 w-6\" />\r\n                </Button>\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          {/* Footer controls */}\r\n          <div className=\"h-12 flex items-center justify-between px-4 bg-black/90 border-t border-gray-800\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"text-white text-sm\">\r\n                {currentMediaIndex + 1}/{message.content.media?.length || 1}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-2\">\r\n              {/* Nút tải xuống */}\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-white hover:bg-white/10 rounded-full h-8 w-8 hover:text-white\"\r\n                onClick={() => currentMedia && handleDownload(currentMedia)}\r\n              >\r\n                <Download className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Sidebar */}\r\n        {showSidebar && (\r\n          <div className=\"w-72 border-l border-gray-800 bg-black/80 flex flex-col overflow-hidden\">\r\n            {/* Sender info */}\r\n            <div className=\"p-3 border-b border-gray-800\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Avatar className=\"h-8 w-8\">\r\n                  <AvatarImage\r\n                    src={\r\n                      message.senderId === currentUser?.id\r\n                        ? currentUser?.userInfo?.profilePictureUrl || undefined\r\n                        : userInfo?.profilePictureUrl ||\r\n                          message.sender?.userInfo?.profilePictureUrl ||\r\n                          undefined\r\n                    }\r\n                    className=\"object-cover\"\r\n                  />\r\n                  <AvatarFallback>\r\n                    {message.senderId === currentUser?.id\r\n                      ? getUserInitials(currentUser)\r\n                      : getUserInitials(message.sender)}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <div>\r\n                  <h3 className=\"text-sm font-medium text-white\">\r\n                    {message.senderId === currentUser?.id\r\n                      ? currentUser?.userInfo?.fullName || \"Bạn\"\r\n                      : userInfo?.fullName ||\r\n                        message.sender?.userInfo?.fullName ||\r\n                        \"Người dùng\"}\r\n                  </h3>\r\n                  <p className=\"text-xs text-gray-400\">\r\n                    {formatMessageTime(message.createdAt)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              {message.content.text && (\r\n                <p className=\"mt-2 text-sm text-gray-300 break-words\">\r\n                  {message.content.text}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            {/* Media thumbnails */}\r\n            {hasMultipleMedia && (\r\n              <div className=\"flex-1 overflow-y-auto p-2\">\r\n                <div className=\"grid grid-cols-2 gap-2\">\r\n                  {message.content.media?.map((media, index) => (\r\n                    <div\r\n                      key={media.fileId}\r\n                      className={`relative aspect-square cursor-pointer rounded overflow-hidden ${\r\n                        index === currentMediaIndex\r\n                          ? \"ring-2 ring-blue-500\"\r\n                          : \"\"\r\n                      }`}\r\n                      onClick={() => {\r\n                        setCurrentMediaIndex(index);\r\n                      }}\r\n                    >\r\n                      {media.type === \"IMAGE\" ? (\r\n                        <Image\r\n                          src={media.url || \"/placeholder.svg\"}\r\n                          alt={media.fileName}\r\n                          className=\"object-cover w-full h-full\"\r\n                          width={120}\r\n                          height={120}\r\n                          unoptimized\r\n                        />\r\n                      ) : media.type === \"VIDEO\" ? (\r\n                        <div className=\"w-full h-full bg-gray-800 flex items-center justify-center relative\">\r\n                          {media.thumbnailUrl ? (\r\n                            <Image\r\n                              src={media.thumbnailUrl || \"/placeholder.svg\"}\r\n                              alt={media.fileName}\r\n                              className=\"object-cover w-full h-full\"\r\n                              width={120}\r\n                              height={120}\r\n                              unoptimized\r\n                            />\r\n                          ) : (\r\n                            <div className=\"flex items-center justify-center w-full h-full\">\r\n                              <svg\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                width=\"20\"\r\n                                height=\"20\"\r\n                                viewBox=\"0 0 24 24\"\r\n                                fill=\"none\"\r\n                                stroke=\"currentColor\"\r\n                                strokeWidth=\"2\"\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                className=\"text-white\"\r\n                              >\r\n                                <polygon points=\"5 3 19 12 5 21 5 3\" />\r\n                              </svg>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"w-full h-full bg-gray-700 flex flex-col items-center justify-center p-2\">\r\n                          {getFileIcon(media.metadata.extension, 24)}\r\n                          <span className=\"text-xs mt-1 text-center truncate w-full text-white\">\r\n                            {media.fileName}\r\n                          </span>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Zoom controls */}\r\n      {currentMedia && currentMedia.type === \"IMAGE\" && (\r\n        <div className=\"fixed right-4 top-1/2 -translate-y-1/2 bg-black/50 rounded-full p-1 flex flex-col gap-1 z-50\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"text-white hover:bg-white/10 rounded-full h-8 w-8 hover:text-white\"\r\n            onClick={zoomIn}\r\n          >\r\n            <ZoomIn className=\"h-4 w-4\" />\r\n          </Button>\r\n          <div className=\"w-1 h-20 bg-white/20 rounded-full mx-auto relative\">\r\n            <div\r\n              className=\"w-1 bg-white rounded-full absolute bottom-0\"\r\n              style={{\r\n                height: `${((zoomLevel - 1) / 2) * 100}%`,\r\n                maxHeight: \"100%\",\r\n              }}\r\n            ></div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"text-white hover:bg-white/10 rounded-full h-8 w-8 hover:text-white\"\r\n            onClick={zoomOut}\r\n          >\r\n            <ZoomOut className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Forward popover */}\r\n      <Popover open={isForwardMenuOpen} onOpenChange={setIsForwardMenuOpen}>\r\n        <PopoverContent className=\"w-80 p-0\" align=\"end\">\r\n          {!forwardSuccess ? (\r\n            <div className=\"max-h-80 overflow-auto\">\r\n              <div className=\"p-3 border-b\">\r\n                <h3 className=\"font-medium\">Chuyển tiếp tới</h3>\r\n                {selectedRecipients.length > 0 && (\r\n                  <div className=\"mt-2 flex flex-wrap gap-1\">\r\n                    {selectedRecipients.map((recipient) => (\r\n                      <div\r\n                        key={`${recipient.type}-${recipient.id}`}\r\n                        className=\"bg-blue-100 text-blue-800 text-xs rounded-full px-2 py-1 flex items-center gap-1\"\r\n                      >\r\n                        <span>{recipient.name}</span>\r\n                        <button\r\n                          className=\"text-blue-600 hover:text-blue-800\"\r\n                          onClick={() =>\r\n                            toggleRecipient(\r\n                              recipient.type,\r\n                              recipient.id,\r\n                              recipient.name,\r\n                            )\r\n                          }\r\n                        >\r\n                          <X className=\"h-3 w-3\" />\r\n                        </button>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <div className=\"divide-y\">\r\n                {conversations.map((conversation) => (\r\n                  <div\r\n                    key={conversation.contact.id}\r\n                    className={`p-3 flex items-center justify-between hover:bg-gray-50 cursor-pointer ${\r\n                      selectedRecipients.some(\r\n                        (r) => r.id === conversation.contact.id,\r\n                      )\r\n                        ? \"bg-blue-50\"\r\n                        : \"\"\r\n                    }`}\r\n                    onClick={() =>\r\n                      toggleRecipient(\r\n                        conversation.type as \"USER\" | \"GROUP\",\r\n                        conversation.contact.id,\r\n                        conversation.contact.userInfo?.fullName || \"Unknown\",\r\n                      )\r\n                    }\r\n                  >\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Avatar className=\"h-8 w-8\">\r\n                        <AvatarImage\r\n                          src={\r\n                            conversation.contact.userInfo?.profilePictureUrl ||\r\n                            undefined\r\n                          }\r\n                        />\r\n                        <AvatarFallback>\r\n                          {conversation.contact.userInfo?.fullName\r\n                            ?.slice(0, 2)\r\n                            .toUpperCase() || \"??\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"text-sm font-medium\">\r\n                          {conversation.contact.userInfo?.fullName}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-500\">\r\n                          {conversation.type === \"GROUP\"\r\n                            ? \"Nhóm\"\r\n                            : \"Người dùng\"}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    {selectedRecipients.some(\r\n                      (r) => r.id === conversation.contact.id,\r\n                    ) && <CheckCircle2 className=\"h-5 w-5 text-blue-600\" />}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <div className=\"p-3 border-t\">\r\n                <Button\r\n                  className=\"w-full\"\r\n                  disabled={selectedRecipients.length === 0}\r\n                  onClick={handleForwardMessage}\r\n                >\r\n                  Chuyển tiếp\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"p-4 text-center\">\r\n              <CheckCircle2 className=\"h-8 w-8 text-green-600 mx-auto mb-2\" />\r\n              <p className=\"font-medium\">Đã chuyển tiếp thành công!</p>\r\n            </div>\r\n          )}\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>,\r\n    document.body,\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AA7BA;;;;;;;;;;;;;;;;AAsCe,SAAS,oBAAoB,EAC1C,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACiB;IACzB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAEzD,EAAE;IACJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,EAAE,0BAA0B,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAClD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD;IAC9C,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IAEtD,qCAAqC;IACrC,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,WAAW,OAAO;QACvB,MAAM,mBAAmB;YAAC;YAAO;YAAQ;YAAO;YAAQ;YAAO;SAAO;QACtE,OAAO,iBAAiB,QAAQ,CAAC,UAAU,WAAW;IACxD;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,WAAW,OAAO;QACvB,MAAM,iBAAiB;YACrB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO,eAAe,QAAQ,CAAC,UAAU,WAAW;IACtD;IAEA,8CAA8C;IAC9C,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,WAAW,OAAO;QACvB,MAAM,kBAAkB;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;SAAO;QACnE,OAAO,gBAAgB,QAAQ,CAAC,UAAU,WAAW;IACvD;IAEA,MAAM,cAAc,CAAC,WAAoB,OAAO,EAAE;QAChD,MAAM,WAAW;QACjB,MAAM,cAAc,QAAQ,KAAK,IAAI;QAErC,IAAI,CAAC,WAAW;YACd,qBACE,8OAAC;gBACC,OAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,SAAQ;gBACR,MAAK;gBACL,QAAO;gBACP,aAAa;gBACb,eAAc;gBACd,gBAAe;gBACf,WAAU;;kCAEV,8OAAC;wBAAK,GAAE;;;;;;kCACR,8OAAC;wBAAS,QAAO;;;;;;;;;;;;QAGvB;QAEA,OAAQ,UAAU,WAAW;YAC3B,KAAK;gBACH,qBACE,8OAAC;oBACC,OAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAa;oBACb,eAAc;oBACd,gBAAe;oBACf,WAAU;;sCAEV,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAS,QAAO;;;;;;sCACjB,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd,KAAK;YACL,KAAK;gBACH,qBACE,8OAAC;oBACC,OAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAa;oBACb,eAAc;oBACd,gBAAe;oBACf,WAAU;;sCAEV,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAS,QAAO;;;;;;sCACjB,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd,KAAK;YACL,KAAK;gBACH,qBACE,8OAAC;oBACC,OAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAa;oBACb,eAAc;oBACd,gBAAe;oBACf,WAAU;;sCAEV,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAS,QAAO;;;;;;sCACjB,8OAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAK,OAAM;4BAAI,QAAO;;;;;;sCACpC,8OAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd,KAAK;YACL,KAAK;gBACH,qBACE,8OAAC;oBACC,OAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAa;oBACb,eAAc;oBACd,gBAAe;oBACf,WAAU;;sCAEV,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAS,QAAO;;;;;;sCACjB,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;;;;;;;;;;;;YAGhC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBACE,8OAAC;oBACC,OAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAa;oBACb,eAAc;oBACd,gBAAe;oBACf,WAAU;;sCAEV,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAS,QAAO;;;;;;sCACjB,8OAAC;4BAAK,IAAG;4BAAI,IAAG;4BAAK,IAAG;4BAAK,IAAG;;;;;;sCAChC,8OAAC;4BAAK,IAAG;4BAAI,IAAG;4BAAK,IAAG;4BAAK,IAAG;;;;;;;;;;;;YAGtC;gBACE,8BAA8B;gBAC9B,IAAI,YAAY,YAAY;oBAC1B,qBACE,8OAAC,oMAAA,CAAA,QAAK;wBACJ,MAAM;wBACN,aAAa;wBACb,WAAU;;;;;;gBAGhB;gBACA,oBAAoB;gBACpB,qBACE,8OAAC;oBACC,OAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAa;oBACb,eAAc;oBACd,gBAAe;oBACf,WAAU;;sCAEV,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAS,QAAO;;;;;;;;;;;;QAGzB;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,sBAAsB,EAAE;YACxB,kBAAkB;YAClB,qBAAqB;YACrB,eAAe;QACjB;IACF,GAAG;QAAC;KAAO;IAEX,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAA6B;QACjC,IAAI,SAAS,QAAQ,SAAS,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC9D,eAAe,QAAQ,OAAO,CAAC,KAAK,CAAC,kBAAkB;QACzD,OAAO,IAAI,SAAS,QAAQ,OAAO;YACjC,eAAe;gBACb,KAAK,QAAQ,OAAO,CAAC,KAAK;gBAC1B,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,UAAU;oBACR,MAAM;oBACN,MAAM;oBACN,UAAU;oBACV,WAAW;oBACX,YAAY;oBACZ,YAAY,IAAI,OAAO,WAAW;oBAClC,eAAe;gBACjB;YACF;QACF,OAAO,IAAI,SAAS,QAAQ,OAAO;YACjC,eAAe;gBACb,KAAK,QAAQ,OAAO,CAAC,KAAK;gBAC1B,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,UAAU;oBACR,MAAM;oBACN,MAAM;oBACN,UAAU;oBACV,WAAW;oBACX,YAAY;oBACZ,YAAY,IAAI,OAAO,WAAW;oBAClC,eAAe;gBACjB;YACF;QACF;QACA,IAAI,gBAAgB,WAAW,aAAa,QAAQ,CAAC,SAAS,GAAG;YAC/D,eAAe,OAAO,8BAA8B;YAEpD,MAAM,aAAa,GAAG,EACnB,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI,IAChC,IAAI,CAAC,CAAC;gBACL,eAAe;YACjB,GACC,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,eAAe;YACjB;QACJ;IACF,GAAG;QAAC;QAAS;KAAkB;IAE/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,iCAAiC;QACjC,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,yCAAyC;QACzC,aAAa;QACb,iBAAiB;YAAE,GAAG;YAAG,GAAG;QAAE;QAE9B,OAAO;YACL,+CAA+C;YAC/C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,uBAAuB;QAC3B,IAAI,CAAC,WAAW,mBAAmB,MAAM,KAAK,GAAG;QAEjD,MAAM,aAAa,mBAAmB,GAAG,CAAC,CAAC,YAAc,CAAC;gBACxD,MAAM,UAAU,IAAI;gBACpB,IAAI,UAAU,EAAE;YAClB,CAAC;QAED,MAAM,UAAU,MAAM,2BAA2B,QAAQ,EAAE,EAAE;QAE7D,IAAI,SAAS;YACX,kBAAkB;YAClB,4CAA4C;YAC5C,WAAW;gBACT,qBAAqB;gBACrB,sBAAsB,EAAE;gBACxB,kBAAkB;YACpB,GAAG;QACL;IACF;IAEA,MAAM,kBAAkB,CACtB,MACA,IACA;QAEA,sBAAsB,CAAC;YACrB,MAAM,SAAS,KAAK,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,MAAM,EAAE,IAAI,KAAK;YAE1D,IAAI,QAAQ;gBACV,OAAO,KAAK,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EAAE,KAAK,MAAM,EAAE,IAAI,KAAK,IAAI;YAC5D,OAAO;gBACL,OAAO;uBAAI;oBAAM;wBAAE;wBAAM;wBAAI;oBAAK;iBAAE;YACtC;QACF;IACF;IAEA,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,OAAO;IAE5C,MAAM,iBAAiB,CAAC;QACtB,sCAAsC;QACtC,MAAM,MAAM,GAAG,EACZ,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI,IAChC,IAAI,CAAC,CAAC;YACL,iCAAiC;YACjC,MAAM,UAAU,IAAI,eAAe,CAAC;YAEpC,oCAAoC;YACpC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,MAAM,QAAQ;YAE9B,sCAAsC;YACtC,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,uBAAuB;YACvB,WAAW,IAAM,IAAI,eAAe,CAAC,UAAU;QACjD,GACC,KAAK,CAAC,CAAC;YACN,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACJ;IAEA,MAAM,kBAAkB;QACtB,IAAI,SAAS,QAAQ,SAAS,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC9D,qBAAqB,CAAC,OACpB,SAAS,IAAI,QAAQ,OAAO,CAAC,KAAK,CAAE,MAAM,GAAG,IAAI,OAAO;YAE1D;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,SAAS,QAAQ,SAAS,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC9D,qBAAqB,CAAC,OACpB,SAAS,QAAQ,OAAO,CAAC,KAAK,CAAE,MAAM,GAAG,IAAI,IAAI,OAAO;YAE1D;QACF;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,gBAAgB,aAAa,IAAI,KAAK,SAAS;QAEpD,EAAE,cAAc;QAEhB,2BAA2B;QAC3B,MAAM,gBAAgB,EAAE,MAAM,GAAG,IAAI,IAAI,CAAC;QAE1C,2BAA2B;QAC3B,MAAM,eAAe,KAAK,GAAG,CAC3B,GACA,KAAK,GAAG,CAAC,GAAG,YAAY,gBAAgB;QAG1C,oCAAoC;QACpC,IAAI,iBAAiB,WAAW;YAC9B,aAAa;YAEb,wCAAwC;YACxC,IAAI,iBAAiB,GAAG;gBACtB,iBAAiB;oBAAE,GAAG;oBAAG,GAAG;gBAAE;YAChC;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY,GAAG;YACjB,EAAE,cAAc;YAChB,cAAc;YACd,aAAa;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAE1C,sBAAsB;YACtB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACtC;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,cAAc,YAAY,GAAG;YAC/B,EAAE,cAAc;YAEhB,+BAA+B;YAC/B,MAAM,KAAK,EAAE,OAAO,GAAG,UAAU,CAAC;YAClC,MAAM,KAAK,EAAE,OAAO,GAAG,UAAU,CAAC;YAElC,2CAA2C;YAC3C,aAAa;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAE1C,+CAA+C;YAC/C,sBAAsB;gBACpB,iBAAiB,CAAC,OAAS,CAAC;wBAC1B,GAAG,KAAK,CAAC,GAAG;wBACZ,GAAG,KAAK,CAAC,GAAG;oBACd,CAAC;YACH;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,YAAY;YACd,cAAc;YACd,qBAAqB;YACrB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACtC;QACF;IACF;IAEA,MAAM,YAAY;QAChB,aAAa;QACb,iBAAiB;YAAE,GAAG;YAAG,GAAG;QAAE;IAChC;IAEA,sCAAsC;IACtC,IAAI,eAA6B;IACjC,IAAI,QAAQ,OAAO,CAAC,KAAK,IAAI,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;QAC7D,eAAe,QAAQ,OAAO,CAAC,KAAK,CAAC,kBAAkB;IACzD,OAAO,IAAI,QAAQ,OAAO,CAAC,KAAK,EAAE;QAChC,eAAe;YACb,KAAK,QAAQ,OAAO,CAAC,KAAK;YAC1B,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;gBACR,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,YAAY,IAAI,OAAO,WAAW;gBAClC,eAAe;YACjB;QACF;IACF,OAAO,IAAI,QAAQ,OAAO,CAAC,KAAK,EAAE;QAChC,eAAe;YACb,KAAK,QAAQ,OAAO,CAAC,KAAK;YAC1B,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;gBACR,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,YAAY,IAAI,OAAO,WAAW;gBAClC,eAAe;YACjB;QACF;IACF;IAEA,MAAM,mBACJ,QAAQ,OAAO,CAAC,KAAK,IAAI,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;IAE1D,6BAA6B;IAC7B,MAAM,SAAS;QACb,MAAM,eAAe,KAAK,GAAG,CAAC,GAAG,YAAY;QAC7C,aAAa;IACf;IAEA,MAAM,UAAU;QACd,MAAM,eAAe,KAAK,GAAG,CAAC,GAAG,YAAY;QAC7C,aAAa;QACb,IAAI,iBAAiB,GAAG;YACtB,iBAAiB;gBAAE,GAAG;gBAAG,GAAG;YAAE;QAChC;IACF;IAEA,2DAA2D;IAC3D,qBAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBAChB,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACZ,cAAc,YACb,CAAC,QAAQ,QAAQ,KAAK,aAAa,KAC/B,aAAa,UAAU,YAAY,QACnC,UAAU,YACV,QAAQ,MAAM,EAAE,UAAU,YAC1B,UAAU;;;;;;kCAElB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe,CAAC;0CAE9B,4BACC,8OAAC,gOAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;yDAE3B,8OAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;0CAG9B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMnB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,8BACC;kDACG,aAAa,IAAI,KAAK,wBACrB,8OAAC;4CACC,KAAK;4CACL,WAAU;4CACV,SAAS;4CACT,aAAa;4CACb,aAAa;4CACb,WAAW;4CACX,cAAc;4CACd,OAAO;gDAAE,QAAQ,YAAY,IAAI,SAAS;4CAAU;;8DAEpD,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAK,aAAa,GAAG,IAAI;oDACzB,KAAK,aAAa,QAAQ;oDAC1B,WAAU;oDACV,OAAO;oDACP,QAAQ;oDACR,WAAW;oDACX,OAAO;wDACL,UAAU;wDACV,WAAW,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;wDAChC,iBAAiB;wDACjB,WAAW,GAAG,cAAc,CAAC,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC,EAAE,CAAC;wDACtD,YAAY,aACR,SACA;oDACN;;;;;;gDAED,YAAY,mBACX,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,KAAK,CAAC,YAAY;wDAAK;;;;;;;gDAGhC,YAAY,mBACX,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;mDAI1B,aAAa,IAAI,KAAK,wBACxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,KAAK,aAAa,GAAG;gDACrB,QAAQ;gDACR,WAAU;gDACV,OAAO;oDAAE,UAAU;gDAAO;;;;;;;;;;mDAG5B,aAAa,IAAI,KAAK,wBACxB,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFACX,aAAa,QAAQ;;;;;;kFAExB,8OAAC;wEAAE,WAAU;kFACV,aAAa,QAAQ,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;8DAO5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,6IAAA,CAAA,UAAe;wDACd,KAAK,aAAa,GAAG;wDACrB,UAAU,aAAa,QAAQ;wDAC/B,YAAY,IAAM,eAAe;;;;;;;;;;;;;;;;iEAKvC,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,YAAY,aAAa,QAAQ,CAAC,SAAS,EAAE;;;;;;0EAEhD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFACX,aAAa,QAAQ;;;;;;kFAExB,8OAAC;wEAAE,WAAU;kFACV,aAAa,QAAQ,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;8DAO5C,8OAAC;oDACC,WAAW,CAAC,OAAO,EACjB,aAAa,QAAQ,CAAC,SAAS,KAAK,QAChC,oBACA,gBACL,wCAAwC,CAAC;8DAEzC,aAAa,QAAQ,CAAC,SAAS,KAAK,sBACnC,8OAAC;wDACC,KAAK,GAAG,aAAa,GAAG,CAAC,UAAU,CAAC;wDACpC,WAAU;wDACV,OAAO,aAAa,QAAQ;wDAC5B,OAAO;4DAAE,QAAQ;wDAAsB;;;;;+DAEvC,iBAAiB,aAAa,QAAQ,CAAC,SAAS,kBAClD,8OAAC;wDACC,KAAK,CAAC,mDAAmD,EAAE,mBACzD,aAAa,GAAG,GACf;wDACH,WAAU;wDACV,OAAO,aAAa,QAAQ;;;;;+DAE5B,WAAW,aAAa,QAAQ,CAAC,SAAS,kBAC5C,8OAAC;wDAAI,WAAU;kEACZ,6BACC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;6EAKrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,YAAY,aAAa,QAAQ,CAAC,SAAS,EAAE;;;;;;0EAEhD,8OAAC;gEAAE,WAAU;0EAAqB;;;;;;;;;;;;;;;;;;;;;;;;oCAa/C,kCACC;;0DACE,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;0DAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAEzB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;0DAET,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;0CAOhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,oBAAoB;gDAAE;gDAAE,QAAQ,OAAO,CAAC,KAAK,EAAE,UAAU;;;;;;;;;;;;kDAI9D,8OAAC;wCAAI,WAAU;kDAEb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,gBAAgB,eAAe;sDAE9C,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAO3B,6BACC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDACV,KACE,QAAQ,QAAQ,KAAK,aAAa,KAC9B,aAAa,UAAU,qBAAqB,YAC5C,UAAU,qBACV,QAAQ,MAAM,EAAE,UAAU,qBAC1B;wDAEN,WAAU;;;;;;kEAEZ,8OAAC,kIAAA,CAAA,iBAAc;kEACZ,QAAQ,QAAQ,KAAK,aAAa,KAC/B,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,eAChB,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;;0DAGtC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,QAAQ,QAAQ,KAAK,aAAa,KAC/B,aAAa,UAAU,YAAY,QACnC,UAAU,YACV,QAAQ,MAAM,EAAE,UAAU,YAC1B;;;;;;kEAEN,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;;;;;;;oCAIzC,QAAQ,OAAO,CAAC,IAAI,kBACnB,8OAAC;wCAAE,WAAU;kDACV,QAAQ,OAAO,CAAC,IAAI;;;;;;;;;;;;4BAM1B,kCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,sBAClC,8OAAC;4CAEC,WAAW,CAAC,8DAA8D,EACxE,UAAU,oBACN,yBACA,IACJ;4CACF,SAAS;gDACP,qBAAqB;4CACvB;sDAEC,MAAM,IAAI,KAAK,wBACd,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,MAAM,GAAG,IAAI;gDAClB,KAAK,MAAM,QAAQ;gDACnB,WAAU;gDACV,OAAO;gDACP,QAAQ;gDACR,WAAW;;;;;uDAEX,MAAM,IAAI,KAAK,wBACjB,8OAAC;gDAAI,WAAU;0DACZ,MAAM,YAAY,iBACjB,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,MAAM,YAAY,IAAI;oDAC3B,KAAK,MAAM,QAAQ;oDACnB,WAAU;oDACV,OAAO;oDACP,QAAQ;oDACR,WAAW;;;;;yEAGb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,SAAQ;wDACR,MAAK;wDACL,QAAO;wDACP,aAAY;wDACZ,eAAc;wDACd,gBAAe;wDACf,WAAU;kEAEV,cAAA,8OAAC;4DAAQ,QAAO;;;;;;;;;;;;;;;;;;;;qEAMxB,8OAAC;gDAAI,WAAU;;oDACZ,YAAY,MAAM,QAAQ,CAAC,SAAS,EAAE;kEACvC,8OAAC;wDAAK,WAAU;kEACb,MAAM,QAAQ;;;;;;;;;;;;2CArDhB,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;YAmEhC,gBAAgB,aAAa,IAAI,KAAK,yBACrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,QAAQ,GAAG,AAAC,CAAC,YAAY,CAAC,IAAI,IAAK,IAAI,CAAC,CAAC;gCACzC,WAAW;4BACb;;;;;;;;;;;kCAGJ,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAMzB,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAmB,cAAc;0BAC9C,cAAA,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAW,OAAM;8BACxC,CAAC,+BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAc;;;;;;oCAC3B,mBAAmB,MAAM,GAAG,mBAC3B,8OAAC;wCAAI,WAAU;kDACZ,mBAAmB,GAAG,CAAC,CAAC,0BACvB,8OAAC;gDAEC,WAAU;;kEAEV,8OAAC;kEAAM,UAAU,IAAI;;;;;;kEACrB,8OAAC;wDACC,WAAU;wDACV,SAAS,IACP,gBACE,UAAU,IAAI,EACd,UAAU,EAAE,EACZ,UAAU,IAAI;kEAIlB,cAAA,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;+CAdV,GAAG,UAAU,IAAI,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE;;;;;;;;;;;;;;;;0CAqBlD,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;wCAEC,WAAW,CAAC,sEAAsE,EAChF,mBAAmB,IAAI,CACrB,CAAC,IAAM,EAAE,EAAE,KAAK,aAAa,OAAO,CAAC,EAAE,IAErC,eACA,IACJ;wCACF,SAAS,IACP,gBACE,aAAa,IAAI,EACjB,aAAa,OAAO,CAAC,EAAE,EACvB,aAAa,OAAO,CAAC,QAAQ,EAAE,YAAY;;0DAI/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC,kIAAA,CAAA,cAAW;gEACV,KACE,aAAa,OAAO,CAAC,QAAQ,EAAE,qBAC/B;;;;;;0EAGJ,8OAAC,kIAAA,CAAA,iBAAc;0EACZ,aAAa,OAAO,CAAC,QAAQ,EAAE,UAC5B,MAAM,GAAG,GACV,iBAAiB;;;;;;;;;;;;kEAGxB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO,CAAC,QAAQ,EAAE;;;;;;0EAElC,8OAAC;gEAAE,WAAU;0EACV,aAAa,IAAI,KAAK,UACnB,SACA;;;;;;;;;;;;;;;;;;4CAIT,mBAAmB,IAAI,CACtB,CAAC,IAAM,EAAE,EAAE,KAAK,aAAa,OAAO,CAAC,EAAE,mBACpC,8OAAC,qNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCA3CxB,aAAa,OAAO,CAAC,EAAE;;;;;;;;;;0CA+ClC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,UAAU,mBAAmB,MAAM,KAAK;oCACxC,SAAS;8CACV;;;;;;;;;;;;;;;;6CAML,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,8OAAC;gCAAE,WAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;cAMrC,SAAS,IAAI;AAEjB", "debugId": null}}]}