@startuml Giải tán nhóm - Activity Diagram
title G<PERSON><PERSON>i tán nhóm - Activity Diagram

|User|
start
:<PERSON><PERSON><PERSON> nhóm;
:<PERSON><PERSON><PERSON> chức năng giải tán nhóm;
:<PERSON><PERSON><PERSON> nhận giải tán nhóm;
:<PERSON><PERSON><PERSON> yêu cầu giải tán nhóm;

|System|
:Ki<PERSON><PERSON> tra quyền của người dùng;

if (Là trưởng nhóm?) then (Có)
  :<PERSON><PERSON><PERSON> danh sách tất cả thành viên;
  :<PERSON><PERSON><PERSON> t<PERSON>t cả thành viên khỏi nhóm;
  :<PERSON><PERSON><PERSON> nhóm khỏi cơ sở dữ liệu;
  :<PERSON><PERSON><PERSON> thông báo cho tất cả thành viên qua WebSocket;
  :<PERSON><PERSON><PERSON> về thông báo giải tán thành công;
else (Không)
  :<PERSON><PERSON><PERSON> về lỗi "Chỉ trưởng nhóm mới có thể giải tán nhóm";
endif

|User|
if (<PERSON><PERSON><PERSON><PERSON> tán nhóm thành công?) then (Có)
  :<PERSON><PERSON> thông báo giải tán nhóm thành công;
  :<PERSON><PERSON><PERSON><PERSON> đến danh sách nhóm;
else (Không)
  :Hiển thị thông báo lỗi;
endif

stop
@enduml
