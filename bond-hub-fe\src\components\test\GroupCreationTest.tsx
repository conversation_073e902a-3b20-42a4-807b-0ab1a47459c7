"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { createGroup } from "@/actions/group.action";
import { useAuthStore } from "@/store/authStore";
import { toast } from "sonner";

/**
 * Component test để kiểm tra logic tạo nhóm
 * Chỉ dùng để test, không dùng trong production
 */
export default function GroupCreationTest() {
  const [groupName, setGroupName] = useState("");
  const [memberIds, setMemberIds] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { currentUser } = useAuthStore();

  const handleTestCreateGroup = async () => {
    if (!groupName.trim()) {
      toast.error("Vui lòng nhập tên nhóm");
      return;
    }

    if (!memberIds.trim()) {
      toast.error("Vui lòng nhập ID thành viên (cách nhau bằng dấu phẩy)");
      return;
    }

    if (!currentUser?.id) {
      toast.error("Bạn cần đăng nhập để tạo nhóm");
      return;
    }

    setIsLoading(true);
    try {
      // Parse member IDs
      const memberIdList = memberIds
        .split(",")
        .map((id) => id.trim())
        .filter((id) => id.length > 0);

      if (memberIdList.length < 2) {
        toast.error("Cần ít nhất 2 thành viên để tạo nhóm");
        setIsLoading(false);
        return;
      }

      // Create initial members array
      const initialMembers = memberIdList.map((userId) => ({
        userId: userId,
      }));

      console.log("[GroupCreationTest] Creating group with:", {
        name: groupName,
        creatorId: currentUser.id,
        initialMembers,
      });

      // Call create group API
      const result = await createGroup({
        name: groupName,
        creatorId: currentUser.id,
        initialMembers,
      });

      if (result.success) {
        toast.success("Tạo nhóm thành công!");
        console.log("[GroupCreationTest] Group created:", result.group);
        
        // Reset form
        setGroupName("");
        setMemberIds("");
      } else {
        toast.error(result.error || "Không thể tạo nhóm");
        console.error("[GroupCreationTest] Failed to create group:", result.error);
      }
    } catch (error) {
      console.error("[GroupCreationTest] Error:", error);
      toast.error("Đã xảy ra lỗi khi tạo nhóm");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Test Group Creation</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            Tên nhóm:
          </label>
          <Input
            value={groupName}
            onChange={(e) => setGroupName(e.target.value)}
            placeholder="Nhập tên nhóm..."
            disabled={isLoading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            ID thành viên (cách nhau bằng dấu phẩy):
          </label>
          <Input
            value={memberIds}
            onChange={(e) => setMemberIds(e.target.value)}
            placeholder="user1-id, user2-id, user3-id..."
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500 mt-1">
            Ví dụ: 123e4567-e89b-12d3-a456-426614174000, 987fcdeb-51a2-43d1-b789-123456789abc
          </p>
        </div>

        <div className="text-sm text-gray-600">
          <p><strong>Current User ID:</strong> {currentUser?.id || "Not logged in"}</p>
        </div>

        <Button
          onClick={handleTestCreateGroup}
          disabled={isLoading || !groupName.trim() || !memberIds.trim()}
          className="w-full"
        >
          {isLoading ? (
            <>
              <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Đang tạo...
            </>
          ) : (
            "Tạo nhóm test"
          )}
        </Button>
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded">
        <h3 className="font-medium mb-2">Hướng dẫn test:</h3>
        <ol className="text-sm text-gray-600 space-y-1">
          <li>1. Nhập tên nhóm</li>
          <li>2. Nhập ID của các thành viên (cách nhau bằng dấu phẩy)</li>
          <li>3. Mở console để xem logs</li>
          <li>4. Kiểm tra socket events trong Network tab</li>
          <li>5. Xác nhận tất cả thành viên nhận được events</li>
        </ol>
      </div>
    </div>
  );
}
