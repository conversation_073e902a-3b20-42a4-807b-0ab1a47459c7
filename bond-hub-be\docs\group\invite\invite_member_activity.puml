@startuml Mời thành viên vào nhóm - Activity Diagram
title Mời thành viên vào nhóm - Activity Diagram

|User|
start
:<PERSON>ọ<PERSON> nhóm;
:<PERSON><PERSON><PERSON> chức năng thêm thành viên;
:<PERSON><PERSON><PERSON> kiếm người dùng;
:<PERSON><PERSON><PERSON> người dùng để thêm vào nhóm;
:<PERSON><PERSON><PERSON> yêu cầu thêm thành viên;

|System|
:Kiểm tra quyền của người dùng;

if (<PERSON><PERSON> quyền thêm thành viên?) then (Có)
  :Kiểm tra người dùng đã là thành viên chưa;
  
  if (<PERSON><PERSON> là thành viên?) then (Có)
    :Tr<PERSON> về lỗi "Người dùng đã là thành viên của nhóm";
  else (Không)
    :Thêm người dùng vào nhóm với vai trò MEMBER;
    :<PERSON><PERSON><PERSON> thông tin vào cơ sở dữ liệu;
    :<PERSON><PERSON><PERSON> thông báo cho người dùng được thêm qua WebSocket;
    :<PERSON><PERSON><PERSON> về thông tin thành viên mới;
  endif
else (Không)
  :Trả về lỗi "Không có quyền thêm thành viên";
endif

|User|
if (Thêm thành viên thành công?) then (Có)
  :Xem thông báo thêm thành viên thành công;
  :Xem danh sách thành viên đã cập nhật;
else (Không)
  :Hiển thị thông báo lỗi;
endif

stop
@enduml
