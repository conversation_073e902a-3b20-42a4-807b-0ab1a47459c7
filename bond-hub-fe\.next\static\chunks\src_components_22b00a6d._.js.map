{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/QRCodeDialog.tsx"], "sourcesContent": ["\"use client\";\r\nimport { <PERSON>alog, DialogContent, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { X, Download } from \"lucide-react\";\r\nimport { VisuallyHidden } from \"@radix-ui/react-visually-hidden\";\r\nimport { QRCodeSVG } from \"qrcode.react\";\r\nimport { useRef } from \"react\";\r\n\r\ninterface QRCodeDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  userId: string;\r\n}\r\n\r\nexport default function QRCodeDialog({\r\n  isOpen,\r\n  onClose,\r\n  userId,\r\n}: QRCodeDialogProps) {\r\n  const qrCodeValue = `friend-${userId}`;\r\n  const qrRef = useRef<SVGSVGElement>(null);\r\n\r\n  // Hàm tải xuống mã QR dưới dạng hình ảnh PNG\r\n  const downloadQRCode = () => {\r\n    if (!qrRef.current) return;\r\n\r\n    try {\r\n      // Tạo một canvas từ SVG\r\n      const svg = qrRef.current;\r\n      const canvas = document.createElement(\"canvas\");\r\n      const ctx = canvas.getContext(\"2d\");\r\n      const svgData = new XMLSerializer().serializeToString(svg);\r\n      const img = new Image();\r\n\r\n      // Thiết lập kích thước canvas\r\n      canvas.width = 300;\r\n      canvas.height = 300;\r\n\r\n      // Tạo Blob từ SVG\r\n      const svgBlob = new Blob([svgData], {\r\n        type: \"image/svg+xml;charset=utf-8\",\r\n      });\r\n      const url = URL.createObjectURL(svgBlob);\r\n\r\n      img.onload = () => {\r\n        if (!ctx) return;\r\n\r\n        // Vẽ hình ảnh lên canvas\r\n        ctx.fillStyle = \"white\";\r\n        ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\r\n        URL.revokeObjectURL(url);\r\n\r\n        // Chuyển đổi canvas thành URL và tải xuống\r\n        canvas.toBlob((blob) => {\r\n          if (!blob) return;\r\n\r\n          const downloadUrl = URL.createObjectURL(blob);\r\n          const a = document.createElement(\"a\");\r\n          a.href = downloadUrl;\r\n          a.download = `qrcode-${userId}.png`;\r\n          document.body.appendChild(a);\r\n          a.click();\r\n          document.body.removeChild(a);\r\n          URL.revokeObjectURL(downloadUrl);\r\n        }, \"image/png\");\r\n      };\r\n\r\n      img.src = url;\r\n    } catch (error) {\r\n      console.error(\"Error downloading QR code:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <DialogContent className=\"max-w-[400px] p-0 overflow-hidden bg-white border-none\">\r\n        <VisuallyHidden>\r\n          <DialogTitle>Mã QR kết bạn</DialogTitle>\r\n        </VisuallyHidden>\r\n        <div className=\"relative w-full flex flex-col items-center justify-center p-6\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"absolute top-2 right-2 z-10\"\r\n            onClick={onClose}\r\n          >\r\n            <X className=\"h-5 w-5\" />\r\n          </Button>\r\n\r\n          <h2 className=\"text-xl font-semibold mb-4\">Mã QR kết bạn</h2>\r\n          <p className=\"text-sm text-gray-500 mb-6 text-center\">\r\n            Chia sẻ mã QR này để người khác có thể quét và kết bạn với bạn\r\n          </p>\r\n\r\n          <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4\">\r\n            <div className=\"w-[250px] h-[250px] flex items-center justify-center\">\r\n              <QRCodeSVG\r\n                value={qrCodeValue}\r\n                size={250}\r\n                level=\"L\"\r\n                includeMargin={true}\r\n                ref={qrRef}\r\n                bgColor=\"#FFFFFF\"\r\n                fgColor=\"#000000\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={downloadQRCode}\r\n            className=\"mb-4 flex items-center gap-2\"\r\n          >\r\n            <Download className=\"h-4 w-4\" />\r\n            Tải mã QR\r\n          </Button>\r\n\r\n          <div className=\"text-sm text-center font-medium text-gray-700 mt-2\">\r\n            Quét mã để kết bạn\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;;AAce,SAAS,aAAa,EACnC,MAAM,EACN,OAAO,EACP,MAAM,EACY;;IAClB,MAAM,cAAc,CAAC,OAAO,EAAE,QAAQ;IACtC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAEpC,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,IAAI;YACF,wBAAwB;YACxB,MAAM,MAAM,MAAM,OAAO;YACzB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM,UAAU,IAAI,gBAAgB,iBAAiB,CAAC;YACtD,MAAM,MAAM,IAAI;YAEhB,8BAA8B;YAC9B,OAAO,KAAK,GAAG;YACf,OAAO,MAAM,GAAG;YAEhB,kBAAkB;YAClB,MAAM,UAAU,IAAI,KAAK;gBAAC;aAAQ,EAAE;gBAClC,MAAM;YACR;YACA,MAAM,MAAM,IAAI,eAAe,CAAC;YAEhC,IAAI,MAAM,GAAG;gBACX,IAAI,CAAC,KAAK;gBAEV,yBAAyB;gBACzB,IAAI,SAAS,GAAG;gBAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;gBAC9C,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;gBACpD,IAAI,eAAe,CAAC;gBAEpB,2CAA2C;gBAC3C,OAAO,MAAM,CAAC,CAAC;oBACb,IAAI,CAAC,MAAM;oBAEX,MAAM,cAAc,IAAI,eAAe,CAAC;oBACxC,MAAM,IAAI,SAAS,aAAa,CAAC;oBACjC,EAAE,IAAI,GAAG;oBACT,EAAE,QAAQ,GAAG,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;oBACnC,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,EAAE,KAAK;oBACP,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,IAAI,eAAe,CAAC;gBACtB,GAAG;YACL;YAEA,IAAI,GAAG,GAAG;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,iLAAA,CAAA,iBAAc;8BACb,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAEf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAyC;;;;;;sCAItD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,MAAM;oCACN,OAAM;oCACN,eAAe;oCACf,KAAK;oCACL,SAAQ;oCACR,SAAQ;;;;;;;;;;;;;;;;sCAKd,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAIlC,6LAAC;4BAAI,WAAU;sCAAqD;;;;;;;;;;;;;;;;;;;;;;;AAO9E;GA/GwB;KAAA", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\";\r\nimport { Check } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn(\"flex items-center justify-center text-current\")}\r\n    >\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n));\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName;\r\n\r\nexport { Checkbox };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sQACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n));\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName;\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" &&\r\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\r\n      orientation === \"horizontal\" &&\r\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n));\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName;\r\n\r\nexport { ScrollArea, ScrollBar };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Tabs = TabsPrimitive.Root;\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsList.displayName = TabsPrimitive.List.displayName;\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsContent.displayName = TabsPrimitive.Content.displayName;\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Popover = PopoverPrimitive.Root;\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger;\r\n\r\nconst PopoverAnchor = PopoverPrimitive.Anchor;\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      ref={ref}\r\n      align={align}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n));\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName;\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,gBAAgB,sKAAA,CAAA,SAAuB;AAE7C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,geACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref,\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  ),\r\n);\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName;\r\n\r\nexport { Separator };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,wKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 499, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\";\r\n\r\nconst Collapsible = CollapsiblePrimitive.Root;\r\n\r\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger;\r\n\r\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent;\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent };\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;AAIA,MAAM,cAAc,0KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,0KAAA,CAAA,qBAAuC;AAElE,MAAM,qBAAqB,0KAAA,CAAA,qBAAuC", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/media/MediaViewer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback, useMemo } from \"react\";\r\nimport { <PERSON><PERSON>, DialogContent, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Media, User } from \"@/types/base\";\r\nimport Image from \"next/image\";\r\nimport { ChevronUp, ChevronDown } from \"lucide-react\";\r\nimport { VisuallyHidden } from \"@radix-ui/react-visually-hidden\";\r\nimport { getUserDataById } from \"@/actions/user.action\";\r\n\r\ninterface MediaViewerProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  media: (Media & {\r\n    createdAt?: Date;\r\n    sender?: {\r\n      id: string;\r\n      userInfo?: {\r\n        fullName?: string;\r\n        profilePictureUrl?: string;\r\n      };\r\n    };\r\n    senderId?: string;\r\n  })[];\r\n  initialIndex?: number;\r\n  chatName?: string;\r\n}\r\n\r\nexport default function MediaViewer({\r\n  isOpen,\r\n  onClose,\r\n  media,\r\n  initialIndex = 0,\r\n  chatName = \"Cloud của tôi\",\r\n}: MediaViewerProps) {\r\n  const [currentIndex, setCurrentIndex] = useState(initialIndex);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  // Group media by date\r\n  const mediaByDate = useMemo(() => {\r\n    const groups: { [key: string]: (Media & { createdAt?: Date })[] } = {};\r\n\r\n    media.forEach((item) => {\r\n      if (!item.createdAt) return;\r\n\r\n      const date = new Date(item.createdAt);\r\n      const dateKey = `${date.getDate()}/${date.getMonth() + 1}`;\r\n\r\n      if (!groups[dateKey]) {\r\n        groups[dateKey] = [];\r\n      }\r\n\r\n      groups[dateKey].push(item);\r\n    });\r\n\r\n    return Object.entries(groups).map(([date, items]) => ({\r\n      date,\r\n      items,\r\n    }));\r\n  }, [media]);\r\n\r\n  // Reset current index when media array changes\r\n  useEffect(() => {\r\n    setCurrentIndex(initialIndex);\r\n\r\n    // Log media for debugging\r\n    console.log(\r\n      \"MediaViewer media:\",\r\n      media.map((m) => ({\r\n        fileId: m.fileId,\r\n        fileName: m.fileName,\r\n        type: m.type,\r\n        extension: m.metadata?.extension,\r\n        isVideo: isVideoMedia(m),\r\n      })),\r\n    );\r\n    console.log(\"Initial index:\", initialIndex);\r\n  }, [media, initialIndex]);\r\n\r\n  // Reset loading state when current index changes\r\n  useEffect(() => {\r\n    setIsLoading(true);\r\n    console.log(`Current index changed to ${currentIndex}`);\r\n    if (media[currentIndex]) {\r\n      console.log(\"Media at current index:\", {\r\n        fileId: media[currentIndex].fileId,\r\n        type: media[currentIndex].type,\r\n        extension: media[currentIndex].metadata?.extension,\r\n      });\r\n    }\r\n  }, [currentIndex, media]);\r\n\r\n  // Helper function to determine if a media is a video\r\n  const isVideoMedia = (mediaItem: Media | undefined): boolean => {\r\n    if (!mediaItem) return false;\r\n\r\n    // Log the media item for debugging\r\n    console.log(\"Checking if media is video:\", {\r\n      fileId: mediaItem.fileId,\r\n      type: mediaItem.type,\r\n      extension: mediaItem.metadata?.extension,\r\n    });\r\n\r\n    // Check if type is explicitly VIDEO\r\n    if (mediaItem.type === \"VIDEO\") {\r\n      console.log(\"Media is video by type\");\r\n      return true;\r\n    }\r\n\r\n    // Check if extension is a video extension and type is not explicitly IMAGE\r\n    if (\r\n      mediaItem.metadata?.extension?.match(/mp4|webm|mov/i) &&\r\n      mediaItem.type !== \"IMAGE\"\r\n    ) {\r\n      console.log(\"Media is video by extension\");\r\n      return true;\r\n    }\r\n\r\n    console.log(\"Media is not a video\");\r\n    return false;\r\n  };\r\n\r\n  const currentMedia = media[currentIndex];\r\n  const isVideo = isVideoMedia(currentMedia);\r\n\r\n  // Log current media for debugging\r\n  useEffect(() => {\r\n    if (currentMedia) {\r\n      console.log(\"Current media in viewer:\", {\r\n        fileId: currentMedia.fileId,\r\n        fileName: currentMedia.fileName,\r\n        type: currentMedia.type,\r\n        extension: currentMedia.metadata?.extension,\r\n        isVideo,\r\n      });\r\n\r\n      // Force update type if needed\r\n      if (isVideo && currentMedia.type !== \"VIDEO\") {\r\n        console.log(\"Forcing video type update\");\r\n        // This is a hack to ensure the video is treated as a video\r\n        // We can't directly modify currentMedia because it's from props\r\n        // But we can log this for debugging\r\n      }\r\n    }\r\n  }, [currentMedia, isVideo]);\r\n\r\n  // State để lưu thông tin người gửi\r\n  const [senderInfo, setSenderInfo] = useState<User | null>(null);\r\n\r\n  // Lấy thông tin người gửi\r\n  useEffect(() => {\r\n    if (!currentMedia) return;\r\n\r\n    console.log(\"Current Media:\", currentMedia);\r\n\r\n    // Nếu đã có thông tin người gửi đầy đủ trong currentMedia.sender\r\n    if (currentMedia.sender?.userInfo?.fullName) {\r\n      console.log(\"Using sender info from media:\", currentMedia.sender);\r\n      setSenderInfo(currentMedia.sender as User);\r\n      return;\r\n    }\r\n\r\n    // Nếu có sender.id hoặc senderId, gọi API để lấy thông tin\r\n    const senderId = currentMedia.sender?.id || currentMedia.senderId;\r\n    if (senderId) {\r\n      console.log(\"Fetching sender info for ID:\", senderId);\r\n\r\n      const fetchSenderInfo = async () => {\r\n        try {\r\n          const result = await getUserDataById(senderId);\r\n          if (result.success && result.user) {\r\n            console.log(\"Sender info fetched:\", result.user);\r\n            setSenderInfo(result.user);\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching sender info:\", error);\r\n        }\r\n      };\r\n\r\n      fetchSenderInfo();\r\n    } else {\r\n      console.log(\"No sender ID available\");\r\n      // Trong tin nhắn riêng, người gửi có thể là người đối diện (chatName)\r\n      // Nhưng chúng ta không có đủ thông tin để hiển thị\r\n    }\r\n  }, [currentMedia]);\r\n\r\n  // Define navigation functions\r\n  const handlePrevious = useCallback(() => {\r\n    let newIndex;\r\n    if (currentIndex > 0) {\r\n      newIndex = currentIndex - 1;\r\n    } else {\r\n      // Loop to the end if at the beginning\r\n      newIndex = media.length - 1;\r\n    }\r\n    console.log(`Navigating to previous: ${currentIndex} -> ${newIndex}`);\r\n    console.log(\"Media at new index:\", media[newIndex]);\r\n    setCurrentIndex(newIndex);\r\n  }, [currentIndex, media]);\r\n\r\n  const handleNext = useCallback(() => {\r\n    let newIndex;\r\n    if (currentIndex < media.length - 1) {\r\n      newIndex = currentIndex + 1;\r\n    } else {\r\n      // Loop to the beginning if at the end\r\n      newIndex = 0;\r\n    }\r\n    console.log(`Navigating to next: ${currentIndex} -> ${newIndex}`);\r\n    console.log(\"Media at new index:\", media[newIndex]);\r\n    setCurrentIndex(newIndex);\r\n  }, [currentIndex, media]);\r\n\r\n  // Handle keyboard navigation\r\n  useEffect(() => {\r\n    const handleKeyDown = (e: KeyboardEvent) => {\r\n      if (!isOpen) return;\r\n\r\n      switch (e.key) {\r\n        case \"ArrowLeft\":\r\n          handlePrevious();\r\n          break;\r\n        case \"ArrowRight\":\r\n          handleNext();\r\n          break;\r\n        case \"Escape\":\r\n          onClose();\r\n          break;\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [isOpen, handlePrevious, handleNext, onClose]);\r\n\r\n  // Function to handle media download\r\n  const handleDownload = async () => {\r\n    try {\r\n      // Fetch the media\r\n      const response = await fetch(currentMedia.url);\r\n      const blob = await response.blob();\r\n\r\n      // Create a download link\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement(\"a\");\r\n      link.href = url;\r\n      link.download =\r\n        currentMedia.fileName || `file.${currentMedia.metadata.extension}`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n    } catch (error) {\r\n      console.error(\"Error downloading media:\", error);\r\n    }\r\n  };\r\n\r\n  if (!currentMedia) return null;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <DialogContent className=\"max-w-[100vw] max-h-[100vh] w-screen h-screen p-0 overflow-hidden bg-black border-none flex flex-col !rounded-none\">\r\n        <DialogTitle>\r\n          {/* Top bar */}\r\n          <div className=\"h-10 bg-[#333333] flex items-center justify-center\">\r\n            <div className=\"text-white font-semibold\">{chatName}</div>\r\n          </div>\r\n          <VisuallyHidden>Xem hình ảnh/video</VisuallyHidden>\r\n        </DialogTitle>\r\n\r\n        <div className=\"flex flex-col -mt-4 space-y-0 flex-1\">\r\n          <div className=\"flex flex-row justify-between flex-1\">\r\n            <div className=\"flex flex-row relative flex-1\">\r\n              {/* Main content area */}\r\n              <div className=\"flex flex-1 items-center justify-between flex-1 relative py-5\">\r\n                {isVideo ? (\r\n                  <div className=\"flex items-center justify-center w-full\">\r\n                    <div className=\"flex items-center justify-center w-full\">\r\n                      <video\r\n                        key={`main-video-${currentMedia.fileId}`}\r\n                        src={currentMedia.url}\r\n                        controls\r\n                        autoPlay\r\n                        className=\"max-h-[calc(70vh-20px)] max-w-[calc(100vw-120px)] object-contain\"\r\n                        onLoadStart={() => {\r\n                          console.log(\"Video loading started\");\r\n                          setIsLoading(true);\r\n                        }}\r\n                        onLoadedData={() => {\r\n                          console.log(\"Video loaded\");\r\n                          setIsLoading(false);\r\n                        }}\r\n                        onError={(e) => {\r\n                          console.error(\"Video error:\", e);\r\n                          setIsLoading(false);\r\n                        }}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"flex items-center justify-center w-full\">\r\n                    <div className=\"flex items-center justify-center w-full\">\r\n                      <Image\r\n                        src={currentMedia.url}\r\n                        alt={currentMedia.fileName || \"Media\"}\r\n                        width={1200}\r\n                        height={800}\r\n                        className={`object-contain max-h-[70vh] max-w-[calc(100vw-120px)] ${\r\n                          isLoading ? \"opacity-0\" : \"opacity-100\"\r\n                        }`}\r\n                        onLoad={() => setIsLoading(false)}\r\n                        unoptimized\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              {/* Navigation buttons */}\r\n              <div className=\"flex flex-col items-center justify-center space-y-1 mr-3 absolute right-0 top-1/2 -translate-y-1/2\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"h-8 w-8 rounded-full bg-black/50 text-white hover:bg-black/70\"\r\n                  onClick={handlePrevious}\r\n                >\r\n                  <ChevronUp className=\"h-5 w-5\" />\r\n                </Button>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"h-8 w-8 rounded-full bg-black/50 text-white hover:bg-black/70\"\r\n                  onClick={handleNext}\r\n                >\r\n                  <ChevronDown className=\"h-5 w-5\" />\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Sidebar with thumbnails */}\r\n            <div className=\"w-[120px] bg-[#212121] border-l border-gray-800 flex flex-col items-center overflow-hidden h-full\">\r\n              {/* Date header */}\r\n              <div className=\"p-1 border-b border-gray-800 text-white w-full\">\r\n                <div className=\"text-xs text-gray-400 text-center\">14/04</div>\r\n              </div>\r\n\r\n              {/* Thumbnails navigation */}\r\n              <div\r\n                className=\"overflow-y-auto items-center h-[calc(100vh-130px)] w-full hide-scrollbar\"\r\n                style={{ scrollbarWidth: \"none\", msOverflowStyle: \"none\" }}\r\n              >\r\n                <style jsx>{`\r\n                  .hide-scrollbar::-webkit-scrollbar {\r\n                    display: none;\r\n                  }\r\n                `}</style>\r\n                {mediaByDate.map((group, groupIndex) => (\r\n                  <div key={groupIndex} className=\"mb-2\">\r\n                    <div className=\"space-y-2 px-1\">\r\n                      {group.items.map((item) => {\r\n                        // Find the index of this item in the full media array\r\n                        const mediaIndex = media.findIndex(\r\n                          (m) => m.fileId === item.fileId,\r\n                        );\r\n                        const isActive = mediaIndex === currentIndex;\r\n\r\n                        return (\r\n                          <div\r\n                            key={item.fileId}\r\n                            className={`relative justidfy-center m-2 items-center cursor-pointer overflow-hidden rounded-md border border-gray-500 ${isActive ? \"border-2 \" : \"\"}`}\r\n                            onClick={() =>\r\n                              mediaIndex >= 0 && setCurrentIndex(mediaIndex)\r\n                            }\r\n                          >\r\n                            <div className=\"aspect-square w-[90px] h-[90px] relative\">\r\n                              {isVideoMedia(item) ? (\r\n                                <>\r\n                                  <video\r\n                                    key={`video-${item.fileId}`}\r\n                                    src={item.url}\r\n                                    className=\"w-full h-full object-cover\"\r\n                                    muted\r\n                                    preload=\"metadata\"\r\n                                    onError={(e) => {\r\n                                      console.error(\r\n                                        \"Thumbnail video error:\",\r\n                                        e,\r\n                                      );\r\n                                    }}\r\n                                  />\r\n                                  <div className=\"absolute inset-0 flex items-center justify-center bg-black/30\">\r\n                                    <svg\r\n                                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                                      width=\"20\"\r\n                                      height=\"20\"\r\n                                      viewBox=\"0 0 24 24\"\r\n                                      fill=\"none\"\r\n                                      stroke=\"white\"\r\n                                      strokeWidth=\"2\"\r\n                                      strokeLinecap=\"round\"\r\n                                      strokeLinejoin=\"round\"\r\n                                    >\r\n                                      <polygon points=\"5 3 19 12 5 21 5 3\"></polygon>\r\n                                    </svg>\r\n                                  </div>\r\n                                </>\r\n                              ) : (\r\n                                <Image\r\n                                  src={item.url}\r\n                                  alt={item.fileName || \"Thumbnail\"}\r\n                                  width={50}\r\n                                  height={50}\r\n                                  className=\"w-full h-full object-cover\"\r\n                                  unoptimized\r\n                                />\r\n                              )}\r\n                            </div>\r\n                          </div>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Bottom controls */}\r\n          <div className=\"bg-[#1a1a1a] text-white h-[40px] flex items-center justify-between px-4 mt-auto border-t border-gray-800\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-6 h-6 rounded-full overflow-hidden bg-gray-700 flex items-center justify-center\">\r\n                  {senderInfo?.userInfo?.profilePictureUrl ? (\r\n                    <Image\r\n                      src={senderInfo.userInfo.profilePictureUrl}\r\n                      alt={senderInfo.userInfo?.fullName || \"User\"}\r\n                      width={24}\r\n                      height={24}\r\n                      className=\"w-full h-full object-cover\"\r\n                      unoptimized\r\n                    />\r\n                  ) : (\r\n                    <span className=\"text-xs text-white\">\r\n                      {senderInfo?.userInfo?.fullName?.charAt(0) || \"U\"}\r\n                    </span>\r\n                  )}\r\n                </div>\r\n                <div className=\"text-xs text-gray-300 flex items-center\">\r\n                  <span>{senderInfo?.userInfo?.fullName || \"Như Tâm\"}</span>\r\n                  <span className=\"mx-2 text-gray-500\">|</span>\r\n                  <span className=\"text-gray-400\">\r\n                    {currentMedia.createdAt\r\n                      ? new Date(currentMedia.createdAt).toLocaleTimeString(\r\n                          \"vi-VN\",\r\n                          {\r\n                            hour: \"2-digit\",\r\n                            minute: \"2-digit\",\r\n                          },\r\n                        ) +\r\n                        \" \" +\r\n                        new Date(currentMedia.createdAt).toLocaleDateString(\r\n                          \"vi-VN\",\r\n                          {\r\n                            day: \"2-digit\",\r\n                            month: \"2-digit\",\r\n                            year: \"numeric\",\r\n                          },\r\n                        )\r\n                      : \"\"}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"h-8 w-8 text-white hover:bg-gray-800 rounded-full\"\r\n                onClick={handleDownload}\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  width=\"18\"\r\n                  height=\"18\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                >\r\n                  <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path>\r\n                  <polyline points=\"7 10 12 15 17 10\"></polyline>\r\n                  <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\"></line>\r\n                </svg>\r\n              </Button>\r\n              <div className=\"h-5 w-[1px] bg-gray-700\"></div>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"h-8 w-8 text-white hover:bg-gray-800 rounded-full\"\r\n                onClick={() => {}}\r\n                title=\"Tính năng đang phát triển\"\r\n                disabled\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  width=\"18\"\r\n                  height=\"18\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                >\r\n                  <path d=\"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\"></path>\r\n                  <polyline points=\"16 6 12 2 8 6\"></polyline>\r\n                  <line x1=\"12\" y1=\"2\" x2=\"12\" y2=\"15\"></line>\r\n                </svg>\r\n              </Button>\r\n              <div className=\"h-5 w-[1px] bg-gray-700\"></div>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"h-8 w-8 text-white hover:bg-gray-800 rounded-full\"\r\n                onClick={() => {}}\r\n                title=\"Tính năng đang phát triển\"\r\n                disabled\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  width=\"18\"\r\n                  height=\"18\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                >\r\n                  <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n                  <circle cx=\"12\" cy=\"12\" r=\"3\"></circle>\r\n                </svg>\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;;;AATA;;;;;;;;;AA6Be,SAAS,YAAY,EAClC,MAAM,EACN,OAAO,EACP,KAAK,EACL,eAAe,CAAC,EAChB,WAAW,eAAe,EACT;;IACjB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sBAAsB;IACtB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YAC1B,MAAM,SAA8D,CAAC;YAErE,MAAM,OAAO;oDAAC,CAAC;oBACb,IAAI,CAAC,KAAK,SAAS,EAAE;oBAErB,MAAM,OAAO,IAAI,KAAK,KAAK,SAAS;oBACpC,MAAM,UAAU,GAAG,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,QAAQ,KAAK,GAAG;oBAE1D,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;wBACpB,MAAM,CAAC,QAAQ,GAAG,EAAE;oBACtB;oBAEA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACvB;;YAEA,OAAO,OAAO,OAAO,CAAC,QAAQ,GAAG;oDAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;wBACpD;wBACA;oBACF,CAAC;;QACH;2CAAG;QAAC;KAAM;IAEV,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,gBAAgB;YAEhB,0BAA0B;YAC1B,QAAQ,GAAG,CACT,sBACA,MAAM,GAAG;yCAAC,CAAC,IAAM,CAAC;wBAChB,QAAQ,EAAE,MAAM;wBAChB,UAAU,EAAE,QAAQ;wBACpB,MAAM,EAAE,IAAI;wBACZ,WAAW,EAAE,QAAQ,EAAE;wBACvB,SAAS,aAAa;oBACxB,CAAC;;YAEH,QAAQ,GAAG,CAAC,kBAAkB;QAChC;gCAAG;QAAC;QAAO;KAAa;IAExB,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;YACb,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,cAAc;YACtD,IAAI,KAAK,CAAC,aAAa,EAAE;gBACvB,QAAQ,GAAG,CAAC,2BAA2B;oBACrC,QAAQ,KAAK,CAAC,aAAa,CAAC,MAAM;oBAClC,MAAM,KAAK,CAAC,aAAa,CAAC,IAAI;oBAC9B,WAAW,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE;gBAC3C;YACF;QACF;gCAAG;QAAC;QAAc;KAAM;IAExB,qDAAqD;IACrD,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,WAAW,OAAO;QAEvB,mCAAmC;QACnC,QAAQ,GAAG,CAAC,+BAA+B;YACzC,QAAQ,UAAU,MAAM;YACxB,MAAM,UAAU,IAAI;YACpB,WAAW,UAAU,QAAQ,EAAE;QACjC;QAEA,oCAAoC;QACpC,IAAI,UAAU,IAAI,KAAK,SAAS;YAC9B,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,2EAA2E;QAC3E,IACE,UAAU,QAAQ,EAAE,WAAW,MAAM,oBACrC,UAAU,IAAI,KAAK,SACnB;YACA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,MAAM,eAAe,KAAK,CAAC,aAAa;IACxC,MAAM,UAAU,aAAa;IAE7B,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,cAAc;gBAChB,QAAQ,GAAG,CAAC,4BAA4B;oBACtC,QAAQ,aAAa,MAAM;oBAC3B,UAAU,aAAa,QAAQ;oBAC/B,MAAM,aAAa,IAAI;oBACvB,WAAW,aAAa,QAAQ,EAAE;oBAClC;gBACF;gBAEA,8BAA8B;gBAC9B,IAAI,WAAW,aAAa,IAAI,KAAK,SAAS;oBAC5C,QAAQ,GAAG,CAAC;gBACZ,2DAA2D;gBAC3D,gEAAgE;gBAChE,oCAAoC;gBACtC;YACF;QACF;gCAAG;QAAC;QAAc;KAAQ;IAE1B,mCAAmC;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE1D,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,cAAc;YAEnB,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,iEAAiE;YACjE,IAAI,aAAa,MAAM,EAAE,UAAU,UAAU;gBAC3C,QAAQ,GAAG,CAAC,iCAAiC,aAAa,MAAM;gBAChE,cAAc,aAAa,MAAM;gBACjC;YACF;YAEA,2DAA2D;YAC3D,MAAM,WAAW,aAAa,MAAM,EAAE,MAAM,aAAa,QAAQ;YACjE,IAAI,UAAU;gBACZ,QAAQ,GAAG,CAAC,gCAAgC;gBAE5C,MAAM;6DAAkB;wBACtB,IAAI;4BACF,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE;4BACrC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gCACjC,QAAQ,GAAG,CAAC,wBAAwB,OAAO,IAAI;gCAC/C,cAAc,OAAO,IAAI;4BAC3B;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,+BAA+B;wBAC/C;oBACF;;gBAEA;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACZ,sEAAsE;YACtE,mDAAmD;YACrD;QACF;gCAAG;QAAC;KAAa;IAEjB,8BAA8B;IAC9B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YACjC,IAAI;YACJ,IAAI,eAAe,GAAG;gBACpB,WAAW,eAAe;YAC5B,OAAO;gBACL,sCAAsC;gBACtC,WAAW,MAAM,MAAM,GAAG;YAC5B;YACA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,aAAa,IAAI,EAAE,UAAU;YACpE,QAAQ,GAAG,CAAC,uBAAuB,KAAK,CAAC,SAAS;YAClD,gBAAgB;QAClB;kDAAG;QAAC;QAAc;KAAM;IAExB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC7B,IAAI;YACJ,IAAI,eAAe,MAAM,MAAM,GAAG,GAAG;gBACnC,WAAW,eAAe;YAC5B,OAAO;gBACL,sCAAsC;gBACtC,WAAW;YACb;YACA,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,aAAa,IAAI,EAAE,UAAU;YAChE,QAAQ,GAAG,CAAC,uBAAuB,KAAK,CAAC,SAAS;YAClD,gBAAgB;QAClB;8CAAG;QAAC;QAAc;KAAM;IAExB,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;uDAAgB,CAAC;oBACrB,IAAI,CAAC,QAAQ;oBAEb,OAAQ,EAAE,GAAG;wBACX,KAAK;4BACH;4BACA;wBACF,KAAK;4BACH;4BACA;wBACF,KAAK;4BACH;4BACA;oBACJ;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;gCAAG;QAAC;QAAQ;QAAgB;QAAY;KAAQ;IAEhD,oCAAoC;IACpC,MAAM,iBAAiB;QACrB,IAAI;YACF,kBAAkB;YAClB,MAAM,WAAW,MAAM,MAAM,aAAa,GAAG;YAC7C,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,yBAAyB;YACzB,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GACX,aAAa,QAAQ,IAAI,CAAC,KAAK,EAAE,aAAa,QAAQ,CAAC,SAAS,EAAE;YACpE,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,cAAW;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAA4B;;;;;;;;;;;sCAE7C,6LAAC,iLAAA,CAAA,iBAAc;sCAAC;;;;;;;;;;;;8BAGlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACZ,wBACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAEC,KAAK,aAAa,GAAG;wDACrB,QAAQ;wDACR,QAAQ;wDACR,WAAU;wDACV,aAAa;4DACX,QAAQ,GAAG,CAAC;4DACZ,aAAa;wDACf;wDACA,cAAc;4DACZ,QAAQ,GAAG,CAAC;4DACZ,aAAa;wDACf;wDACA,SAAS,CAAC;4DACR,QAAQ,KAAK,CAAC,gBAAgB;4DAC9B,aAAa;wDACf;uDAhBK,CAAC,WAAW,EAAE,aAAa,MAAM,EAAE;;;;;;;;;;;;;;qEAqB9C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,aAAa,GAAG;wDACrB,KAAK,aAAa,QAAQ,IAAI;wDAC9B,OAAO;wDACP,QAAQ;wDACR,WAAW,CAAC,sDAAsD,EAChE,YAAY,cAAc,eAC1B;wDACF,QAAQ,IAAM,aAAa;wDAC3B,WAAW;;;;;;;;;;;;;;;;;;;;;sDAOrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAM7B,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DAAoC;;;;;;;;;;;sDAIrD,6LAAC;4CAEC,OAAO;gDAAE,gBAAgB;gDAAQ,iBAAiB;4CAAO;sFAD/C;;;;;;gDAQT,YAAY,GAAG,CAAC,CAAC,OAAO,2BACvB,6LAAC;kGAA+B;kEAC9B,cAAA,6LAAC;sGAAc;sEACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;gEAChB,sDAAsD;gEACtD,MAAM,aAAa,MAAM,SAAS,CAChC,CAAC,IAAM,EAAE,MAAM,KAAK,KAAK,MAAM;gEAEjC,MAAM,WAAW,eAAe;gEAEhC,qBACE,6LAAC;oEAGC,SAAS,IACP,cAAc,KAAK,gBAAgB;8GAF1B,CAAC,2GAA2G,EAAE,WAAW,cAAc,IAAI;8EAKtJ,cAAA,6LAAC;kHAAc;kFACZ,aAAa,sBACZ;;8FACE,6LAAC;oFAEC,KAAK,KAAK,GAAG;oFAEb,KAAK;oFACL,SAAQ;oFACR,SAAS,CAAC;wFACR,QAAQ,KAAK,CACX,0BACA;oFAEJ;8HARU;mFAFL,CAAC,MAAM,EAAE,KAAK,MAAM,EAAE;;;;;8FAY7B,6LAAC;8HAAc;8FACb,cAAA,6LAAC;wFACC,OAAM;wFACN,OAAM;wFACN,QAAO;wFACP,SAAQ;wFACR,MAAK;wFACL,QAAO;wFACP,aAAY;wFACZ,eAAc;wFACd,gBAAe;;kGAEf,cAAA,6LAAC;4FAAQ,QAAO;;;;;;;;;;;;;;;;;;yGAKtB,6LAAC,gIAAA,CAAA,UAAK;4EACJ,KAAK,KAAK,GAAG;4EACb,KAAK,KAAK,QAAQ,IAAI;4EACtB,OAAO;4EACP,QAAQ;4EACR,WAAU;4EACV,WAAW;;;;;;;;;;;mEA7CZ,KAAK,MAAM;;;;;4DAmDtB;;;;;;uDA9DM;;;;;;;;;;;;;;;;;;;;;;;sCAuElB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,YAAY,UAAU,kCACrB,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,WAAW,QAAQ,CAAC,iBAAiB;oDAC1C,KAAK,WAAW,QAAQ,EAAE,YAAY;oDACtC,OAAO;oDACP,QAAQ;oDACR,WAAU;oDACV,WAAW;;;;;yEAGb,6LAAC;oDAAK,WAAU;8DACb,YAAY,UAAU,UAAU,OAAO,MAAM;;;;;;;;;;;0DAIpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,YAAY,UAAU,YAAY;;;;;;kEACzC,6LAAC;wDAAK,WAAU;kEAAqB;;;;;;kEACrC,6LAAC;wDAAK,WAAU;kEACb,aAAa,SAAS,GACnB,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB,CACjD,SACA;4DACE,MAAM;4DACN,QAAQ;wDACV,KAEF,MACA,IAAI,KAAK,aAAa,SAAS,EAAE,kBAAkB,CACjD,SACA;4DACE,KAAK;4DACL,OAAO;4DACP,MAAM;wDACR,KAEF;;;;;;;;;;;;;;;;;;;;;;;8CAKZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;sDAET,cAAA,6LAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,QAAO;gDACP,aAAY;gDACZ,eAAc;gDACd,gBAAe;;kEAEf,6LAAC;wDAAK,GAAE;;;;;;kEACR,6LAAC;wDAAS,QAAO;;;;;;kEACjB,6LAAC;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;wDAAK,IAAG;;;;;;;;;;;;;;;;;sDAGrC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,KAAO;4CAChB,OAAM;4CACN,QAAQ;sDAER,cAAA,6LAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,QAAO;gDACP,aAAY;gDACZ,eAAc;gDACd,gBAAe;;kEAEf,6LAAC;wDAAK,GAAE;;;;;;kEACR,6LAAC;wDAAS,QAAO;;;;;;kEACjB,6LAAC;wDAAK,IAAG;wDAAK,IAAG;wDAAI,IAAG;wDAAK,IAAG;;;;;;;;;;;;;;;;;sDAGpC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,KAAO;4CAChB,OAAM;4CACN,QAAQ;sDAER,cAAA,6LAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,QAAO;gDACP,aAAY;gDACZ,eAAc;gDACd,gBAAe;;kEAEf,6LAAC;wDAAO,IAAG;wDAAK,IAAG;wDAAK,GAAE;;;;;;kEAC1B,6LAAC;wDAAO,IAAG;wDAAK,IAAG;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C;GA3gBwB;KAAA", "debugId": null}}, {"offset": {"line": 1381, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/GroupQRCodeDialog.tsx"], "sourcesContent": ["\"use client\";\r\nimport { <PERSON>alog, DialogContent, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { X, Download } from \"lucide-react\";\r\nimport { VisuallyHidden } from \"@radix-ui/react-visually-hidden\";\r\nimport { QRCodeSVG } from \"qrcode.react\";\r\nimport { useRef } from \"react\";\r\n\r\ninterface GroupQRCodeDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  groupId: string;\r\n  groupName?: string;\r\n}\r\n\r\nexport default function GroupQRCodeDialog({\r\n  isOpen,\r\n  onClose,\r\n  groupId,\r\n  groupName,\r\n}: GroupQRCodeDialogProps) {\r\n  const qrCodeValue = `group-${groupId}`;\r\n  const qrRef = useRef<SVGSVGElement>(null);\r\n\r\n  // Hàm tải xuống mã QR dưới dạng hình ảnh PNG\r\n  const downloadQRCode = () => {\r\n    if (!qrRef.current) return;\r\n\r\n    try {\r\n      // Tạo một canvas từ SVG\r\n      const svg = qrRef.current;\r\n      const canvas = document.createElement(\"canvas\");\r\n      const ctx = canvas.getContext(\"2d\");\r\n      const svgData = new XMLSerializer().serializeToString(svg);\r\n      const img = new Image();\r\n\r\n      // Thiết lập kích thước canvas\r\n      canvas.width = 300;\r\n      canvas.height = 300;\r\n\r\n      // Tạo Blob từ SVG\r\n      const svgBlob = new Blob([svgData], {\r\n        type: \"image/svg+xml;charset=utf-8\",\r\n      });\r\n      const url = URL.createObjectURL(svgBlob);\r\n\r\n      img.onload = () => {\r\n        if (!ctx) return;\r\n\r\n        // Vẽ hình ảnh lên canvas\r\n        ctx.fillStyle = \"white\";\r\n        ctx.fillRect(0, 0, canvas.width, canvas.height);\r\n        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\r\n        URL.revokeObjectURL(url);\r\n\r\n        // Chuyển đổi canvas thành URL và tải xuống\r\n        canvas.toBlob((blob) => {\r\n          if (!blob) return;\r\n\r\n          const downloadUrl = URL.createObjectURL(blob);\r\n          const a = document.createElement(\"a\");\r\n          a.href = downloadUrl;\r\n          a.download = `qrcode-group-${groupId}.png`;\r\n          document.body.appendChild(a);\r\n          a.click();\r\n          document.body.removeChild(a);\r\n          URL.revokeObjectURL(downloadUrl);\r\n        }, \"image/png\");\r\n      };\r\n\r\n      img.src = url;\r\n    } catch (error) {\r\n      console.error(\"Error downloading QR code:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <DialogContent className=\"max-w-[400px] p-0 overflow-hidden bg-white border-none\">\r\n        <VisuallyHidden>\r\n          <DialogTitle>Mã QR nhóm</DialogTitle>\r\n        </VisuallyHidden>\r\n        <div className=\"relative w-full flex flex-col items-center justify-center p-6\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"absolute top-2 right-2 z-10\"\r\n            onClick={onClose}\r\n          >\r\n            <X className=\"h-5 w-5\" />\r\n          </Button>\r\n\r\n          <h2 className=\"text-xl font-semibold mb-4\">Mã QR nhóm</h2>\r\n          {groupName && (\r\n            <p className=\"text-lg font-medium text-gray-800 mb-2\">\r\n              {groupName}\r\n            </p>\r\n          )}\r\n          <p className=\"text-sm text-gray-500 mb-6 text-center\">\r\n            Chia sẻ mã QR này để người khác có thể quét và tham gia nhóm\r\n          </p>\r\n\r\n          <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4\">\r\n            <div className=\"w-[250px] h-[250px] flex items-center justify-center\">\r\n              <QRCodeSVG\r\n                value={qrCodeValue}\r\n                size={250}\r\n                level=\"L\"\r\n                includeMargin={true}\r\n                ref={qrRef}\r\n                bgColor=\"#FFFFFF\"\r\n                fgColor=\"#000000\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={downloadQRCode}\r\n            className=\"mb-4 flex items-center gap-2\"\r\n          >\r\n            <Download className=\"h-4 w-4\" />\r\n            Tải mã QR\r\n          </Button>\r\n\r\n          <div className=\"text-sm text-center font-medium text-gray-700 mt-2\">\r\n            Quét mã để tham gia nhóm\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;;AAee,SAAS,kBAAkB,EACxC,MAAM,EACN,OAAO,EACP,OAAO,EACP,SAAS,EACc;;IACvB,MAAM,cAAc,CAAC,MAAM,EAAE,SAAS;IACtC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAEpC,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,IAAI;YACF,wBAAwB;YACxB,MAAM,MAAM,MAAM,OAAO;YACzB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM,UAAU,IAAI,gBAAgB,iBAAiB,CAAC;YACtD,MAAM,MAAM,IAAI;YAEhB,8BAA8B;YAC9B,OAAO,KAAK,GAAG;YACf,OAAO,MAAM,GAAG;YAEhB,kBAAkB;YAClB,MAAM,UAAU,IAAI,KAAK;gBAAC;aAAQ,EAAE;gBAClC,MAAM;YACR;YACA,MAAM,MAAM,IAAI,eAAe,CAAC;YAEhC,IAAI,MAAM,GAAG;gBACX,IAAI,CAAC,KAAK;gBAEV,yBAAyB;gBACzB,IAAI,SAAS,GAAG;gBAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;gBAC9C,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;gBACpD,IAAI,eAAe,CAAC;gBAEpB,2CAA2C;gBAC3C,OAAO,MAAM,CAAC,CAAC;oBACb,IAAI,CAAC,MAAM;oBAEX,MAAM,cAAc,IAAI,eAAe,CAAC;oBACxC,MAAM,IAAI,SAAS,aAAa,CAAC;oBACjC,EAAE,IAAI,GAAG;oBACT,EAAE,QAAQ,GAAG,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC;oBAC1C,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,EAAE,KAAK;oBACP,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,IAAI,eAAe,CAAC;gBACtB,GAAG;YACL;YAEA,IAAI,GAAG,GAAG;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,iLAAA,CAAA,iBAAc;8BACb,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAEf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;wBAC1C,2BACC,6LAAC;4BAAE,WAAU;sCACV;;;;;;sCAGL,6LAAC;4BAAE,WAAU;sCAAyC;;;;;;sCAItD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yJAAA,CAAA,YAAS;oCACR,OAAO;oCACP,MAAM;oCACN,OAAM;oCACN,eAAe;oCACf,KAAK;oCACL,SAAQ;oCACR,SAAQ;;;;;;;;;;;;;;;;sCAKd,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAIlC,6LAAC;4BAAI,WAAU;sCAAqD;;;;;;;;;;;;;;;;;;;;;;;AAO9E;GArHwB;KAAA", "debugId": null}}]}