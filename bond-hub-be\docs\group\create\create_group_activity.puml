@startuml Tạo nhóm - Activity Diagram
title Tạo nhóm - Activity Diagram

|User|
start
:<PERSON><PERSON><PERSON> tạo nhóm mới;
:<PERSON><PERSON><PERSON><PERSON> tên nhóm;
:Chọn ảnh đại diện nhóm (t<PERSON><PERSON> chọn);
:<PERSON><PERSON><PERSON> các thành viên ban đầu;
:<PERSON><PERSON><PERSON> yêu cầu tạo nhóm;

|System|
:Kiểm tra thông tin nhóm;

if (Thông tin hợp lệ?) then (Có)
  if (Có ảnh đại diện?) then (Có)
    :Kiểm tra định dạng ảnh;
    
    if (Định dạng hợp lệ?) then (Có)
      :T<PERSON>i ảnh lên hệ thống lưu trữ;
    else (<PERSON>hông)
      :Tr<PERSON> về lỗi "Định dạng ảnh không hợp lệ";
      stop
    endif
  endif
  
  :Tạo nhóm mới trong cơ sở dữ liệu;
  :Thê<PERSON> người tạo làm thành viên với vai trò LEADER;
  
  if (<PERSON><PERSON> thành viên ban đầu?) then (Có)
    :Thê<PERSON> các thành viên ban đầu với vai trò MEMBER;
    :Gửi thông báo cho các thành viên qua WebSocket;
  endif
  
  :Trả về thông tin nhóm đã tạo;
else (Không)
  :Trả về lỗi "Thông tin nhóm không hợp lệ";
endif

|User|
if (Tạo nhóm thành công?) then (Có)
  :Xem thông tin nhóm đã tạo;
else (Không)
  :Hiển thị thông báo lỗi;
endif

stop
@enduml
