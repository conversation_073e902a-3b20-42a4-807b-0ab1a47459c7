@startuml Rời nhóm - Activity Diagram
title Rời nhóm - Activity Diagram

|User|
start
:<PERSON><PERSON><PERSON> nhóm;
:<PERSON><PERSON><PERSON> chức năng rời nhóm;
:<PERSON><PERSON><PERSON> nhận rời nhóm;
:<PERSON><PERSON><PERSON> yêu cầu rời nhóm;

|System|
:Kiểm tra người dùng có phải thành viên của nhóm không;

if (<PERSON>à thành viên?) then (Có)
  :Kiểm tra vai trò của người dùng;
  
  if (Là trưởng nhóm?) then (Có)
    :<PERSON><PERSON><PERSON> về lỗi "Trưởng nhóm không thể rời nhóm. Hãy chuyển quyền trưởng nhóm trước.";
  else (Không)
    :<PERSON><PERSON><PERSON> thành viên khỏi nhóm;
    :<PERSON><PERSON><PERSON> thông báo cho các thành viên khác qua WebSocket;
    :<PERSON><PERSON><PERSON> về thông báo rời nhóm thành công;
  endif
else (Không)
  :Tr<PERSON> về lỗi "Bạn không phải thành viên của nhóm này";
endif

|User|
if (Rời nhóm thành công?) then (Có)
  :Xem thông báo rời nhóm thành công;
  :Chuyển đến danh sách nhóm;
else (Không)
  :Hiển thị thông báo lỗi;
endif

stop
@enduml
