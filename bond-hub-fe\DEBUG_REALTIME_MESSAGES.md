# Debug Real-Time Messages Issue

## Vấn đề
Khi đang mở chat group, tin nhắn mới từ thành viên khác không hiển thị dù socket đã được gửi.

## Debugging Steps Applied

### 1. Thêm Debug Logs

**File: `src/components/chat/ChatSocketHandler.tsx`**
- <PERSON>h<PERSON><PERSON> logs chi tiết trong `handleNewMessage` để track:
  - Message analysis (groupId, currentChatType, selectedGroup, etc.)
  - `isFromCurrentChat` logic
  - Message processing flow
  - ChatStore message count before/after

**File: `src/components/chat/ChatArea.tsx`**
- Thê<PERSON> logs trong useEffect để track:
  - Messages array changes
  - Last few messages content
  - Re-render triggers

### 2. Tạo Test Component

**File: `src/components/test/RealTimeMessageTest.tsx`**
- Monitor socket events real-time
- Test message sending via ChatStore và direct socket
- Display current chat state, socket status
- Show recent messages và user info

### 3. Debugging Checklist

#### A. Socket Connection
- [ ] Socket connected (`isConnected: true`)
- [ ] Socket ID available
- [ ] No connection errors in console

#### B. Message Reception
- [ ] `newMessage` event received in ChatSocketHandler
- [ ] Message analysis logs show correct data
- [ ] `isFromCurrentChat` evaluates to `true` for current group
- [ ] Message added to chatStore (`processNewMessage` called)

#### C. UI Updates
- [ ] ChatArea useEffect triggered when messages change
- [ ] Messages array updated in ChatArea
- [ ] Component re-renders with new messages

#### D. Group Chat State
- [ ] `currentChatType === "GROUP"`
- [ ] `selectedGroup.id` matches `message.groupId`
- [ ] User is member of the group
- [ ] Group exists in conversations list

### 4. Common Issues & Solutions

#### Issue 1: Socket Events Not Received
**Symptoms:** No logs in ChatSocketHandler
**Solutions:**
- Check socket connection status
- Verify user is in group room on backend
- Check message gateway events

#### Issue 2: `isFromCurrentChat` False
**Symptoms:** Message received but not added to current chat
**Solutions:**
- Verify `message.groupId` matches `selectedGroup.id`
- Check `currentChatType` is "GROUP"
- Ensure group is properly selected

#### Issue 3: Messages Added But UI Not Updated
**Symptoms:** ChatStore updated but ChatArea doesn't re-render
**Solutions:**
- Check ChatArea useEffect dependencies
- Verify messages array reference changes
- Check for React rendering issues

#### Issue 4: Group Room Not Joined
**Symptoms:** Messages sent but not received by other members
**Solutions:**
- Check backend message gateway logs
- Verify user joined group room after group creation
- Reconnect message socket if needed

### 5. Testing Procedure

1. **Setup Test Environment:**
   ```typescript
   import RealTimeMessageTest from "@/components/test/RealTimeMessageTest";
   ```

2. **Open Multiple Browser Tabs:**
   - Tab 1: User A in group chat
   - Tab 2: User B in group chat  
   - Tab 3: RealTimeMessageTest component

3. **Test Scenarios:**
   - User A sends message → User B should see it real-time
   - User B sends message → User A should see it real-time
   - Check console logs in all tabs

4. **Monitor Console Logs:**
   ```
   [ChatSocketHandler] New message received: {...}
   [ChatSocketHandler] Message analysis: {...}
   [ChatSocketHandler] Adding message to current chat: {...}
   [ChatArea] Messages updated for GROUP: {...}
   [ChatArea] Last messages: {...}
   ```

### 6. Expected Log Flow

**When message is received:**
```
1. [ChatSocketHandler] New message received: {messageId, groupId, senderId, content}
2. [ChatSocketHandler] Message analysis: {isFromCurrentChat: true, ...}
3. [ChatSocketHandler] Adding message to current chat: {messageId, content, senderId}
4. [ChatSocketHandler] Message added, new count: X
5. [ChatArea] Messages updated for GROUP: groupId, count: X
6. [ChatArea] Last messages: [{id, content, senderId}, ...]
```

**If message not showing:**
- Check which step fails
- Verify data at each step
- Fix the specific issue

### 7. Backend Verification

**Check backend logs for:**
- User joined group room successfully
- Message broadcast to all group members
- No errors in message gateway

**Verify database:**
- User is member of the group
- Group exists and is active
- Message was saved correctly

### 8. Next Steps

1. Run test component and monitor logs
2. Identify where the flow breaks
3. Fix the specific issue found
4. Test with multiple users
5. Remove debug logs after fixing
